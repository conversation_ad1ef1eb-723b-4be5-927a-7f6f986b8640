ace.define("ace/ext/linking",["require","exports","module","ace/editor","ace/config"], function(require, exports, module) {

var Editor = require("ace/editor").Editor;

require("../config").defineOptions(Editor.prototype, "editor", {
    enableLinking: {
        set: function(val) {
            if (val) {
                this.on("click", onClick);
                this.on("mousemove", onMouseMove);
            } else {
                this.off("click", onClick);
                this.off("mousemove", onMouseMove);
            }
        },
        value: false
    }
})

function onMouseMove(e) {
    var editor = e.editor;
    var ctrl = e.getAccelKey();

    if (ctrl) {
        var editor = e.editor;
        var docPos = e.getDocumentPosition();
        var session = editor.session;
        var token = session.getTokenAt(docPos.row, docPos.column);

        editor._emit("linkHover", {position: docPos, token: token});
    }
}

function onClick(e) {
    var ctrl = e.getAccel<PERSON>ey();
    var button = e.getButton();

    if (button == 0 && ctrl) {
        var editor = e.editor;
        var docPos = e.getDocumentPosition();
        var session = editor.session;
        var token = session.getTokenAt(docPos.row, docPos.column);

        editor._emit("linkClick", {position: docPos, token: token});
    }
}

});
;
                (function() {
                    ace.require(["ace/ext/linking"], function() {});
                })();
            