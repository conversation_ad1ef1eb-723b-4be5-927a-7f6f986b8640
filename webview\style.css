body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--vscode-editor-background);
    color: var(--vscode-editor-foreground);
    height: 100vh;
    display: flex;
    flex-direction: column;
}

.toolbar {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background-color: var(--vscode-titleBar-activeBackground);
    border-bottom: 1px solid var(--vscode-panel-border);
    gap: 12px;
    flex-shrink: 0;
}

.toolbar button {
    background-color: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    border: none;
    padding: 6px 12px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 13px;
    transition: background-color 0.2s;
}

.toolbar button:hover {
    background-color: var(--vscode-button-hoverBackground);
}

.toolbar button:active {
    background-color: var(--vscode-button-activeBackground);
}

#languageIndicator {
    font-size: 12px;
    color: var(--vscode-descriptionForeground);
    margin-left: auto;
}

#editor-container {
    flex: 1;
    position: relative;
    overflow: hidden;
}

#droplet-editor {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--vscode-editor-background);
}

/* Our minimal Droplet editor styles are included in the droplet-minimal.js file */

/* Loading state */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    font-size: 14px;
    color: var(--vscode-descriptionForeground);
}

.loading::after {
    content: '';
    width: 20px;
    height: 20px;
    margin-left: 10px;
    border: 2px solid var(--vscode-progressBar-background);
    border-top: 2px solid var(--vscode-progressBar-foreground);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error state */
.error {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    font-size: 14px;
    color: var(--vscode-errorForeground);
    text-align: center;
    padding: 20px;
}

/* Mode indicator */
.mode-indicator {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: var(--vscode-badge-background);
    color: var(--vscode-badge-foreground);
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 11px;
    z-index: 1000;
}
