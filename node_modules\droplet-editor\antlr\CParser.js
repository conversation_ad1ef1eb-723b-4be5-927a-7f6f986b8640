// Generated from C.g4 by ANTLR 4.5
// jshint ignore: start
var antlr4 = require('antlr4/index');
var CListener = require('./CListener').CListener;
var grammarFileName = "C.g4";

var serializedATN = ["\3\u0430\ud6d1\u8206\uad2d\u4417\uaef1\u8d80\uaadd",
    "\3s\u04e9\4\2\t\2\4\3\t\3\4\4\t\4\4\5\t\5\4\6\t\6\4\7\t\7\4\b\t\b\4",
    "\t\t\t\4\n\t\n\4\13\t\13\4\f\t\f\4\r\t\r\4\16\t\16\4\17\t\17\4\20\t",
    "\20\4\21\t\21\4\22\t\22\4\23\t\23\4\24\t\24\4\25\t\25\4\26\t\26\4\27",
    "\t\27\4\30\t\30\4\31\t\31\4\32\t\32\4\33\t\33\4\34\t\34\4\35\t\35\4",
    "\36\t\36\4\37\t\37\4 \t \4!\t!\4\"\t\"\4#\t#\4$\t$\4%\t%\4&\t&\4\'\t",
    "\'\4(\t(\4)\t)\4*\t*\4+\t+\4,\t,\4-\t-\4.\t.\4/\t/\4\60\t\60\4\61\t",
    "\61\4\62\t\62\4\63\t\63\4\64\t\64\4\65\t\65\4\66\t\66\4\67\t\67\48\t",
    "8\49\t9\4:\t:\4;\t;\4<\t<\4=\t=\4>\t>\4?\t?\4@\t@\4A\tA\4B\tB\4C\tC",
    "\4D\tD\4E\tE\4F\tF\4G\tG\4H\tH\4I\tI\4J\tJ\4K\tK\4L\tL\4M\tM\4N\tN\4",
    "O\tO\4P\tP\4Q\tQ\4R\tR\4S\tS\4T\tT\4U\tU\3\2\3\2\3\2\6\2\u00ae\n\2\r",
    "\2\16\2\u00af\3\2\3\2\3\2\3\2\3\2\3\2\5\2\u00b8\n\2\3\2\3\2\3\2\3\2",
    "\3\2\3\2\3\2\3\2\3\2\3\2\3\2\3\2\3\2\3\2\3\2\3\2\3\2\3\2\5\2\u00cc\n",
    "\2\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\4\3\4\3\4\3\4\3\4\3\4\7\4\u00db\n\4",
    "\f\4\16\4\u00de\13\4\3\5\3\5\3\5\3\5\3\5\3\5\3\5\5\5\u00e7\n\5\3\6\3",
    "\6\3\6\3\6\3\6\3\6\3\6\3\6\3\6\3\6\3\6\3\6\3\6\3\6\3\6\3\6\3\6\3\6\3",
    "\6\3\6\3\6\3\6\3\6\3\6\3\6\3\6\3\6\3\6\3\6\3\6\3\6\3\6\3\6\3\6\5\6\u010b",
    "\n\6\3\6\3\6\3\6\3\6\3\6\3\6\3\6\3\6\5\6\u0115\n\6\3\6\3\6\3\6\3\6\3",
    "\6\3\6\3\6\3\6\3\6\3\6\3\6\7\6\u0122\n\6\f\6\16\6\u0125\13\6\3\7\3\7",
    "\3\7\3\7\3\7\3\7\7\7\u012d\n\7\f\7\16\7\u0130\13\7\3\b\3\b\3\b\3\b\3",
    "\b\3\b\3\b\3\b\3\b\3\b\3\b\3\b\3\b\3\b\3\b\3\b\3\b\3\b\3\b\3\b\3\b\3",
    "\b\5\b\u0148\n\b\3\t\3\t\3\n\3\n\3\n\3\n\3\n\3\n\3\n\3\n\3\n\3\n\3\n",
    "\3\n\5\n\u0158\n\n\3\13\3\13\3\13\3\13\3\13\3\13\3\13\3\13\3\13\3\13",
    "\3\13\3\13\7\13\u0166\n\13\f\13\16\13\u0169\13\13\3\f\3\f\3\f\3\f\3",
    "\f\3\f\3\f\3\f\3\f\7\f\u0174\n\f\f\f\16\f\u0177\13\f\3\r\3\r\3\r\3\r",
    "\3\r\3\r\3\r\3\r\3\r\7\r\u0182\n\r\f\r\16\r\u0185\13\r\3\16\3\16\3\16",
    "\3\16\3\16\3\16\3\16\3\16\3\16\3\16\3\16\3\16\3\16\3\16\3\16\7\16\u0196",
    "\n\16\f\16\16\16\u0199\13\16\3\17\3\17\3\17\3\17\3\17\3\17\3\17\3\17",
    "\3\17\7\17\u01a4\n\17\f\17\16\17\u01a7\13\17\3\20\3\20\3\20\3\20\3\20",
    "\3\20\7\20\u01af\n\20\f\20\16\20\u01b2\13\20\3\21\3\21\3\21\3\21\3\21",
    "\3\21\7\21\u01ba\n\21\f\21\16\21\u01bd\13\21\3\22\3\22\3\22\3\22\3\22",
    "\3\22\7\22\u01c5\n\22\f\22\16\22\u01c8\13\22\3\23\3\23\3\23\3\23\3\23",
    "\3\23\7\23\u01d0\n\23\f\23\16\23\u01d3\13\23\3\24\3\24\3\24\3\24\3\24",
    "\3\24\7\24\u01db\n\24\f\24\16\24\u01de\13\24\3\25\3\25\3\25\3\25\3\25",
    "\3\25\5\25\u01e6\n\25\3\26\3\26\3\26\3\26\3\26\5\26\u01ed\n\26\3\27",
    "\3\27\3\30\3\30\3\30\3\30\3\30\3\30\7\30\u01f7\n\30\f\30\16\30\u01fa",
    "\13\30\3\31\3\31\3\32\3\32\5\32\u0200\n\32\3\32\3\32\3\32\5\32\u0205",
    "\n\32\3\33\6\33\u0208\n\33\r\33\16\33\u0209\3\34\6\34\u020d\n\34\r\34",
    "\16\34\u020e\3\35\3\35\3\35\3\35\3\35\5\35\u0216\n\35\3\36\3\36\3\36",
    "\3\36\3\36\3\36\7\36\u021e\n\36\f\36\16\36\u0221\13\36\3\37\3\37\3\37",
    "\3\37\3\37\5\37\u0228\n\37\3 \3 \3!\3!\3!\3!\3!\3!\3!\3!\3!\3!\3!\3",
    "!\3!\3!\5!\u023a\n!\3\"\3\"\5\"\u023e\n\"\3\"\3\"\3\"\3\"\3\"\3\"\3",
    "\"\5\"\u0247\n\"\3#\3#\3$\3$\3$\3$\3$\7$\u0250\n$\f$\16$\u0253\13$\3",
    "%\3%\5%\u0257\n%\3%\3%\3%\5%\u025c\n%\3&\3&\5&\u0260\n&\3&\3&\5&\u0264",
    "\n&\5&\u0266\n&\3\'\3\'\3\'\3\'\3\'\3\'\7\'\u026e\n\'\f\'\16\'\u0271",
    "\13\'\3(\3(\5(\u0275\n(\3(\3(\5(\u0279\n(\3)\3)\5)\u027d\n)\3)\3)\3",
    ")\3)\3)\3)\5)\u0285\n)\3)\3)\3)\3)\3)\3)\3)\5)\u028e\n)\3*\3*\3*\3*",
    "\3*\3*\7*\u0296\n*\f*\16*\u0299\13*\3+\3+\3+\3+\3+\5+\u02a0\n+\3,\3",
    ",\3-\3-\3-\3-\3-\3.\3.\3/\3/\3/\3/\3/\3/\5/\u02b1\n/\3\60\3\60\3\60",
    "\3\60\3\60\3\60\3\60\3\60\3\60\3\60\5\60\u02bd\n\60\3\61\5\61\u02c0",
    "\n\61\3\61\3\61\7\61\u02c4\n\61\f\61\16\61\u02c7\13\61\3\62\3\62\3\62",
    "\3\62\3\62\3\62\5\62\u02cf\n\62\3\62\3\62\3\62\5\62\u02d4\n\62\3\62",
    "\5\62\u02d7\n\62\3\62\3\62\3\62\3\62\3\62\5\62\u02de\n\62\3\62\3\62",
    "\3\62\3\62\3\62\3\62\3\62\3\62\3\62\3\62\3\62\3\62\3\62\5\62\u02ed\n",
    "\62\3\62\3\62\3\62\3\62\3\62\3\62\3\62\3\62\3\62\3\62\5\62\u02f9\n\62",
    "\3\62\7\62\u02fc\n\62\f\62\16\62\u02ff\13\62\3\63\3\63\3\63\6\63\u0304",
    "\n\63\r\63\16\63\u0305\3\63\3\63\5\63\u030a\n\63\3\64\3\64\3\64\3\64",
    "\3\64\3\64\3\64\3\65\3\65\3\65\7\65\u0316\n\65\f\65\16\65\u0319\13\65",
    "\3\65\5\65\u031c\n\65\3\66\3\66\3\66\5\66\u0321\n\66\3\66\5\66\u0324",
    "\n\66\3\66\5\66\u0327\n\66\3\67\3\67\3\67\3\67\3\67\7\67\u032e\n\67",
    "\f\67\16\67\u0331\13\67\38\38\58\u0335\n8\38\38\58\u0339\n8\38\38\3",
    "8\58\u033e\n8\38\38\58\u0342\n8\38\58\u0345\n8\39\39\39\39\39\79\u034c",
    "\n9\f9\169\u034f\139\3:\3:\3:\3:\3:\5:\u0356\n:\3;\3;\3;\3;\3;\3;\7",
    ";\u035e\n;\f;\16;\u0361\13;\3<\3<\3<\3<\3<\5<\u0368\n<\5<\u036a\n<\3",
    "=\3=\3=\3=\3=\3=\7=\u0372\n=\f=\16=\u0375\13=\3>\3>\5>\u0379\n>\3?\3",
    "?\5?\u037d\n?\3?\3?\7?\u0381\n?\f?\16?\u0384\13?\5?\u0386\n?\3@\3@\3",
    "@\3@\3@\7@\u038d\n@\f@\16@\u0390\13@\3@\3@\5@\u0394\n@\3@\5@\u0397\n",
    "@\3@\3@\3@\3@\5@\u039d\n@\3@\3@\3@\3@\3@\3@\3@\3@\3@\3@\3@\3@\3@\3@",
    "\5@\u03ad\n@\3@\3@\7@\u03b1\n@\f@\16@\u03b4\13@\5@\u03b6\n@\3@\3@\3",
    "@\5@\u03bb\n@\3@\5@\u03be\n@\3@\3@\3@\3@\3@\5@\u03c5\n@\3@\3@\3@\3@",
    "\3@\3@\3@\3@\3@\3@\3@\3@\3@\3@\3@\3@\3@\5@\u03d8\n@\3@\3@\7@\u03dc\n",
    "@\f@\16@\u03df\13@\7@\u03e1\n@\f@\16@\u03e4\13@\3A\3A\3B\3B\3B\3B\3",
    "B\3B\3B\3B\3B\3B\5B\u03f2\nB\3C\3C\5C\u03f6\nC\3C\3C\3C\3C\3C\5C\u03fd",
    "\nC\3C\7C\u0400\nC\fC\16C\u0403\13C\3D\3D\3D\3E\3E\3E\3E\3E\7E\u040d",
    "\nE\fE\16E\u0410\13E\3F\3F\3F\3F\3F\3F\5F\u0418\nF\3G\3G\3G\3G\3G\6",
    "G\u041f\nG\rG\16G\u0420\3G\3G\3G\3H\3H\3H\3H\3H\3H\3H\3H\3H\3H\3H\3",
    "H\7H\u0432\nH\fH\16H\u0435\13H\5H\u0437\nH\3H\3H\3H\3H\7H\u043d\nH\f",
    "H\16H\u0440\13H\5H\u0442\nH\7H\u0444\nH\fH\16H\u0447\13H\3H\3H\5H\u044b",
    "\nH\3I\3I\3I\3I\3I\3I\3I\3I\3I\3I\3I\5I\u0458\nI\3J\3J\5J\u045c\nJ\3",
    "J\3J\3K\3K\3K\3K\3K\7K\u0465\nK\fK\16K\u0468\13K\3L\3L\5L\u046c\nL\3",
    "M\5M\u046f\nM\3M\3M\3N\3N\3N\3N\3N\3N\3N\5N\u047a\nN\3N\3N\3N\3N\3N",
    "\3N\5N\u0482\nN\3O\3O\3O\3O\3O\3O\3O\3O\3O\3O\3O\3O\3O\3O\3O\3O\3O\5",
    "O\u0495\nO\3O\3O\5O\u0499\nO\3O\3O\5O\u049d\nO\3O\3O\3O\3O\3O\3O\5O",
    "\u04a5\nO\3O\3O\5O\u04a9\nO\3O\3O\3O\5O\u04ae\nO\3P\3P\3P\3P\3P\3P\3",
    "P\3P\3P\5P\u04b9\nP\3P\3P\3P\3P\3P\5P\u04c0\nP\3Q\5Q\u04c3\nQ\3Q\3Q",
    "\3R\3R\3R\3R\3R\7R\u04cc\nR\fR\16R\u04cf\13R\3S\3S\3S\5S\u04d4\nS\3",
    "T\5T\u04d7\nT\3T\3T\5T\u04db\nT\3T\3T\3U\3U\3U\3U\3U\7U\u04e4\nU\fU",
    "\16U\u04e7\13U\3U\2\36\6\n\f\24\26\30\32\34\36 \"$&.:FLRbptx~\u0084",
    "\u0088\u0094\u00a2\u00a8V\2\4\6\b\n\f\16\20\22\24\26\30\32\34\36 \"",
    "$&(*,.\60\62\64\668:<>@BDFHJLNPRTVXZ\\^`bdfhjlnprtvxz|~\u0080\u0082",
    "\u0084\u0086\u0088\u008a\u008c\u008e\u0090\u0092\u0094\u0096\u0098\u009a",
    "\u009c\u009e\u00a0\u00a2\u00a4\u00a6\u00a8\2\16\7\2IIKKMMPPUV\3\2[e",
    "\b\2\21\21\34\34$$**--<<\n\2\6\b\24\24\31\31\35\35\"#\'(/\60\66\67\3",
    "\2\6\b\4\2++..\6\2\25\25%%\61\61\65\65\5\2\n\13!!::\4\2=>ZZ\3\2=>\4",
    "\2\r\r\17\17\4\2\20\20\61\61\u0559\2\u00cb\3\2\2\2\4\u00cd\3\2\2\2\6",
    "\u00d4\3\2\2\2\b\u00e6\3\2\2\2\n\u010a\3\2\2\2\f\u0126\3\2\2\2\16\u0147",
    "\3\2\2\2\20\u0149\3\2\2\2\22\u0157\3\2\2\2\24\u0159\3\2\2\2\26\u016a",
    "\3\2\2\2\30\u0178\3\2\2\2\32\u0186\3\2\2\2\34\u019a\3\2\2\2\36\u01a8",
    "\3\2\2\2 \u01b3\3\2\2\2\"\u01be\3\2\2\2$\u01c9\3\2\2\2&\u01d4\3\2\2",
    "\2(\u01df\3\2\2\2*\u01ec\3\2\2\2,\u01ee\3\2\2\2.\u01f0\3\2\2\2\60\u01fb",
    "\3\2\2\2\62\u0204\3\2\2\2\64\u0207\3\2\2\2\66\u020c\3\2\2\28\u0215\3",
    "\2\2\2:\u0217\3\2\2\2<\u0227\3\2\2\2>\u0229\3\2\2\2@\u0239\3\2\2\2B",
    "\u0246\3\2\2\2D\u0248\3\2\2\2F\u024a\3\2\2\2H\u025b\3\2\2\2J\u0265\3",
    "\2\2\2L\u0267\3\2\2\2N\u0278\3\2\2\2P\u028d\3\2\2\2R\u028f\3\2\2\2T",
    "\u029f\3\2\2\2V\u02a1\3\2\2\2X\u02a3\3\2\2\2Z\u02a8\3\2\2\2\\\u02b0",
    "\3\2\2\2^\u02bc\3\2\2\2`\u02bf\3\2\2\2b\u02ce\3\2\2\2d\u0309\3\2\2\2",
    "f\u030b\3\2\2\2h\u031b\3\2\2\2j\u0326\3\2\2\2l\u032f\3\2\2\2n\u0344",
    "\3\2\2\2p\u0346\3\2\2\2r\u0355\3\2\2\2t\u0357\3\2\2\2v\u0369\3\2\2\2",
    "x\u036b\3\2\2\2z\u0376\3\2\2\2|\u0385\3\2\2\2~\u03b5\3\2\2\2\u0080\u03e5",
    "\3\2\2\2\u0082\u03f1\3\2\2\2\u0084\u03f3\3\2\2\2\u0086\u0404\3\2\2\2",
    "\u0088\u0407\3\2\2\2\u008a\u0417\3\2\2\2\u008c\u0419\3\2\2\2\u008e\u044a",
    "\3\2\2\2\u0090\u0457\3\2\2\2\u0092\u0459\3\2\2\2\u0094\u045f\3\2\2\2",
    "\u0096\u046b\3\2\2\2\u0098\u046e\3\2\2\2\u009a\u0481\3\2\2\2\u009c\u04ad",
    "\3\2\2\2\u009e\u04bf\3\2\2\2\u00a0\u04c2\3\2\2\2\u00a2\u04c6\3\2\2\2",
    "\u00a4\u04d3\3\2\2\2\u00a6\u04d6\3\2\2\2\u00a8\u04de\3\2\2\2\u00aa\u00cc",
    "\7k\2\2\u00ab\u00cc\7l\2\2\u00ac\u00ae\7m\2\2\u00ad\u00ac\3\2\2\2\u00ae",
    "\u00af\3\2\2\2\u00af\u00ad\3\2\2\2\u00af\u00b0\3\2\2\2\u00b0\u00cc\3",
    "\2\2\2\u00b1\u00b2\7=\2\2\u00b2\u00b3\5.\30\2\u00b3\u00b4\7>\2\2\u00b4",
    "\u00cc\3\2\2\2\u00b5\u00cc\5\4\3\2\u00b6\u00b8\7\3\2\2\u00b7\u00b6\3",
    "\2\2\2\u00b7\u00b8\3\2\2\2\u00b8\u00b9\3\2\2\2\u00b9\u00ba\7=\2\2\u00ba",
    "\u00bb\5\u0092J\2\u00bb\u00bc\7>\2\2\u00bc\u00cc\3\2\2\2\u00bd\u00be",
    "\7\4\2\2\u00be\u00bf\7=\2\2\u00bf\u00c0\5\16\b\2\u00c0\u00c1\7Z\2\2",
    "\u00c1\u00c2\5z>\2\u00c2\u00c3\7>\2\2\u00c3\u00cc\3\2\2\2\u00c4\u00c5",
    "\7\5\2\2\u00c5\u00c6\7=\2\2\u00c6\u00c7\5z>\2\u00c7\u00c8\7Z\2\2\u00c8",
    "\u00c9\5\16\b\2\u00c9\u00ca\7>\2\2\u00ca\u00cc\3\2\2\2\u00cb\u00aa\3",
    "\2\2\2\u00cb\u00ab\3\2\2\2\u00cb\u00ad\3\2\2\2\u00cb\u00b1\3\2\2\2\u00cb",
    "\u00b5\3\2\2\2\u00cb\u00b7\3\2\2\2\u00cb\u00bd\3\2\2\2\u00cb\u00c4\3",
    "\2\2\2\u00cc\3\3\2\2\2\u00cd\u00ce\78\2\2\u00ce\u00cf\7=\2\2\u00cf\u00d0",
    "\5*\26\2\u00d0\u00d1\7Z\2\2\u00d1\u00d2\5\6\4\2\u00d2\u00d3\7>\2\2\u00d3",
    "\5\3\2\2\2\u00d4\u00d5\b\4\1\2\u00d5\u00d6\5\b\5\2\u00d6\u00dc\3\2\2",
    "\2\u00d7\u00d8\f\3\2\2\u00d8\u00d9\7Z\2\2\u00d9\u00db\5\b\5\2\u00da",
    "\u00d7\3\2\2\2\u00db\u00de\3\2\2\2\u00dc\u00da\3\2\2\2\u00dc\u00dd\3",
    "\2\2\2\u00dd\7\3\2\2\2\u00de\u00dc\3\2\2\2\u00df\u00e0\5z>\2\u00e0\u00e1",
    "\7X\2\2\u00e1\u00e2\5*\26\2\u00e2\u00e7\3\2\2\2\u00e3\u00e4\7\27\2\2",
    "\u00e4\u00e5\7X\2\2\u00e5\u00e7\5*\26\2\u00e6\u00df\3\2\2\2\u00e6\u00e3",
    "\3\2\2\2\u00e7\t\3\2\2\2\u00e8\u00e9\b\6\1\2\u00e9\u010b\5\2\2\2\u00ea",
    "\u00eb\7=\2\2\u00eb\u00ec\5z>\2\u00ec\u00ed\7>\2\2\u00ed\u00ee\7A\2",
    "\2\u00ee\u00ef\5\u0084C\2\u00ef\u00f0\7B\2\2\u00f0\u010b\3\2\2\2\u00f1",
    "\u00f2\7=\2\2\u00f2\u00f3\5z>\2\u00f3\u00f4\7>\2\2\u00f4\u00f5\7A\2",
    "\2\u00f5\u00f6\5\u0084C\2\u00f6\u00f7\7Z\2\2\u00f7\u00f8\7B\2\2\u00f8",
    "\u010b\3\2\2\2\u00f9\u00fa\7\3\2\2\u00fa\u00fb\7=\2\2\u00fb\u00fc\5",
    "z>\2\u00fc\u00fd\7>\2\2\u00fd\u00fe\7A\2\2\u00fe\u00ff\5\u0084C\2\u00ff",
    "\u0100\7B\2\2\u0100\u010b\3\2\2\2\u0101\u0102\7\3\2\2\u0102\u0103\7",
    "=\2\2\u0103\u0104\5z>\2\u0104\u0105\7>\2\2\u0105\u0106\7A\2\2\u0106",
    "\u0107\5\u0084C\2\u0107\u0108\7Z\2\2\u0108\u0109\7B\2\2\u0109\u010b",
    "\3\2\2\2\u010a\u00e8\3\2\2\2\u010a\u00ea\3\2\2\2\u010a\u00f1\3\2\2\2",
    "\u010a\u00f9\3\2\2\2\u010a\u0101\3\2\2\2\u010b\u0123\3\2\2\2\u010c\u010d",
    "\f\f\2\2\u010d\u010e\7?\2\2\u010e\u010f\5.\30\2\u010f\u0110\7@\2\2\u0110",
    "\u0122\3\2\2\2\u0111\u0112\f\13\2\2\u0112\u0114\7=\2\2\u0113\u0115\5",
    "\f\7\2\u0114\u0113\3\2\2\2\u0114\u0115\3\2\2\2\u0115\u0116\3\2\2\2\u0116",
    "\u0122\7>\2\2\u0117\u0118\f\n\2\2\u0118\u0119\7i\2\2\u0119\u0122\7k",
    "\2\2\u011a\u011b\f\t\2\2\u011b\u011c\7h\2\2\u011c\u0122\7k\2\2\u011d",
    "\u011e\f\b\2\2\u011e\u0122\7J\2\2\u011f\u0120\f\7\2\2\u0120\u0122\7",
    "L\2\2\u0121\u010c\3\2\2\2\u0121\u0111\3\2\2\2\u0121\u0117\3\2\2\2\u0121",
    "\u011a\3\2\2\2\u0121\u011d\3\2\2\2\u0121\u011f\3\2\2\2\u0122\u0125\3",
    "\2\2\2\u0123\u0121\3\2\2\2\u0123\u0124\3\2\2\2\u0124\13\3\2\2\2\u0125",
    "\u0123\3\2\2\2\u0126\u0127\b\7\1\2\u0127\u0128\5*\26\2\u0128\u012e\3",
    "\2\2\2\u0129\u012a\f\3\2\2\u012a\u012b\7Z\2\2\u012b\u012d\5*\26\2\u012c",
    "\u0129\3\2\2\2\u012d\u0130\3\2\2\2\u012e\u012c\3\2\2\2\u012e\u012f\3",
    "\2\2\2\u012f\r\3\2\2\2\u0130\u012e\3\2\2\2\u0131\u0148\5\n\6\2\u0132",
    "\u0133\7J\2\2\u0133\u0148\5\16\b\2\u0134\u0135\7L\2\2\u0135\u0148\5",
    "\16\b\2\u0136\u0137\5\20\t\2\u0137\u0138\5\22\n\2\u0138\u0148\3\2\2",
    "\2\u0139\u013a\7)\2\2\u013a\u0148\5\16\b\2\u013b\u013c\7)\2\2\u013c",
    "\u013d\7=\2\2\u013d\u013e\5z>\2\u013e\u013f\7>\2\2\u013f\u0148\3\2\2",
    "\2\u0140\u0141\7\64\2\2\u0141\u0142\7=\2\2\u0142\u0143\5z>\2\u0143\u0144",
    "\7>\2\2\u0144\u0148\3\2\2\2\u0145\u0146\7R\2\2\u0146\u0148\7k\2\2\u0147",
    "\u0131\3\2\2\2\u0147\u0132\3\2\2\2\u0147\u0134\3\2\2\2\u0147\u0136\3",
    "\2\2\2\u0147\u0139\3\2\2\2\u0147\u013b\3\2\2\2\u0147\u0140\3\2\2\2\u0147",
    "\u0145\3\2\2\2\u0148\17\3\2\2\2\u0149\u014a\t\2\2\2\u014a\21\3\2\2\2",
    "\u014b\u0158\5\16\b\2\u014c\u014d\7=\2\2\u014d\u014e\5z>\2\u014e\u014f",
    "\7>\2\2\u014f\u0150\5\22\n\2\u0150\u0158\3\2\2\2\u0151\u0152\7\3\2\2",
    "\u0152\u0153\7=\2\2\u0153\u0154\5z>\2\u0154\u0155\7>\2\2\u0155\u0156",
    "\5\22\n\2\u0156\u0158\3\2\2\2\u0157\u014b\3\2\2\2\u0157\u014c\3\2\2",
    "\2\u0157\u0151\3\2\2\2\u0158\23\3\2\2\2\u0159\u015a\b\13\1\2\u015a\u015b",
    "\5\22\n\2\u015b\u0167\3\2\2\2\u015c\u015d\f\5\2\2\u015d\u015e\7M\2\2",
    "\u015e\u0166\5\22\n\2\u015f\u0160\f\4\2\2\u0160\u0161\7N\2\2\u0161\u0166",
    "\5\22\n\2\u0162\u0163\f\3\2\2\u0163\u0164\7O\2\2\u0164\u0166\5\22\n",
    "\2\u0165\u015c\3\2\2\2\u0165\u015f\3\2\2\2\u0165\u0162\3\2\2\2\u0166",
    "\u0169\3\2\2\2\u0167\u0165\3\2\2\2\u0167\u0168\3\2\2\2\u0168\25\3\2",
    "\2\2\u0169\u0167\3\2\2\2\u016a\u016b\b\f\1\2\u016b\u016c\5\24\13\2\u016c",
    "\u0175\3\2\2\2\u016d\u016e\f\4\2\2\u016e\u016f\7I\2\2\u016f\u0174\5",
    "\24\13\2\u0170\u0171\f\3\2\2\u0171\u0172\7K\2\2\u0172\u0174\5\24\13",
    "\2\u0173\u016d\3\2\2\2\u0173\u0170\3\2\2\2\u0174\u0177\3\2\2\2\u0175",
    "\u0173\3\2\2\2\u0175\u0176\3\2\2\2\u0176\27\3\2\2\2\u0177\u0175\3\2",
    "\2\2\u0178\u0179\b\r\1\2\u0179\u017a\5\26\f\2\u017a\u0183\3\2\2\2\u017b",
    "\u017c\f\4\2\2\u017c\u017d\7G\2\2\u017d\u0182\5\26\f\2\u017e\u017f\f",
    "\3\2\2\u017f\u0180\7H\2\2\u0180\u0182\5\26\f\2\u0181\u017b\3\2\2\2\u0181",
    "\u017e\3\2\2\2\u0182\u0185\3\2\2\2\u0183\u0181\3\2\2\2\u0183\u0184\3",
    "\2\2\2\u0184\31\3\2\2\2\u0185\u0183\3\2\2\2\u0186\u0187\b\16\1\2\u0187",
    "\u0188\5\30\r\2\u0188\u0197\3\2\2\2\u0189\u018a\f\6\2\2\u018a\u018b",
    "\7C\2\2\u018b\u0196\5\30\r\2\u018c\u018d\f\5\2\2\u018d\u018e\7E\2\2",
    "\u018e\u0196\5\30\r\2\u018f\u0190\f\4\2\2\u0190\u0191\7D\2\2\u0191\u0196",
    "\5\30\r\2\u0192\u0193\f\3\2\2\u0193\u0194\7F\2\2\u0194\u0196\5\30\r",
    "\2\u0195\u0189\3\2\2\2\u0195\u018c\3\2\2\2\u0195\u018f\3\2\2\2\u0195",
    "\u0192\3\2\2\2\u0196\u0199\3\2\2\2\u0197\u0195\3\2\2\2\u0197\u0198\3",
    "\2\2\2\u0198\33\3\2\2\2\u0199\u0197\3\2\2\2\u019a\u019b\b\17\1\2\u019b",
    "\u019c\5\32\16\2\u019c\u01a5\3\2\2\2\u019d\u019e\f\4\2\2\u019e\u019f",
    "\7f\2\2\u019f\u01a4\5\32\16\2\u01a0\u01a1\f\3\2\2\u01a1\u01a2\7g\2\2",
    "\u01a2\u01a4\5\32\16\2\u01a3\u019d\3\2\2\2\u01a3\u01a0\3\2\2\2\u01a4",
    "\u01a7\3\2\2\2\u01a5\u01a3\3\2\2\2\u01a5\u01a6\3\2\2\2\u01a6\35\3\2",
    "\2\2\u01a7\u01a5\3\2\2\2\u01a8\u01a9\b\20\1\2\u01a9\u01aa\5\34\17\2",
    "\u01aa\u01b0\3\2\2\2\u01ab\u01ac\f\3\2\2\u01ac\u01ad\7P\2\2\u01ad\u01af",
    "\5\34\17\2\u01ae\u01ab\3\2\2\2\u01af\u01b2\3\2\2\2\u01b0\u01ae\3\2\2",
    "\2\u01b0\u01b1\3\2\2\2\u01b1\37\3\2\2\2\u01b2\u01b0\3\2\2\2\u01b3\u01b4",
    "\b\21\1\2\u01b4\u01b5\5\36\20\2\u01b5\u01bb\3\2\2\2\u01b6\u01b7\f\3",
    "\2\2\u01b7\u01b8\7T\2\2\u01b8\u01ba\5\36\20\2\u01b9\u01b6\3\2\2\2\u01ba",
    "\u01bd\3\2\2\2\u01bb\u01b9\3\2\2\2\u01bb\u01bc\3\2\2\2\u01bc!\3\2\2",
    "\2\u01bd\u01bb\3\2\2\2\u01be\u01bf\b\22\1\2\u01bf\u01c0\5 \21\2\u01c0",
    "\u01c6\3\2\2\2\u01c1\u01c2\f\3\2\2\u01c2\u01c3\7Q\2\2\u01c3\u01c5\5",
    " \21\2\u01c4\u01c1\3\2\2\2\u01c5\u01c8\3\2\2\2\u01c6\u01c4\3\2\2\2\u01c6",
    "\u01c7\3\2\2\2\u01c7#\3\2\2\2\u01c8\u01c6\3\2\2\2\u01c9\u01ca\b\23\1",
    "\2\u01ca\u01cb\5\"\22\2\u01cb\u01d1\3\2\2\2\u01cc\u01cd\f\3\2\2\u01cd",
    "\u01ce\7R\2\2\u01ce\u01d0\5\"\22\2\u01cf\u01cc\3\2\2\2\u01d0\u01d3\3",
    "\2\2\2\u01d1\u01cf\3\2\2\2\u01d1\u01d2\3\2\2\2\u01d2%\3\2\2\2\u01d3",
    "\u01d1\3\2\2\2\u01d4\u01d5\b\24\1\2\u01d5\u01d6\5$\23\2\u01d6\u01dc",
    "\3\2\2\2\u01d7\u01d8\f\3\2\2\u01d8\u01d9\7S\2\2\u01d9\u01db\5$\23\2",
    "\u01da\u01d7\3\2\2\2\u01db\u01de\3\2\2\2\u01dc\u01da\3\2\2\2\u01dc\u01dd",
    "\3\2\2\2\u01dd\'\3\2\2\2\u01de\u01dc\3\2\2\2\u01df\u01e5\5&\24\2\u01e0",
    "\u01e1\7W\2\2\u01e1\u01e2\5.\30\2\u01e2\u01e3\7X\2\2\u01e3\u01e4\5(",
    "\25\2\u01e4\u01e6\3\2\2\2\u01e5\u01e0\3\2\2\2\u01e5\u01e6\3\2\2\2\u01e6",
    ")\3\2\2\2\u01e7\u01ed\5(\25\2\u01e8\u01e9\5\16\b\2\u01e9\u01ea\5,\27",
    "\2\u01ea\u01eb\5*\26\2\u01eb\u01ed\3\2\2\2\u01ec\u01e7\3\2\2\2\u01ec",
    "\u01e8\3\2\2\2\u01ed+\3\2\2\2\u01ee\u01ef\t\3\2\2\u01ef-\3\2\2\2\u01f0",
    "\u01f1\b\30\1\2\u01f1\u01f2\5*\26\2\u01f2\u01f8\3\2\2\2\u01f3\u01f4",
    "\f\3\2\2\u01f4\u01f5\7Z\2\2\u01f5\u01f7\5*\26\2\u01f6\u01f3\3\2\2\2",
    "\u01f7\u01fa\3\2\2\2\u01f8\u01f6\3\2\2\2\u01f8\u01f9\3\2\2\2\u01f9/",
    "\3\2\2\2\u01fa\u01f8\3\2\2\2\u01fb\u01fc\5(\25\2\u01fc\61\3\2\2\2\u01fd",
    "\u01ff\5\64\33\2\u01fe\u0200\5:\36\2\u01ff\u01fe\3\2\2\2\u01ff\u0200",
    "\3\2\2\2\u0200\u0201\3\2\2\2\u0201\u0202\7Y\2\2\u0202\u0205\3\2\2\2",
    "\u0203\u0205\5\u008cG\2\u0204\u01fd\3\2\2\2\u0204\u0203\3\2\2\2\u0205",
    "\63\3\2\2\2\u0206\u0208\58\35\2\u0207\u0206\3\2\2\2\u0208\u0209\3\2",
    "\2\2\u0209\u0207\3\2\2\2\u0209\u020a\3\2\2\2\u020a\65\3\2\2\2\u020b",
    "\u020d\58\35\2\u020c\u020b\3\2\2\2\u020d\u020e\3\2\2\2\u020e\u020c\3",
    "\2\2\2\u020e\u020f\3\2\2\2\u020f\67\3\2\2\2\u0210\u0216\5> \2\u0211",
    "\u0216\5@!\2\u0212\u0216\5Z.\2\u0213\u0216\5\\/\2\u0214\u0216\5^\60",
    "\2\u0215\u0210\3\2\2\2\u0215\u0211\3\2\2\2\u0215\u0212\3\2\2\2\u0215",
    "\u0213\3\2\2\2\u0215\u0214\3\2\2\2\u02169\3\2\2\2\u0217\u0218\b\36\1",
    "\2\u0218\u0219\5<\37\2\u0219\u021f\3\2\2\2\u021a\u021b\f\3\2\2\u021b",
    "\u021c\7Z\2\2\u021c\u021e\5<\37\2\u021d\u021a\3\2\2\2\u021e\u0221\3",
    "\2\2\2\u021f\u021d\3\2\2\2\u021f\u0220\3\2\2\2\u0220;\3\2\2\2\u0221",
    "\u021f\3\2\2\2\u0222\u0228\5`\61\2\u0223\u0224\5`\61\2\u0224\u0225\7",
    "[\2\2\u0225\u0226\5\u0082B\2\u0226\u0228\3\2\2\2\u0227\u0222\3\2\2\2",
    "\u0227\u0223\3\2\2\2\u0228=\3\2\2\2\u0229\u022a\t\4\2\2\u022a?\3\2\2",
    "\2\u022b\u023a\t\5\2\2\u022c\u022d\7\3\2\2\u022d\u022e\7=\2\2\u022e",
    "\u022f\t\6\2\2\u022f\u023a\7>\2\2\u0230\u023a\5X-\2\u0231\u023a\5B\"",
    "\2\u0232\u023a\5P)\2\u0233\u023a\5\u0080A\2\u0234\u0235\7\t\2\2\u0235",
    "\u0236\7=\2\2\u0236\u0237\5\60\31\2\u0237\u0238\7>\2\2\u0238\u023a\3",
    "\2\2\2\u0239\u022b\3\2\2\2\u0239\u022c\3\2\2\2\u0239\u0230\3\2\2\2\u0239",
    "\u0231\3\2\2\2\u0239\u0232\3\2\2\2\u0239\u0233\3\2\2\2\u0239\u0234\3",
    "\2\2\2\u023aA\3\2\2\2\u023b\u023d\5D#\2\u023c\u023e\7k\2\2\u023d\u023c",
    "\3\2\2\2\u023d\u023e\3\2\2\2\u023e\u023f\3\2\2\2\u023f\u0240\7A\2\2",
    "\u0240\u0241\5F$\2\u0241\u0242\7B\2\2\u0242\u0247\3\2\2\2\u0243\u0244",
    "\5D#\2\u0244\u0245\7k\2\2\u0245\u0247\3\2\2\2\u0246\u023b\3\2\2\2\u0246",
    "\u0243\3\2\2\2\u0247C\3\2\2\2\u0248\u0249\t\7\2\2\u0249E\3\2\2\2\u024a",
    "\u024b\b$\1\2\u024b\u024c\5H%\2\u024c\u0251\3\2\2\2\u024d\u024e\f\3",
    "\2\2\u024e\u0250\5H%\2\u024f\u024d\3\2\2\2\u0250\u0253\3\2\2\2\u0251",
    "\u024f\3\2\2\2\u0251\u0252\3\2\2\2\u0252G\3\2\2\2\u0253\u0251\3\2\2",
    "\2\u0254\u0256\5J&\2\u0255\u0257\5L\'\2\u0256\u0255\3\2\2\2\u0256\u0257",
    "\3\2\2\2\u0257\u0258\3\2\2\2\u0258\u0259\7Y\2\2\u0259\u025c\3\2\2\2",
    "\u025a\u025c\5\u008cG\2\u025b\u0254\3\2\2\2\u025b\u025a\3\2\2\2\u025c",
    "I\3\2\2\2\u025d\u025f\5@!\2\u025e\u0260\5J&\2\u025f\u025e\3\2\2\2\u025f",
    "\u0260\3\2\2\2\u0260\u0266\3\2\2\2\u0261\u0263\5Z.\2\u0262\u0264\5J",
    "&\2\u0263\u0262\3\2\2\2\u0263\u0264\3\2\2\2\u0264\u0266\3\2\2\2\u0265",
    "\u025d\3\2\2\2\u0265\u0261\3\2\2\2\u0266K\3\2\2\2\u0267\u0268\b\'\1",
    "\2\u0268\u0269\5N(\2\u0269\u026f\3\2\2\2\u026a\u026b\f\3\2\2\u026b\u026c",
    "\7Z\2\2\u026c\u026e\5N(\2\u026d\u026a\3\2\2\2\u026e\u0271\3\2\2\2\u026f",
    "\u026d\3\2\2\2\u026f\u0270\3\2\2\2\u0270M\3\2\2\2\u0271\u026f\3\2\2",
    "\2\u0272\u0279\5`\61\2\u0273\u0275\5`\61\2\u0274\u0273\3\2\2\2\u0274",
    "\u0275\3\2\2\2\u0275\u0276\3\2\2\2\u0276\u0277\7X\2\2\u0277\u0279\5",
    "\60\31\2\u0278\u0272\3\2\2\2\u0278\u0274\3\2\2\2\u0279O\3\2\2\2\u027a",
    "\u027c\7\33\2\2\u027b\u027d\7k\2\2\u027c\u027b\3\2\2\2\u027c\u027d\3",
    "\2\2\2\u027d\u027e\3\2\2\2\u027e\u027f\7A\2\2\u027f\u0280\5R*\2\u0280",
    "\u0281\7B\2\2\u0281\u028e\3\2\2\2\u0282\u0284\7\33\2\2\u0283\u0285\7",
    "k\2\2\u0284\u0283\3\2\2\2\u0284\u0285\3\2\2\2\u0285\u0286\3\2\2\2\u0286",
    "\u0287\7A\2\2\u0287\u0288\5R*\2\u0288\u0289\7Z\2\2\u0289\u028a\7B\2",
    "\2\u028a\u028e\3\2\2\2\u028b\u028c\7\33\2\2\u028c\u028e\7k\2\2\u028d",
    "\u027a\3\2\2\2\u028d\u0282\3\2\2\2\u028d\u028b\3\2\2\2\u028eQ\3\2\2",
    "\2\u028f\u0290\b*\1\2\u0290\u0291\5T+\2\u0291\u0297\3\2\2\2\u0292\u0293",
    "\f\3\2\2\u0293\u0294\7Z\2\2\u0294\u0296\5T+\2\u0295\u0292\3\2\2\2\u0296",
    "\u0299\3\2\2\2\u0297\u0295\3\2\2\2\u0297\u0298\3\2\2\2\u0298S\3\2\2",
    "\2\u0299\u0297\3\2\2\2\u029a\u02a0\5V,\2\u029b\u029c\5V,\2\u029c\u029d",
    "\7[\2\2\u029d\u029e\5\60\31\2\u029e\u02a0\3\2\2\2\u029f\u029a\3\2\2",
    "\2\u029f\u029b\3\2\2\2\u02a0U\3\2\2\2\u02a1\u02a2\7k\2\2\u02a2W\3\2",
    "\2\2\u02a3\u02a4\7\65\2\2\u02a4\u02a5\7=\2\2\u02a5\u02a6\5z>\2\u02a6",
    "\u02a7\7>\2\2\u02a7Y\3\2\2\2\u02a8\u02a9\t\b\2\2\u02a9[\3\2\2\2\u02aa",
    "\u02b1\t\t\2\2\u02ab\u02b1\5f\64\2\u02ac\u02ad\7\f\2\2\u02ad\u02ae\7",
    "=\2\2\u02ae\u02af\7k\2\2\u02af\u02b1\7>\2\2\u02b0\u02aa\3\2\2\2\u02b0",
    "\u02ab\3\2\2\2\u02b0\u02ac\3\2\2\2\u02b1]\3\2\2\2\u02b2\u02b3\7\63\2",
    "\2\u02b3\u02b4\7=\2\2\u02b4\u02b5\5z>\2\u02b5\u02b6\7>\2\2\u02b6\u02bd",
    "\3\2\2\2\u02b7\u02b8\7\63\2\2\u02b8\u02b9\7=\2\2\u02b9\u02ba\5\60\31",
    "\2\u02ba\u02bb\7>\2\2\u02bb\u02bd\3\2\2\2\u02bc\u02b2\3\2\2\2\u02bc",
    "\u02b7\3\2\2\2\u02bd_\3\2\2\2\u02be\u02c0\5n8\2\u02bf\u02be\3\2\2\2",
    "\u02bf\u02c0\3\2\2\2\u02c0\u02c1\3\2\2\2\u02c1\u02c5\5b\62\2\u02c2\u02c4",
    "\5d\63\2\u02c3\u02c2\3\2\2\2\u02c4\u02c7\3\2\2\2\u02c5\u02c3\3\2\2\2",
    "\u02c5\u02c6\3\2\2\2\u02c6a\3\2\2\2\u02c7\u02c5\3\2\2\2\u02c8\u02c9",
    "\b\62\1\2\u02c9\u02cf\7k\2\2\u02ca\u02cb\7=\2\2\u02cb\u02cc\5`\61\2",
    "\u02cc\u02cd\7>\2\2\u02cd\u02cf\3\2\2\2\u02ce\u02c8\3\2\2\2\u02ce\u02ca",
    "\3\2\2\2\u02cf\u02fd\3\2\2\2\u02d0\u02d1\f\b\2\2\u02d1\u02d3\7?\2\2",
    "\u02d2\u02d4\5p9\2\u02d3\u02d2\3\2\2\2\u02d3\u02d4\3\2\2\2\u02d4\u02d6",
    "\3\2\2\2\u02d5\u02d7\5*\26\2\u02d6\u02d5\3\2\2\2\u02d6\u02d7\3\2\2\2",
    "\u02d7\u02d8\3\2\2\2\u02d8\u02fc\7@\2\2\u02d9\u02da\f\7\2\2\u02da\u02db",
    "\7?\2\2\u02db\u02dd\7*\2\2\u02dc\u02de\5p9\2\u02dd\u02dc\3\2\2\2\u02dd",
    "\u02de\3\2\2\2\u02de\u02df\3\2\2\2\u02df\u02e0\5*\26\2\u02e0\u02e1\7",
    "@\2\2\u02e1\u02fc\3\2\2\2\u02e2\u02e3\f\6\2\2\u02e3\u02e4\7?\2\2\u02e4",
    "\u02e5\5p9\2\u02e5\u02e6\7*\2\2\u02e6\u02e7\5*\26\2\u02e7\u02e8\7@\2",
    "\2\u02e8\u02fc\3\2\2\2\u02e9\u02ea\f\5\2\2\u02ea\u02ec\7?\2\2\u02eb",
    "\u02ed\5p9\2\u02ec\u02eb\3\2\2\2\u02ec\u02ed\3\2\2\2\u02ed\u02ee\3\2",
    "\2\2\u02ee\u02ef\7M\2\2\u02ef\u02fc\7@\2\2\u02f0\u02f1\f\4\2\2\u02f1",
    "\u02f2\7=\2\2\u02f2\u02f3\5r:\2\u02f3\u02f4\7>\2\2\u02f4\u02fc\3\2\2",
    "\2\u02f5\u02f6\f\3\2\2\u02f6\u02f8\7=\2\2\u02f7\u02f9\5x=\2\u02f8\u02f7",
    "\3\2\2\2\u02f8\u02f9\3\2\2\2\u02f9\u02fa\3\2\2\2\u02fa\u02fc\7>\2\2",
    "\u02fb\u02d0\3\2\2\2\u02fb\u02d9\3\2\2\2\u02fb\u02e2\3\2\2\2\u02fb\u02e9",
    "\3\2\2\2\u02fb\u02f0\3\2\2\2\u02fb\u02f5\3\2\2\2\u02fc\u02ff\3\2\2\2",
    "\u02fd\u02fb\3\2\2\2\u02fd\u02fe\3\2\2\2\u02fec\3\2\2\2\u02ff\u02fd",
    "\3\2\2\2\u0300\u0301\7\r\2\2\u0301\u0303\7=\2\2\u0302\u0304\7m\2\2\u0303",
    "\u0302\3\2\2\2\u0304\u0305\3\2\2\2\u0305\u0303\3\2\2\2\u0305\u0306\3",
    "\2\2\2\u0306\u0307\3\2\2\2\u0307\u030a\7>\2\2\u0308\u030a\5f\64\2\u0309",
    "\u0300\3\2\2\2\u0309\u0308\3\2\2\2\u030ae\3\2\2\2\u030b\u030c\7\16\2",
    "\2\u030c\u030d\7=\2\2\u030d\u030e\7=\2\2\u030e\u030f\5h\65\2\u030f\u0310",
    "\7>\2\2\u0310\u0311\7>\2\2\u0311g\3\2\2\2\u0312\u0317\5j\66\2\u0313",
    "\u0314\7Z\2\2\u0314\u0316\5j\66\2\u0315\u0313\3\2\2\2\u0316\u0319\3",
    "\2\2\2\u0317\u0315\3\2\2\2\u0317\u0318\3\2\2\2\u0318\u031c\3\2\2\2\u0319",
    "\u0317\3\2\2\2\u031a\u031c\3\2\2\2\u031b\u0312\3\2\2\2\u031b\u031a\3",
    "\2\2\2\u031ci\3\2\2\2\u031d\u0323\n\n\2\2\u031e\u0320\7=\2\2\u031f\u0321",
    "\5\f\7\2\u0320\u031f\3\2\2\2\u0320\u0321\3\2\2\2\u0321\u0322\3\2\2\2",
    "\u0322\u0324\7>\2\2\u0323\u031e\3\2\2\2\u0323\u0324\3\2\2\2\u0324\u0327",
    "\3\2\2\2\u0325\u0327\3\2\2\2\u0326\u031d\3\2\2\2\u0326\u0325\3\2\2\2",
    "\u0327k\3\2\2\2\u0328\u032e\n\13\2\2\u0329\u032a\7=\2\2\u032a\u032b",
    "\5l\67\2\u032b\u032c\7>\2\2\u032c\u032e\3\2\2\2\u032d\u0328\3\2\2\2",
    "\u032d\u0329\3\2\2\2\u032e\u0331\3\2\2\2\u032f\u032d\3\2\2\2\u032f\u0330",
    "\3\2\2\2\u0330m\3\2\2\2\u0331\u032f\3\2\2\2\u0332\u0334\7M\2\2\u0333",
    "\u0335\5p9\2\u0334\u0333\3\2\2\2\u0334\u0335\3\2\2\2\u0335\u0345\3\2",
    "\2\2\u0336\u0338\7M\2\2\u0337\u0339\5p9\2\u0338\u0337\3\2\2\2\u0338",
    "\u0339\3\2\2\2\u0339\u033a\3\2\2\2\u033a\u0345\5n8\2\u033b\u033d\7T",
    "\2\2\u033c\u033e\5p9\2\u033d\u033c\3\2\2\2\u033d\u033e\3\2\2\2\u033e",
    "\u0345\3\2\2\2\u033f\u0341\7T\2\2\u0340\u0342\5p9\2\u0341\u0340\3\2",
    "\2\2\u0341\u0342\3\2\2\2\u0342\u0343\3\2\2\2\u0343\u0345\5n8\2\u0344",
    "\u0332\3\2\2\2\u0344\u0336\3\2\2\2\u0344\u033b\3\2\2\2\u0344\u033f\3",
    "\2\2\2\u0345o\3\2\2\2\u0346\u0347\b9\1\2\u0347\u0348\5Z.\2\u0348\u034d",
    "\3\2\2\2\u0349\u034a\f\3\2\2\u034a\u034c\5Z.\2\u034b\u0349\3\2\2\2\u034c",
    "\u034f\3\2\2\2\u034d\u034b\3\2\2\2\u034d\u034e\3\2\2\2\u034eq\3\2\2",
    "\2\u034f\u034d\3\2\2\2\u0350\u0356\5t;\2\u0351\u0352\5t;\2\u0352\u0353",
    "\7Z\2\2\u0353\u0354\7j\2\2\u0354\u0356\3\2\2\2\u0355\u0350\3\2\2\2\u0355",
    "\u0351\3\2\2\2\u0356s\3\2\2\2\u0357\u0358\b;\1\2\u0358\u0359\5v<\2\u0359",
    "\u035f\3\2\2\2\u035a\u035b\f\3\2\2\u035b\u035c\7Z\2\2\u035c\u035e\5",
    "v<\2\u035d\u035a\3\2\2\2\u035e\u0361\3\2\2\2\u035f\u035d\3\2\2\2\u035f",
    "\u0360\3\2\2\2\u0360u\3\2\2\2\u0361\u035f\3\2\2\2\u0362\u0363\5\64\33",
    "\2\u0363\u0364\5`\61\2\u0364\u036a\3\2\2\2\u0365\u0367\5\66\34\2\u0366",
    "\u0368\5|?\2\u0367\u0366\3\2\2\2\u0367\u0368\3\2\2\2\u0368\u036a\3\2",
    "\2\2\u0369\u0362\3\2\2\2\u0369\u0365\3\2\2\2\u036aw\3\2\2\2\u036b\u036c",
    "\b=\1\2\u036c\u036d\7k\2\2\u036d\u0373\3\2\2\2\u036e\u036f\f\3\2\2\u036f",
    "\u0370\7Z\2\2\u0370\u0372\7k\2\2\u0371\u036e\3\2\2\2\u0372\u0375\3\2",
    "\2\2\u0373\u0371\3\2\2\2\u0373\u0374\3\2\2\2\u0374y\3\2\2\2\u0375\u0373",
    "\3\2\2\2\u0376\u0378\5J&\2\u0377\u0379\5|?\2\u0378\u0377\3\2\2\2\u0378",
    "\u0379\3\2\2\2\u0379{\3\2\2\2\u037a\u0386\5n8\2\u037b\u037d\5n8\2\u037c",
    "\u037b\3\2\2\2\u037c\u037d\3\2\2\2\u037d\u037e\3\2\2\2\u037e\u0382\5",
    "~@\2\u037f\u0381\5d\63\2\u0380\u037f\3\2\2\2\u0381\u0384\3\2\2\2\u0382",
    "\u0380\3\2\2\2\u0382\u0383\3\2\2\2\u0383\u0386\3\2\2\2\u0384\u0382\3",
    "\2\2\2\u0385\u037a\3\2\2\2\u0385\u037c\3\2\2\2\u0386}\3\2\2\2\u0387",
    "\u0388\b@\1\2\u0388\u0389\7=\2\2\u0389\u038a\5|?\2\u038a\u038e\7>\2",
    "\2\u038b\u038d\5d\63\2\u038c\u038b\3\2\2\2\u038d\u0390\3\2\2\2\u038e",
    "\u038c\3\2\2\2\u038e\u038f\3\2\2\2\u038f\u03b6\3\2\2\2\u0390\u038e\3",
    "\2\2\2\u0391\u0393\7?\2\2\u0392\u0394\5p9\2\u0393\u0392\3\2\2\2\u0393",
    "\u0394\3\2\2\2\u0394\u0396\3\2\2\2\u0395\u0397\5*\26\2\u0396\u0395\3",
    "\2\2\2\u0396\u0397\3\2\2\2\u0397\u0398\3\2\2\2\u0398\u03b6\7@\2\2\u0399",
    "\u039a\7?\2\2\u039a\u039c\7*\2\2\u039b\u039d\5p9\2\u039c\u039b\3\2\2",
    "\2\u039c\u039d\3\2\2\2\u039d\u039e\3\2\2\2\u039e\u039f\5*\26\2\u039f",
    "\u03a0\7@\2\2\u03a0\u03b6\3\2\2\2\u03a1\u03a2\7?\2\2\u03a2\u03a3\5p",
    "9\2\u03a3\u03a4\7*\2\2\u03a4\u03a5\5*\26\2\u03a5\u03a6\7@\2\2\u03a6",
    "\u03b6\3\2\2\2\u03a7\u03a8\7?\2\2\u03a8\u03a9\7M\2\2\u03a9\u03b6\7@",
    "\2\2\u03aa\u03ac\7=\2\2\u03ab\u03ad\5r:\2\u03ac\u03ab\3\2\2\2\u03ac",
    "\u03ad\3\2\2\2\u03ad\u03ae\3\2\2\2\u03ae\u03b2\7>\2\2\u03af\u03b1\5",
    "d\63\2\u03b0\u03af\3\2\2\2\u03b1\u03b4\3\2\2\2\u03b2\u03b0\3\2\2\2\u03b2",
    "\u03b3\3\2\2\2\u03b3\u03b6\3\2\2\2\u03b4\u03b2\3\2\2\2\u03b5\u0387\3",
    "\2\2\2\u03b5\u0391\3\2\2\2\u03b5\u0399\3\2\2\2\u03b5\u03a1\3\2\2\2\u03b5",
    "\u03a7\3\2\2\2\u03b5\u03aa\3\2\2\2\u03b6\u03e2\3\2\2\2\u03b7\u03b8\f",
    "\7\2\2\u03b8\u03ba\7?\2\2\u03b9\u03bb\5p9\2\u03ba\u03b9\3\2\2\2\u03ba",
    "\u03bb\3\2\2\2\u03bb\u03bd\3\2\2\2\u03bc\u03be\5*\26\2\u03bd\u03bc\3",
    "\2\2\2\u03bd\u03be\3\2\2\2\u03be\u03bf\3\2\2\2\u03bf\u03e1\7@\2\2\u03c0",
    "\u03c1\f\6\2\2\u03c1\u03c2\7?\2\2\u03c2\u03c4\7*\2\2\u03c3\u03c5\5p",
    "9\2\u03c4\u03c3\3\2\2\2\u03c4\u03c5\3\2\2\2\u03c5\u03c6\3\2\2\2\u03c6",
    "\u03c7\5*\26\2\u03c7\u03c8\7@\2\2\u03c8\u03e1\3\2\2\2\u03c9\u03ca\f",
    "\5\2\2\u03ca\u03cb\7?\2\2\u03cb\u03cc\5p9\2\u03cc\u03cd\7*\2\2\u03cd",
    "\u03ce\5*\26\2\u03ce\u03cf\7@\2\2\u03cf\u03e1\3\2\2\2\u03d0\u03d1\f",
    "\4\2\2\u03d1\u03d2\7?\2\2\u03d2\u03d3\7M\2\2\u03d3\u03e1\7@\2\2\u03d4",
    "\u03d5\f\3\2\2\u03d5\u03d7\7=\2\2\u03d6\u03d8\5r:\2\u03d7\u03d6\3\2",
    "\2\2\u03d7\u03d8\3\2\2\2\u03d8\u03d9\3\2\2\2\u03d9\u03dd\7>\2\2\u03da",
    "\u03dc\5d\63\2\u03db\u03da\3\2\2\2\u03dc\u03df\3\2\2\2\u03dd\u03db\3",
    "\2\2\2\u03dd\u03de\3\2\2\2\u03de\u03e1\3\2\2\2\u03df\u03dd\3\2\2\2\u03e0",
    "\u03b7\3\2\2\2\u03e0\u03c0\3\2\2\2\u03e0\u03c9\3\2\2\2\u03e0\u03d0\3",
    "\2\2\2\u03e0\u03d4\3\2\2\2\u03e1\u03e4\3\2\2\2\u03e2\u03e0\3\2\2\2\u03e2",
    "\u03e3\3\2\2\2\u03e3\177\3\2\2\2\u03e4\u03e2\3\2\2\2\u03e5\u03e6\7k",
    "\2\2\u03e6\u0081\3\2\2\2\u03e7\u03f2\5*\26\2\u03e8\u03e9\7A\2\2\u03e9",
    "\u03ea\5\u0084C\2\u03ea\u03eb\7B\2\2\u03eb\u03f2\3\2\2\2\u03ec\u03ed",
    "\7A\2\2\u03ed\u03ee\5\u0084C\2\u03ee\u03ef\7Z\2\2\u03ef\u03f0\7B\2\2",
    "\u03f0\u03f2\3\2\2\2\u03f1\u03e7\3\2\2\2\u03f1\u03e8\3\2\2\2\u03f1\u03ec",
    "\3\2\2\2\u03f2\u0083\3\2\2\2\u03f3\u03f5\bC\1\2\u03f4\u03f6\5\u0086",
    "D\2\u03f5\u03f4\3\2\2\2\u03f5\u03f6\3\2\2\2\u03f6\u03f7\3\2\2\2\u03f7",
    "\u03f8\5\u0082B\2\u03f8\u0401\3\2\2\2\u03f9\u03fa\f\3\2\2\u03fa\u03fc",
    "\7Z\2\2\u03fb\u03fd\5\u0086D\2\u03fc\u03fb\3\2\2\2\u03fc\u03fd\3\2\2",
    "\2\u03fd\u03fe\3\2\2\2\u03fe\u0400\5\u0082B\2\u03ff\u03f9\3\2\2\2\u0400",
    "\u0403\3\2\2\2\u0401\u03ff\3\2\2\2\u0401\u0402\3\2\2\2\u0402\u0085\3",
    "\2\2\2\u0403\u0401\3\2\2\2\u0404\u0405\5\u0088E\2\u0405\u0406\7[\2\2",
    "\u0406\u0087\3\2\2\2\u0407\u0408\bE\1\2\u0408\u0409\5\u008aF\2\u0409",
    "\u040e\3\2\2\2\u040a\u040b\f\3\2\2\u040b\u040d\5\u008aF\2\u040c\u040a",
    "\3\2\2\2\u040d\u0410\3\2\2\2\u040e\u040c\3\2\2\2\u040e\u040f\3\2\2\2",
    "\u040f\u0089\3\2\2\2\u0410\u040e\3\2\2\2\u0411\u0412\7?\2\2\u0412\u0413",
    "\5\60\31\2\u0413\u0414\7@\2\2\u0414\u0418\3\2\2\2\u0415\u0416\7i\2\2",
    "\u0416\u0418\7k\2\2\u0417\u0411\3\2\2\2\u0417\u0415\3\2\2\2\u0418\u008b",
    "\3\2\2\2\u0419\u041a\7;\2\2\u041a\u041b\7=\2\2\u041b\u041c\5\60\31\2",
    "\u041c\u041e\7Z\2\2\u041d\u041f\7m\2\2\u041e\u041d\3\2\2\2\u041f\u0420",
    "\3\2\2\2\u0420\u041e\3\2\2\2\u0420\u0421\3\2\2\2\u0421\u0422\3\2\2\2",
    "\u0422\u0423\7>\2\2\u0423\u0424\7Y\2\2\u0424\u008d\3\2\2\2\u0425\u044b",
    "\5\u0090I\2\u0426\u044b\5\u0092J\2\u0427\u044b\5\u0098M\2\u0428\u044b",
    "\5\u009aN\2\u0429\u044b\5\u009cO\2\u042a\u044b\5\u009eP\2\u042b\u042c",
    "\t\f\2\2\u042c\u042d\t\r\2\2\u042d\u0436\7=\2\2\u042e\u0433\5&\24\2",
    "\u042f\u0430\7Z\2\2\u0430\u0432\5&\24\2\u0431\u042f\3\2\2\2\u0432\u0435",
    "\3\2\2\2\u0433\u0431\3\2\2\2\u0433\u0434\3\2\2\2\u0434\u0437\3\2\2\2",
    "\u0435\u0433\3\2\2\2\u0436\u042e\3\2\2\2\u0436\u0437\3\2\2\2\u0437\u0445",
    "\3\2\2\2\u0438\u0441\7X\2\2\u0439\u043e\5&\24\2\u043a\u043b\7Z\2\2\u043b",
    "\u043d\5&\24\2\u043c\u043a\3\2\2\2\u043d\u0440\3\2\2\2\u043e\u043c\3",
    "\2\2\2\u043e\u043f\3\2\2\2\u043f\u0442\3\2\2\2\u0440\u043e\3\2\2\2\u0441",
    "\u0439\3\2\2\2\u0441\u0442\3\2\2\2\u0442\u0444\3\2\2\2\u0443\u0438\3",
    "\2\2\2\u0444\u0447\3\2\2\2\u0445\u0443\3\2\2\2\u0445\u0446\3\2\2\2\u0446",
    "\u0448\3\2\2\2\u0447\u0445\3\2\2\2\u0448\u0449\7>\2\2\u0449\u044b\7",
    "Y\2\2\u044a\u0425\3\2\2\2\u044a\u0426\3\2\2\2\u044a\u0427\3\2\2\2\u044a",
    "\u0428\3\2\2\2\u044a\u0429\3\2\2\2\u044a\u042a\3\2\2\2\u044a\u042b\3",
    "\2\2\2\u044b\u008f\3\2\2\2\u044c\u044d\7k\2\2\u044d\u044e\7X\2\2\u044e",
    "\u0458\5\u008eH\2\u044f\u0450\7\23\2\2\u0450\u0451\5\60\31\2\u0451\u0452",
    "\7X\2\2\u0452\u0453\5\u008eH\2\u0453\u0458\3\2\2\2\u0454\u0455\7\27",
    "\2\2\u0455\u0456\7X\2\2\u0456\u0458\5\u008eH\2\u0457\u044c\3\2\2\2\u0457",
    "\u044f\3\2\2\2\u0457\u0454\3\2\2\2\u0458\u0091\3\2\2\2\u0459\u045b\7",
    "A\2\2\u045a\u045c\5\u0094K\2\u045b\u045a\3\2\2\2\u045b\u045c\3\2\2\2",
    "\u045c\u045d\3\2\2\2\u045d\u045e\7B\2\2\u045e\u0093\3\2\2\2\u045f\u0460",
    "\bK\1\2\u0460\u0461\5\u0096L\2\u0461\u0466\3\2\2\2\u0462\u0463\f\3\2",
    "\2\u0463\u0465\5\u0096L\2\u0464\u0462\3\2\2\2\u0465\u0468\3\2\2\2\u0466",
    "\u0464\3\2\2\2\u0466\u0467\3\2\2\2\u0467\u0095\3\2\2\2\u0468\u0466\3",
    "\2\2\2\u0469\u046c\5\62\32\2\u046a\u046c\5\u008eH\2\u046b\u0469\3\2",
    "\2\2\u046b\u046a\3\2\2\2\u046c\u0097\3\2\2\2\u046d\u046f\5.\30\2\u046e",
    "\u046d\3\2\2\2\u046e\u046f\3\2\2\2\u046f\u0470\3\2\2\2\u0470\u0471\7",
    "Y\2\2\u0471\u0099\3\2\2\2\u0472\u0473\7 \2\2\u0473\u0474\7=\2\2\u0474",
    "\u0475\5.\30\2\u0475\u0476\7>\2\2\u0476\u0479\5\u008eH\2\u0477\u0478",
    "\7\32\2\2\u0478\u047a\5\u008eH\2\u0479\u0477\3\2\2\2\u0479\u047a\3\2",
    "\2\2\u047a\u0482\3\2\2\2\u047b\u047c\7,\2\2\u047c\u047d\7=\2\2\u047d",
    "\u047e\5.\30\2\u047e\u047f\7>\2\2\u047f\u0480\5\u008eH\2\u0480\u0482",
    "\3\2\2\2\u0481\u0472\3\2\2\2\u0481\u047b\3\2\2\2\u0482\u009b\3\2\2\2",
    "\u0483\u0484\7\62\2\2\u0484\u0485\7=\2\2\u0485\u0486\5.\30\2\u0486\u0487",
    "\7>\2\2\u0487\u0488\5\u008eH\2\u0488\u04ae\3\2\2\2\u0489\u048a\7\30",
    "\2\2\u048a\u048b\5\u008eH\2\u048b\u048c\7\62\2\2\u048c\u048d\7=\2\2",
    "\u048d\u048e\5.\30\2\u048e\u048f\7>\2\2\u048f\u0490\7Y\2\2\u0490\u04ae",
    "\3\2\2\2\u0491\u0492\7\36\2\2\u0492\u0494\7=\2\2\u0493\u0495\5.\30\2",
    "\u0494\u0493\3\2\2\2\u0494\u0495\3\2\2\2\u0495\u0496\3\2\2\2\u0496\u0498",
    "\7Y\2\2\u0497\u0499\5.\30\2\u0498\u0497\3\2\2\2\u0498\u0499\3\2\2\2",
    "\u0499\u049a\3\2\2\2\u049a\u049c\7Y\2\2\u049b\u049d\5.\30\2\u049c\u049b",
    "\3\2\2\2\u049c\u049d\3\2\2\2\u049d\u049e\3\2\2\2\u049e\u049f\7>\2\2",
    "\u049f\u04ae\5\u008eH\2\u04a0\u04a1\7\36\2\2\u04a1\u04a2\7=\2\2\u04a2",
    "\u04a4\5\62\32\2\u04a3\u04a5\5.\30\2\u04a4\u04a3\3\2\2\2\u04a4\u04a5",
    "\3\2\2\2\u04a5\u04a6\3\2\2\2\u04a6\u04a8\7Y\2\2\u04a7\u04a9\5.\30\2",
    "\u04a8\u04a7\3\2\2\2\u04a8\u04a9\3\2\2\2\u04a9\u04aa\3\2\2\2\u04aa\u04ab",
    "\7>\2\2\u04ab\u04ac\5\u008eH\2\u04ac\u04ae\3\2\2\2\u04ad\u0483\3\2\2",
    "\2\u04ad\u0489\3\2\2\2\u04ad\u0491\3\2\2\2\u04ad\u04a0\3\2\2\2\u04ae",
    "\u009d\3\2\2\2\u04af\u04b0\7\37\2\2\u04b0\u04b1\7k\2\2\u04b1\u04c0\7",
    "Y\2\2\u04b2\u04b3\7\26\2\2\u04b3\u04c0\7Y\2\2\u04b4\u04b5\7\22\2\2\u04b5",
    "\u04c0\7Y\2\2\u04b6\u04b8\7&\2\2\u04b7\u04b9\5.\30\2\u04b8\u04b7\3\2",
    "\2\2\u04b8\u04b9\3\2\2\2\u04b9\u04ba\3\2\2\2\u04ba\u04c0\7Y\2\2\u04bb",
    "\u04bc\7\37\2\2\u04bc\u04bd\5\16\b\2\u04bd\u04be\7Y\2\2\u04be\u04c0",
    "\3\2\2\2\u04bf\u04af\3\2\2\2\u04bf\u04b2\3\2\2\2\u04bf\u04b4\3\2\2\2",
    "\u04bf\u04b6\3\2\2\2\u04bf\u04bb\3\2\2\2\u04c0\u009f\3\2\2\2\u04c1\u04c3",
    "\5\u00a2R\2\u04c2\u04c1\3\2\2\2\u04c2\u04c3\3\2\2\2\u04c3\u04c4\3\2",
    "\2\2\u04c4\u04c5\7\2\2\3\u04c5\u00a1\3\2\2\2\u04c6\u04c7\bR\1\2\u04c7",
    "\u04c8\5\u00a4S\2\u04c8\u04cd\3\2\2\2\u04c9\u04ca\f\3\2\2\u04ca\u04cc",
    "\5\u00a4S\2\u04cb\u04c9\3\2\2\2\u04cc\u04cf\3\2\2\2\u04cd\u04cb\3\2",
    "\2\2\u04cd\u04ce\3\2\2\2\u04ce\u00a3\3\2\2\2\u04cf\u04cd\3\2\2\2\u04d0",
    "\u04d4\5\u00a6T\2\u04d1\u04d4\5\62\32\2\u04d2\u04d4\7Y\2\2\u04d3\u04d0",
    "\3\2\2\2\u04d3\u04d1\3\2\2\2\u04d3\u04d2\3\2\2\2\u04d4\u00a5\3\2\2\2",
    "\u04d5\u04d7\5\64\33\2\u04d6\u04d5\3\2\2\2\u04d6\u04d7\3\2\2\2\u04d7",
    "\u04d8\3\2\2\2\u04d8\u04da\5`\61\2\u04d9\u04db\5\u00a8U\2\u04da\u04d9",
    "\3\2\2\2\u04da\u04db\3\2\2\2\u04db\u04dc\3\2\2\2\u04dc\u04dd\5\u0092",
    "J\2\u04dd\u00a7\3\2\2\2\u04de\u04df\bU\1\2\u04df\u04e0\5\62\32\2\u04e0",
    "\u04e5\3\2\2\2\u04e1\u04e2\f\3\2\2\u04e2\u04e4\5\62\32\2\u04e3\u04e1",
    "\3\2\2\2\u04e4\u04e7\3\2\2\2\u04e5\u04e3\3\2\2\2\u04e5\u04e6\3\2\2\2",
    "\u04e6\u00a9\3\2\2\2\u04e7\u04e5\3\2\2\2\u008c\u00af\u00b7\u00cb\u00dc",
    "\u00e6\u010a\u0114\u0121\u0123\u012e\u0147\u0157\u0165\u0167\u0173\u0175",
    "\u0181\u0183\u0195\u0197\u01a3\u01a5\u01b0\u01bb\u01c6\u01d1\u01dc\u01e5",
    "\u01ec\u01f8\u01ff\u0204\u0209\u020e\u0215\u021f\u0227\u0239\u023d\u0246",
    "\u0251\u0256\u025b\u025f\u0263\u0265\u026f\u0274\u0278\u027c\u0284\u028d",
    "\u0297\u029f\u02b0\u02bc\u02bf\u02c5\u02ce\u02d3\u02d6\u02dd\u02ec\u02f8",
    "\u02fb\u02fd\u0305\u0309\u0317\u031b\u0320\u0323\u0326\u032d\u032f\u0334",
    "\u0338\u033d\u0341\u0344\u034d\u0355\u035f\u0367\u0369\u0373\u0378\u037c",
    "\u0382\u0385\u038e\u0393\u0396\u039c\u03ac\u03b2\u03b5\u03ba\u03bd\u03c4",
    "\u03d7\u03dd\u03e0\u03e2\u03f1\u03f5\u03fc\u0401\u040e\u0417\u0420\u0433",
    "\u0436\u043e\u0441\u0445\u044a\u0457\u045b\u0466\u046b\u046e\u0479\u0481",
    "\u0494\u0498\u049c\u04a4\u04a8\u04ad\u04b8\u04bf\u04c2\u04cd\u04d3\u04d6",
    "\u04da\u04e5"].join("");


var atn = new antlr4.atn.ATNDeserializer().deserialize(serializedATN);

var decisionsToDFA = atn.decisionToState.map( function(ds, index) { return new antlr4.dfa.DFA(ds, index); });

var sharedContextCache = new antlr4.PredictionContextCache();

var literalNames = [ 'null', "'__extension__'", "'__builtin_va_arg'", "'__builtin_offsetof'", 
                     "'__m128'", "'__m128d'", "'__m128i'", "'__typeof__'", 
                     "'__inline__'", "'__stdcall'", "'__declspec'", "'__asm'", 
                     "'__attribute__'", "'__asm__'", "'__volatile__'", "'auto'", 
                     "'break'", "'case'", "'char'", "'const'", "'continue'", 
                     "'default'", "'do'", "'double'", "'else'", "'enum'", 
                     "'extern'", "'float'", "'for'", "'goto'", "'if'", "'inline'", 
                     "'int'", "'long'", "'register'", "'restrict'", "'return'", 
                     "'short'", "'signed'", "'sizeof'", "'static'", "'struct'", 
                     "'switch'", "'typedef'", "'union'", "'unsigned'", "'void'", 
                     "'volatile'", "'while'", "'_Alignas'", "'_Alignof'", 
                     "'_Atomic'", "'_Bool'", "'_Complex'", "'_Generic'", 
                     "'_Imaginary'", "'_Noreturn'", "'_Static_assert'", 
                     "'_Thread_local'", "'('", "')'", "'['", "']'", "'{'", 
                     "'}'", "'<'", "'<='", "'>'", "'>='", "'<<'", "'>>'", 
                     "'+'", "'++'", "'-'", "'--'", "'*'", "'/'", "'%'", 
                     "'&'", "'|'", "'&&'", "'||'", "'^'", "'!'", "'~'", 
                     "'?'", "':'", "';'", "','", "'='", "'*='", "'/='", 
                     "'%='", "'+='", "'-='", "'<<='", "'>>='", "'&='", "'^='", 
                     "'|='", "'=='", "'!='", "'->'", "'.'", "'...'" ];

var symbolicNames = [ 'null', 'null', 'null', 'null', 'null', 'null', 'null', 
                      'null', 'null', 'null', 'null', 'null', 'null', 'null', 
                      'null', "Auto", "Break", "Case", "Char", "Const", 
                      "Continue", "Default", "Do", "Double", "Else", "Enum", 
                      "Extern", "Float", "For", "Goto", "If", "Inline", 
                      "Int", "Long", "Register", "Restrict", "Return", "Short", 
                      "Signed", "Sizeof", "Static", "Struct", "Switch", 
                      "Typedef", "Union", "Unsigned", "Void", "Volatile", 
                      "While", "Alignas", "Alignof", "Atomic", "Bool", "Complex", 
                      "Generic", "Imaginary", "Noreturn", "StaticAssert", 
                      "ThreadLocal", "LeftParen", "RightParen", "LeftBracket", 
                      "RightBracket", "LeftBrace", "RightBrace", "Less", 
                      "LessEqual", "Greater", "GreaterEqual", "LeftShift", 
                      "RightShift", "Plus", "PlusPlus", "Minus", "MinusMinus", 
                      "Star", "Div", "Mod", "And", "Or", "AndAnd", "OrOr", 
                      "Caret", "Not", "Tilde", "Question", "Colon", "Semi", 
                      "Comma", "Assign", "StarAssign", "DivAssign", "ModAssign", 
                      "PlusAssign", "MinusAssign", "LeftShiftAssign", "RightShiftAssign", 
                      "AndAssign", "XorAssign", "OrAssign", "Equal", "NotEqual", 
                      "Arrow", "Dot", "Ellipsis", "Identifier", "Constant", 
                      "StringLiteral", "LineDirective", "PragmaDirective", 
                      "Whitespace", "Newline", "BlockComment", "LineComment" ];

var ruleNames =  [ "primaryExpression", "genericSelection", "genericAssocList", 
                   "genericAssociation", "postfixExpression", "argumentExpressionList", 
                   "unaryExpression", "unaryOperator", "castExpression", 
                   "multiplicativeExpression", "additiveExpression", "shiftExpression", 
                   "relationalExpression", "equalityExpression", "andExpression", 
                   "exclusiveOrExpression", "inclusiveOrExpression", "logicalAndExpression", 
                   "logicalOrExpression", "conditionalExpression", "assignmentExpression", 
                   "assignmentOperator", "expression", "constantExpression", 
                   "declaration", "declarationSpecifiers", "declarationSpecifiers2", 
                   "declarationSpecifier", "initDeclaratorList", "initDeclarator", 
                   "storageClassSpecifier", "typeSpecifier", "structOrUnionSpecifier", 
                   "structOrUnion", "structDeclarationList", "structDeclaration", 
                   "specifierQualifierList", "structDeclaratorList", "structDeclarator", 
                   "enumSpecifier", "enumeratorList", "enumerator", "enumerationConstant", 
                   "atomicTypeSpecifier", "typeQualifier", "functionSpecifier", 
                   "alignmentSpecifier", "declarator", "directDeclarator", 
                   "gccDeclaratorExtension", "gccAttributeSpecifier", "gccAttributeList", 
                   "gccAttribute", "nestedParenthesesBlock", "pointer", 
                   "typeQualifierList", "parameterTypeList", "parameterList", 
                   "parameterDeclaration", "identifierList", "typeName", 
                   "abstractDeclarator", "directAbstractDeclarator", "typedefName", 
                   "initializer", "initializerList", "designation", "designatorList", 
                   "designator", "staticAssertDeclaration", "statement", 
                   "labeledStatement", "compoundStatement", "blockItemList", 
                   "blockItem", "expressionStatement", "selectionStatement", 
                   "iterationStatement", "jumpStatement", "compilationUnit", 
                   "translationUnit", "externalDeclaration", "functionDefinition", 
                   "declarationList" ];

function CParser (input) {
	antlr4.Parser.call(this, input);
    this._interp = new antlr4.atn.ParserATNSimulator(this, atn, decisionsToDFA, sharedContextCache);
    this.ruleNames = ruleNames;
    this.literalNames = literalNames;
    this.symbolicNames = symbolicNames;
    return this;
}

CParser.prototype = Object.create(antlr4.Parser.prototype);
CParser.prototype.constructor = CParser;

Object.defineProperty(CParser.prototype, "atn", {
	get : function() {
		return atn;
	}
});

CParser.EOF = antlr4.Token.EOF;
CParser.T__0 = 1;
CParser.T__1 = 2;
CParser.T__2 = 3;
CParser.T__3 = 4;
CParser.T__4 = 5;
CParser.T__5 = 6;
CParser.T__6 = 7;
CParser.T__7 = 8;
CParser.T__8 = 9;
CParser.T__9 = 10;
CParser.T__10 = 11;
CParser.T__11 = 12;
CParser.T__12 = 13;
CParser.T__13 = 14;
CParser.Auto = 15;
CParser.Break = 16;
CParser.Case = 17;
CParser.Char = 18;
CParser.Const = 19;
CParser.Continue = 20;
CParser.Default = 21;
CParser.Do = 22;
CParser.Double = 23;
CParser.Else = 24;
CParser.Enum = 25;
CParser.Extern = 26;
CParser.Float = 27;
CParser.For = 28;
CParser.Goto = 29;
CParser.If = 30;
CParser.Inline = 31;
CParser.Int = 32;
CParser.Long = 33;
CParser.Register = 34;
CParser.Restrict = 35;
CParser.Return = 36;
CParser.Short = 37;
CParser.Signed = 38;
CParser.Sizeof = 39;
CParser.Static = 40;
CParser.Struct = 41;
CParser.Switch = 42;
CParser.Typedef = 43;
CParser.Union = 44;
CParser.Unsigned = 45;
CParser.Void = 46;
CParser.Volatile = 47;
CParser.While = 48;
CParser.Alignas = 49;
CParser.Alignof = 50;
CParser.Atomic = 51;
CParser.Bool = 52;
CParser.Complex = 53;
CParser.Generic = 54;
CParser.Imaginary = 55;
CParser.Noreturn = 56;
CParser.StaticAssert = 57;
CParser.ThreadLocal = 58;
CParser.LeftParen = 59;
CParser.RightParen = 60;
CParser.LeftBracket = 61;
CParser.RightBracket = 62;
CParser.LeftBrace = 63;
CParser.RightBrace = 64;
CParser.Less = 65;
CParser.LessEqual = 66;
CParser.Greater = 67;
CParser.GreaterEqual = 68;
CParser.LeftShift = 69;
CParser.RightShift = 70;
CParser.Plus = 71;
CParser.PlusPlus = 72;
CParser.Minus = 73;
CParser.MinusMinus = 74;
CParser.Star = 75;
CParser.Div = 76;
CParser.Mod = 77;
CParser.And = 78;
CParser.Or = 79;
CParser.AndAnd = 80;
CParser.OrOr = 81;
CParser.Caret = 82;
CParser.Not = 83;
CParser.Tilde = 84;
CParser.Question = 85;
CParser.Colon = 86;
CParser.Semi = 87;
CParser.Comma = 88;
CParser.Assign = 89;
CParser.StarAssign = 90;
CParser.DivAssign = 91;
CParser.ModAssign = 92;
CParser.PlusAssign = 93;
CParser.MinusAssign = 94;
CParser.LeftShiftAssign = 95;
CParser.RightShiftAssign = 96;
CParser.AndAssign = 97;
CParser.XorAssign = 98;
CParser.OrAssign = 99;
CParser.Equal = 100;
CParser.NotEqual = 101;
CParser.Arrow = 102;
CParser.Dot = 103;
CParser.Ellipsis = 104;
CParser.Identifier = 105;
CParser.Constant = 106;
CParser.StringLiteral = 107;
CParser.LineDirective = 108;
CParser.PragmaDirective = 109;
CParser.Whitespace = 110;
CParser.Newline = 111;
CParser.BlockComment = 112;
CParser.LineComment = 113;

CParser.RULE_primaryExpression = 0;
CParser.RULE_genericSelection = 1;
CParser.RULE_genericAssocList = 2;
CParser.RULE_genericAssociation = 3;
CParser.RULE_postfixExpression = 4;
CParser.RULE_argumentExpressionList = 5;
CParser.RULE_unaryExpression = 6;
CParser.RULE_unaryOperator = 7;
CParser.RULE_castExpression = 8;
CParser.RULE_multiplicativeExpression = 9;
CParser.RULE_additiveExpression = 10;
CParser.RULE_shiftExpression = 11;
CParser.RULE_relationalExpression = 12;
CParser.RULE_equalityExpression = 13;
CParser.RULE_andExpression = 14;
CParser.RULE_exclusiveOrExpression = 15;
CParser.RULE_inclusiveOrExpression = 16;
CParser.RULE_logicalAndExpression = 17;
CParser.RULE_logicalOrExpression = 18;
CParser.RULE_conditionalExpression = 19;
CParser.RULE_assignmentExpression = 20;
CParser.RULE_assignmentOperator = 21;
CParser.RULE_expression = 22;
CParser.RULE_constantExpression = 23;
CParser.RULE_declaration = 24;
CParser.RULE_declarationSpecifiers = 25;
CParser.RULE_declarationSpecifiers2 = 26;
CParser.RULE_declarationSpecifier = 27;
CParser.RULE_initDeclaratorList = 28;
CParser.RULE_initDeclarator = 29;
CParser.RULE_storageClassSpecifier = 30;
CParser.RULE_typeSpecifier = 31;
CParser.RULE_structOrUnionSpecifier = 32;
CParser.RULE_structOrUnion = 33;
CParser.RULE_structDeclarationList = 34;
CParser.RULE_structDeclaration = 35;
CParser.RULE_specifierQualifierList = 36;
CParser.RULE_structDeclaratorList = 37;
CParser.RULE_structDeclarator = 38;
CParser.RULE_enumSpecifier = 39;
CParser.RULE_enumeratorList = 40;
CParser.RULE_enumerator = 41;
CParser.RULE_enumerationConstant = 42;
CParser.RULE_atomicTypeSpecifier = 43;
CParser.RULE_typeQualifier = 44;
CParser.RULE_functionSpecifier = 45;
CParser.RULE_alignmentSpecifier = 46;
CParser.RULE_declarator = 47;
CParser.RULE_directDeclarator = 48;
CParser.RULE_gccDeclaratorExtension = 49;
CParser.RULE_gccAttributeSpecifier = 50;
CParser.RULE_gccAttributeList = 51;
CParser.RULE_gccAttribute = 52;
CParser.RULE_nestedParenthesesBlock = 53;
CParser.RULE_pointer = 54;
CParser.RULE_typeQualifierList = 55;
CParser.RULE_parameterTypeList = 56;
CParser.RULE_parameterList = 57;
CParser.RULE_parameterDeclaration = 58;
CParser.RULE_identifierList = 59;
CParser.RULE_typeName = 60;
CParser.RULE_abstractDeclarator = 61;
CParser.RULE_directAbstractDeclarator = 62;
CParser.RULE_typedefName = 63;
CParser.RULE_initializer = 64;
CParser.RULE_initializerList = 65;
CParser.RULE_designation = 66;
CParser.RULE_designatorList = 67;
CParser.RULE_designator = 68;
CParser.RULE_staticAssertDeclaration = 69;
CParser.RULE_statement = 70;
CParser.RULE_labeledStatement = 71;
CParser.RULE_compoundStatement = 72;
CParser.RULE_blockItemList = 73;
CParser.RULE_blockItem = 74;
CParser.RULE_expressionStatement = 75;
CParser.RULE_selectionStatement = 76;
CParser.RULE_iterationStatement = 77;
CParser.RULE_jumpStatement = 78;
CParser.RULE_compilationUnit = 79;
CParser.RULE_translationUnit = 80;
CParser.RULE_externalDeclaration = 81;
CParser.RULE_functionDefinition = 82;
CParser.RULE_declarationList = 83;

function PrimaryExpressionContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_primaryExpression;
    return this;
}

PrimaryExpressionContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
PrimaryExpressionContext.prototype.constructor = PrimaryExpressionContext;

PrimaryExpressionContext.prototype.Identifier = function() {
    return this.getToken(CParser.Identifier, 0);
};

PrimaryExpressionContext.prototype.Constant = function() {
    return this.getToken(CParser.Constant, 0);
};

PrimaryExpressionContext.prototype.StringLiteral = function(i) {
	if(i===undefined) {
		i = null;
	}
    if(i===null) {
        return this.getTokens(CParser.StringLiteral);
    } else {
        return this.getToken(CParser.StringLiteral, i);
    }
};


PrimaryExpressionContext.prototype.expression = function() {
    return this.getTypedRuleContext(ExpressionContext,0);
};

PrimaryExpressionContext.prototype.genericSelection = function() {
    return this.getTypedRuleContext(GenericSelectionContext,0);
};

PrimaryExpressionContext.prototype.compoundStatement = function() {
    return this.getTypedRuleContext(CompoundStatementContext,0);
};

PrimaryExpressionContext.prototype.unaryExpression = function() {
    return this.getTypedRuleContext(UnaryExpressionContext,0);
};

PrimaryExpressionContext.prototype.typeName = function() {
    return this.getTypedRuleContext(TypeNameContext,0);
};

PrimaryExpressionContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterPrimaryExpression(this);
	}
};

PrimaryExpressionContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitPrimaryExpression(this);
	}
};




CParser.PrimaryExpressionContext = PrimaryExpressionContext;

CParser.prototype.primaryExpression = function() {

    var localctx = new PrimaryExpressionContext(this, this._ctx, this.state);
    this.enterRule(localctx, 0, CParser.RULE_primaryExpression);
    var _la = 0; // Token type
    try {
        this.state = 201;
        var la_ = this._interp.adaptivePredict(this._input,2,this._ctx);
        switch(la_) {
        case 1:
            this.enterOuterAlt(localctx, 1);
            this.state = 168;
            this.match(CParser.Identifier);
            break;

        case 2:
            this.enterOuterAlt(localctx, 2);
            this.state = 169;
            this.match(CParser.Constant);
            break;

        case 3:
            this.enterOuterAlt(localctx, 3);
            this.state = 171; 
            this._errHandler.sync(this);
            var _alt = 1;
            do {
            	switch (_alt) {
            	case 1:
            		this.state = 170;
            		this.match(CParser.StringLiteral);
            		break;
            	default:
            		throw new antlr4.error.NoViableAltException(this);
            	}
            	this.state = 173; 
            	this._errHandler.sync(this);
            	_alt = this._interp.adaptivePredict(this._input,0, this._ctx);
            } while ( _alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER );
            break;

        case 4:
            this.enterOuterAlt(localctx, 4);
            this.state = 175;
            this.match(CParser.LeftParen);
            this.state = 176;
            this.expression(0);
            this.state = 177;
            this.match(CParser.RightParen);
            break;

        case 5:
            this.enterOuterAlt(localctx, 5);
            this.state = 179;
            this.genericSelection();
            break;

        case 6:
            this.enterOuterAlt(localctx, 6);
            this.state = 181;
            _la = this._input.LA(1);
            if(_la===CParser.T__0) {
                this.state = 180;
                this.match(CParser.T__0);
            }

            this.state = 183;
            this.match(CParser.LeftParen);
            this.state = 184;
            this.compoundStatement();
            this.state = 185;
            this.match(CParser.RightParen);
            break;

        case 7:
            this.enterOuterAlt(localctx, 7);
            this.state = 187;
            this.match(CParser.T__1);
            this.state = 188;
            this.match(CParser.LeftParen);
            this.state = 189;
            this.unaryExpression();
            this.state = 190;
            this.match(CParser.Comma);
            this.state = 191;
            this.typeName();
            this.state = 192;
            this.match(CParser.RightParen);
            break;

        case 8:
            this.enterOuterAlt(localctx, 8);
            this.state = 194;
            this.match(CParser.T__2);
            this.state = 195;
            this.match(CParser.LeftParen);
            this.state = 196;
            this.typeName();
            this.state = 197;
            this.match(CParser.Comma);
            this.state = 198;
            this.unaryExpression();
            this.state = 199;
            this.match(CParser.RightParen);
            break;

        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function GenericSelectionContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_genericSelection;
    return this;
}

GenericSelectionContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
GenericSelectionContext.prototype.constructor = GenericSelectionContext;

GenericSelectionContext.prototype.assignmentExpression = function() {
    return this.getTypedRuleContext(AssignmentExpressionContext,0);
};

GenericSelectionContext.prototype.genericAssocList = function() {
    return this.getTypedRuleContext(GenericAssocListContext,0);
};

GenericSelectionContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterGenericSelection(this);
	}
};

GenericSelectionContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitGenericSelection(this);
	}
};




CParser.GenericSelectionContext = GenericSelectionContext;

CParser.prototype.genericSelection = function() {

    var localctx = new GenericSelectionContext(this, this._ctx, this.state);
    this.enterRule(localctx, 2, CParser.RULE_genericSelection);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 203;
        this.match(CParser.Generic);
        this.state = 204;
        this.match(CParser.LeftParen);
        this.state = 205;
        this.assignmentExpression();
        this.state = 206;
        this.match(CParser.Comma);
        this.state = 207;
        this.genericAssocList(0);
        this.state = 208;
        this.match(CParser.RightParen);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function GenericAssocListContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_genericAssocList;
    return this;
}

GenericAssocListContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
GenericAssocListContext.prototype.constructor = GenericAssocListContext;

GenericAssocListContext.prototype.genericAssociation = function() {
    return this.getTypedRuleContext(GenericAssociationContext,0);
};

GenericAssocListContext.prototype.genericAssocList = function() {
    return this.getTypedRuleContext(GenericAssocListContext,0);
};

GenericAssocListContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterGenericAssocList(this);
	}
};

GenericAssocListContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitGenericAssocList(this);
	}
};



CParser.prototype.genericAssocList = function(_p) {
	if(_p===undefined) {
	    _p = 0;
	}
    var _parentctx = this._ctx;
    var _parentState = this.state;
    var localctx = new GenericAssocListContext(this, this._ctx, _parentState);
    var _prevctx = localctx;
    var _startState = 4;
    this.enterRecursionRule(localctx, 4, CParser.RULE_genericAssocList, _p);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 211;
        this.genericAssociation();
        this._ctx.stop = this._input.LT(-1);
        this.state = 218;
        this._errHandler.sync(this);
        var _alt = this._interp.adaptivePredict(this._input,3,this._ctx)
        while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
            if(_alt===1) {
                if(this._parseListeners!==null) {
                    this.triggerExitRuleEvent();
                }
                _prevctx = localctx;
                localctx = new GenericAssocListContext(this, _parentctx, _parentState);
                this.pushNewRecursionContext(localctx, _startState, CParser.RULE_genericAssocList);
                this.state = 213;
                if (!( this.precpred(this._ctx, 1))) {
                    throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 1)");
                }
                this.state = 214;
                this.match(CParser.Comma);
                this.state = 215;
                this.genericAssociation(); 
            }
            this.state = 220;
            this._errHandler.sync(this);
            _alt = this._interp.adaptivePredict(this._input,3,this._ctx);
        }

    } catch( error) {
        if(error instanceof antlr4.error.RecognitionException) {
	        localctx.exception = error;
	        this._errHandler.reportError(this, error);
	        this._errHandler.recover(this, error);
	    } else {
	    	throw error;
	    }
    } finally {
        this.unrollRecursionContexts(_parentctx)
    }
    return localctx;
};

function GenericAssociationContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_genericAssociation;
    return this;
}

GenericAssociationContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
GenericAssociationContext.prototype.constructor = GenericAssociationContext;

GenericAssociationContext.prototype.typeName = function() {
    return this.getTypedRuleContext(TypeNameContext,0);
};

GenericAssociationContext.prototype.assignmentExpression = function() {
    return this.getTypedRuleContext(AssignmentExpressionContext,0);
};

GenericAssociationContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterGenericAssociation(this);
	}
};

GenericAssociationContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitGenericAssociation(this);
	}
};




CParser.GenericAssociationContext = GenericAssociationContext;

CParser.prototype.genericAssociation = function() {

    var localctx = new GenericAssociationContext(this, this._ctx, this.state);
    this.enterRule(localctx, 6, CParser.RULE_genericAssociation);
    try {
        this.state = 228;
        switch(this._input.LA(1)) {
        case CParser.T__0:
        case CParser.T__3:
        case CParser.T__4:
        case CParser.T__5:
        case CParser.T__6:
        case CParser.Char:
        case CParser.Const:
        case CParser.Double:
        case CParser.Enum:
        case CParser.Float:
        case CParser.Int:
        case CParser.Long:
        case CParser.Restrict:
        case CParser.Short:
        case CParser.Signed:
        case CParser.Struct:
        case CParser.Union:
        case CParser.Unsigned:
        case CParser.Void:
        case CParser.Volatile:
        case CParser.Atomic:
        case CParser.Bool:
        case CParser.Complex:
        case CParser.Identifier:
            this.enterOuterAlt(localctx, 1);
            this.state = 221;
            this.typeName();
            this.state = 222;
            this.match(CParser.Colon);
            this.state = 223;
            this.assignmentExpression();
            break;
        case CParser.Default:
            this.enterOuterAlt(localctx, 2);
            this.state = 225;
            this.match(CParser.Default);
            this.state = 226;
            this.match(CParser.Colon);
            this.state = 227;
            this.assignmentExpression();
            break;
        default:
            throw new antlr4.error.NoViableAltException(this);
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function PostfixExpressionContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_postfixExpression;
    return this;
}

PostfixExpressionContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
PostfixExpressionContext.prototype.constructor = PostfixExpressionContext;

PostfixExpressionContext.prototype.primaryExpression = function() {
    return this.getTypedRuleContext(PrimaryExpressionContext,0);
};

PostfixExpressionContext.prototype.typeName = function() {
    return this.getTypedRuleContext(TypeNameContext,0);
};

PostfixExpressionContext.prototype.initializerList = function() {
    return this.getTypedRuleContext(InitializerListContext,0);
};

PostfixExpressionContext.prototype.postfixExpression = function() {
    return this.getTypedRuleContext(PostfixExpressionContext,0);
};

PostfixExpressionContext.prototype.expression = function() {
    return this.getTypedRuleContext(ExpressionContext,0);
};

PostfixExpressionContext.prototype.argumentExpressionList = function() {
    return this.getTypedRuleContext(ArgumentExpressionListContext,0);
};

PostfixExpressionContext.prototype.Identifier = function() {
    return this.getToken(CParser.Identifier, 0);
};

PostfixExpressionContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterPostfixExpression(this);
	}
};

PostfixExpressionContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitPostfixExpression(this);
	}
};



CParser.prototype.postfixExpression = function(_p) {
	if(_p===undefined) {
	    _p = 0;
	}
    var _parentctx = this._ctx;
    var _parentState = this.state;
    var localctx = new PostfixExpressionContext(this, this._ctx, _parentState);
    var _prevctx = localctx;
    var _startState = 8;
    this.enterRecursionRule(localctx, 8, CParser.RULE_postfixExpression, _p);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 264;
        var la_ = this._interp.adaptivePredict(this._input,5,this._ctx);
        switch(la_) {
        case 1:
            this.state = 231;
            this.primaryExpression();
            break;

        case 2:
            this.state = 232;
            this.match(CParser.LeftParen);
            this.state = 233;
            this.typeName();
            this.state = 234;
            this.match(CParser.RightParen);
            this.state = 235;
            this.match(CParser.LeftBrace);
            this.state = 236;
            this.initializerList(0);
            this.state = 237;
            this.match(CParser.RightBrace);
            break;

        case 3:
            this.state = 239;
            this.match(CParser.LeftParen);
            this.state = 240;
            this.typeName();
            this.state = 241;
            this.match(CParser.RightParen);
            this.state = 242;
            this.match(CParser.LeftBrace);
            this.state = 243;
            this.initializerList(0);
            this.state = 244;
            this.match(CParser.Comma);
            this.state = 245;
            this.match(CParser.RightBrace);
            break;

        case 4:
            this.state = 247;
            this.match(CParser.T__0);
            this.state = 248;
            this.match(CParser.LeftParen);
            this.state = 249;
            this.typeName();
            this.state = 250;
            this.match(CParser.RightParen);
            this.state = 251;
            this.match(CParser.LeftBrace);
            this.state = 252;
            this.initializerList(0);
            this.state = 253;
            this.match(CParser.RightBrace);
            break;

        case 5:
            this.state = 255;
            this.match(CParser.T__0);
            this.state = 256;
            this.match(CParser.LeftParen);
            this.state = 257;
            this.typeName();
            this.state = 258;
            this.match(CParser.RightParen);
            this.state = 259;
            this.match(CParser.LeftBrace);
            this.state = 260;
            this.initializerList(0);
            this.state = 261;
            this.match(CParser.Comma);
            this.state = 262;
            this.match(CParser.RightBrace);
            break;

        }
        this._ctx.stop = this._input.LT(-1);
        this.state = 289;
        this._errHandler.sync(this);
        var _alt = this._interp.adaptivePredict(this._input,8,this._ctx)
        while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
            if(_alt===1) {
                if(this._parseListeners!==null) {
                    this.triggerExitRuleEvent();
                }
                _prevctx = localctx;
                this.state = 287;
                var la_ = this._interp.adaptivePredict(this._input,7,this._ctx);
                switch(la_) {
                case 1:
                    localctx = new PostfixExpressionContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, CParser.RULE_postfixExpression);
                    this.state = 266;
                    if (!( this.precpred(this._ctx, 10))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 10)");
                    }
                    this.state = 267;
                    this.match(CParser.LeftBracket);
                    this.state = 268;
                    this.expression(0);
                    this.state = 269;
                    this.match(CParser.RightBracket);
                    break;

                case 2:
                    localctx = new PostfixExpressionContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, CParser.RULE_postfixExpression);
                    this.state = 271;
                    if (!( this.precpred(this._ctx, 9))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 9)");
                    }
                    this.state = 272;
                    this.match(CParser.LeftParen);
                    this.state = 274;
                    _la = this._input.LA(1);
                    if((((_la) & ~0x1f) == 0 && ((1 << _la) & ((1 << CParser.T__0) | (1 << CParser.T__1) | (1 << CParser.T__2))) !== 0) || ((((_la - 39)) & ~0x1f) == 0 && ((1 << (_la - 39)) & ((1 << (CParser.Sizeof - 39)) | (1 << (CParser.Alignof - 39)) | (1 << (CParser.Generic - 39)) | (1 << (CParser.LeftParen - 39)))) !== 0) || ((((_la - 71)) & ~0x1f) == 0 && ((1 << (_la - 71)) & ((1 << (CParser.Plus - 71)) | (1 << (CParser.PlusPlus - 71)) | (1 << (CParser.Minus - 71)) | (1 << (CParser.MinusMinus - 71)) | (1 << (CParser.Star - 71)) | (1 << (CParser.And - 71)) | (1 << (CParser.AndAnd - 71)) | (1 << (CParser.Not - 71)) | (1 << (CParser.Tilde - 71)))) !== 0) || ((((_la - 105)) & ~0x1f) == 0 && ((1 << (_la - 105)) & ((1 << (CParser.Identifier - 105)) | (1 << (CParser.Constant - 105)) | (1 << (CParser.StringLiteral - 105)))) !== 0)) {
                        this.state = 273;
                        this.argumentExpressionList(0);
                    }

                    this.state = 276;
                    this.match(CParser.RightParen);
                    break;

                case 3:
                    localctx = new PostfixExpressionContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, CParser.RULE_postfixExpression);
                    this.state = 277;
                    if (!( this.precpred(this._ctx, 8))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 8)");
                    }
                    this.state = 278;
                    this.match(CParser.Dot);
                    this.state = 279;
                    this.match(CParser.Identifier);
                    break;

                case 4:
                    localctx = new PostfixExpressionContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, CParser.RULE_postfixExpression);
                    this.state = 280;
                    if (!( this.precpred(this._ctx, 7))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 7)");
                    }
                    this.state = 281;
                    this.match(CParser.Arrow);
                    this.state = 282;
                    this.match(CParser.Identifier);
                    break;

                case 5:
                    localctx = new PostfixExpressionContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, CParser.RULE_postfixExpression);
                    this.state = 283;
                    if (!( this.precpred(this._ctx, 6))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 6)");
                    }
                    this.state = 284;
                    this.match(CParser.PlusPlus);
                    break;

                case 6:
                    localctx = new PostfixExpressionContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, CParser.RULE_postfixExpression);
                    this.state = 285;
                    if (!( this.precpred(this._ctx, 5))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 5)");
                    }
                    this.state = 286;
                    this.match(CParser.MinusMinus);
                    break;

                } 
            }
            this.state = 291;
            this._errHandler.sync(this);
            _alt = this._interp.adaptivePredict(this._input,8,this._ctx);
        }

    } catch( error) {
        if(error instanceof antlr4.error.RecognitionException) {
	        localctx.exception = error;
	        this._errHandler.reportError(this, error);
	        this._errHandler.recover(this, error);
	    } else {
	    	throw error;
	    }
    } finally {
        this.unrollRecursionContexts(_parentctx)
    }
    return localctx;
};

function ArgumentExpressionListContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_argumentExpressionList;
    return this;
}

ArgumentExpressionListContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
ArgumentExpressionListContext.prototype.constructor = ArgumentExpressionListContext;

ArgumentExpressionListContext.prototype.assignmentExpression = function() {
    return this.getTypedRuleContext(AssignmentExpressionContext,0);
};

ArgumentExpressionListContext.prototype.argumentExpressionList = function() {
    return this.getTypedRuleContext(ArgumentExpressionListContext,0);
};

ArgumentExpressionListContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterArgumentExpressionList(this);
	}
};

ArgumentExpressionListContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitArgumentExpressionList(this);
	}
};



CParser.prototype.argumentExpressionList = function(_p) {
	if(_p===undefined) {
	    _p = 0;
	}
    var _parentctx = this._ctx;
    var _parentState = this.state;
    var localctx = new ArgumentExpressionListContext(this, this._ctx, _parentState);
    var _prevctx = localctx;
    var _startState = 10;
    this.enterRecursionRule(localctx, 10, CParser.RULE_argumentExpressionList, _p);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 293;
        this.assignmentExpression();
        this._ctx.stop = this._input.LT(-1);
        this.state = 300;
        this._errHandler.sync(this);
        var _alt = this._interp.adaptivePredict(this._input,9,this._ctx)
        while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
            if(_alt===1) {
                if(this._parseListeners!==null) {
                    this.triggerExitRuleEvent();
                }
                _prevctx = localctx;
                localctx = new ArgumentExpressionListContext(this, _parentctx, _parentState);
                this.pushNewRecursionContext(localctx, _startState, CParser.RULE_argumentExpressionList);
                this.state = 295;
                if (!( this.precpred(this._ctx, 1))) {
                    throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 1)");
                }
                this.state = 296;
                this.match(CParser.Comma);
                this.state = 297;
                this.assignmentExpression(); 
            }
            this.state = 302;
            this._errHandler.sync(this);
            _alt = this._interp.adaptivePredict(this._input,9,this._ctx);
        }

    } catch( error) {
        if(error instanceof antlr4.error.RecognitionException) {
	        localctx.exception = error;
	        this._errHandler.reportError(this, error);
	        this._errHandler.recover(this, error);
	    } else {
	    	throw error;
	    }
    } finally {
        this.unrollRecursionContexts(_parentctx)
    }
    return localctx;
};

function UnaryExpressionContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_unaryExpression;
    return this;
}

UnaryExpressionContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
UnaryExpressionContext.prototype.constructor = UnaryExpressionContext;

UnaryExpressionContext.prototype.postfixExpression = function() {
    return this.getTypedRuleContext(PostfixExpressionContext,0);
};

UnaryExpressionContext.prototype.unaryExpression = function() {
    return this.getTypedRuleContext(UnaryExpressionContext,0);
};

UnaryExpressionContext.prototype.unaryOperator = function() {
    return this.getTypedRuleContext(UnaryOperatorContext,0);
};

UnaryExpressionContext.prototype.castExpression = function() {
    return this.getTypedRuleContext(CastExpressionContext,0);
};

UnaryExpressionContext.prototype.typeName = function() {
    return this.getTypedRuleContext(TypeNameContext,0);
};

UnaryExpressionContext.prototype.Identifier = function() {
    return this.getToken(CParser.Identifier, 0);
};

UnaryExpressionContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterUnaryExpression(this);
	}
};

UnaryExpressionContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitUnaryExpression(this);
	}
};




CParser.UnaryExpressionContext = UnaryExpressionContext;

CParser.prototype.unaryExpression = function() {

    var localctx = new UnaryExpressionContext(this, this._ctx, this.state);
    this.enterRule(localctx, 12, CParser.RULE_unaryExpression);
    try {
        this.state = 325;
        var la_ = this._interp.adaptivePredict(this._input,10,this._ctx);
        switch(la_) {
        case 1:
            this.enterOuterAlt(localctx, 1);
            this.state = 303;
            this.postfixExpression(0);
            break;

        case 2:
            this.enterOuterAlt(localctx, 2);
            this.state = 304;
            this.match(CParser.PlusPlus);
            this.state = 305;
            this.unaryExpression();
            break;

        case 3:
            this.enterOuterAlt(localctx, 3);
            this.state = 306;
            this.match(CParser.MinusMinus);
            this.state = 307;
            this.unaryExpression();
            break;

        case 4:
            this.enterOuterAlt(localctx, 4);
            this.state = 308;
            this.unaryOperator();
            this.state = 309;
            this.castExpression();
            break;

        case 5:
            this.enterOuterAlt(localctx, 5);
            this.state = 311;
            this.match(CParser.Sizeof);
            this.state = 312;
            this.unaryExpression();
            break;

        case 6:
            this.enterOuterAlt(localctx, 6);
            this.state = 313;
            this.match(CParser.Sizeof);
            this.state = 314;
            this.match(CParser.LeftParen);
            this.state = 315;
            this.typeName();
            this.state = 316;
            this.match(CParser.RightParen);
            break;

        case 7:
            this.enterOuterAlt(localctx, 7);
            this.state = 318;
            this.match(CParser.Alignof);
            this.state = 319;
            this.match(CParser.LeftParen);
            this.state = 320;
            this.typeName();
            this.state = 321;
            this.match(CParser.RightParen);
            break;

        case 8:
            this.enterOuterAlt(localctx, 8);
            this.state = 323;
            this.match(CParser.AndAnd);
            this.state = 324;
            this.match(CParser.Identifier);
            break;

        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function UnaryOperatorContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_unaryOperator;
    return this;
}

UnaryOperatorContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
UnaryOperatorContext.prototype.constructor = UnaryOperatorContext;


UnaryOperatorContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterUnaryOperator(this);
	}
};

UnaryOperatorContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitUnaryOperator(this);
	}
};




CParser.UnaryOperatorContext = UnaryOperatorContext;

CParser.prototype.unaryOperator = function() {

    var localctx = new UnaryOperatorContext(this, this._ctx, this.state);
    this.enterRule(localctx, 14, CParser.RULE_unaryOperator);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 327;
        _la = this._input.LA(1);
        if(!(((((_la - 71)) & ~0x1f) == 0 && ((1 << (_la - 71)) & ((1 << (CParser.Plus - 71)) | (1 << (CParser.Minus - 71)) | (1 << (CParser.Star - 71)) | (1 << (CParser.And - 71)) | (1 << (CParser.Not - 71)) | (1 << (CParser.Tilde - 71)))) !== 0))) {
        this._errHandler.recoverInline(this);
        }
        else {
            this.consume();
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function CastExpressionContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_castExpression;
    return this;
}

CastExpressionContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
CastExpressionContext.prototype.constructor = CastExpressionContext;

CastExpressionContext.prototype.unaryExpression = function() {
    return this.getTypedRuleContext(UnaryExpressionContext,0);
};

CastExpressionContext.prototype.typeName = function() {
    return this.getTypedRuleContext(TypeNameContext,0);
};

CastExpressionContext.prototype.castExpression = function() {
    return this.getTypedRuleContext(CastExpressionContext,0);
};

CastExpressionContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterCastExpression(this);
	}
};

CastExpressionContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitCastExpression(this);
	}
};




CParser.CastExpressionContext = CastExpressionContext;

CParser.prototype.castExpression = function() {

    var localctx = new CastExpressionContext(this, this._ctx, this.state);
    this.enterRule(localctx, 16, CParser.RULE_castExpression);
    try {
        this.state = 341;
        var la_ = this._interp.adaptivePredict(this._input,11,this._ctx);
        switch(la_) {
        case 1:
            this.enterOuterAlt(localctx, 1);
            this.state = 329;
            this.unaryExpression();
            break;

        case 2:
            this.enterOuterAlt(localctx, 2);
            this.state = 330;
            this.match(CParser.LeftParen);
            this.state = 331;
            this.typeName();
            this.state = 332;
            this.match(CParser.RightParen);
            this.state = 333;
            this.castExpression();
            break;

        case 3:
            this.enterOuterAlt(localctx, 3);
            this.state = 335;
            this.match(CParser.T__0);
            this.state = 336;
            this.match(CParser.LeftParen);
            this.state = 337;
            this.typeName();
            this.state = 338;
            this.match(CParser.RightParen);
            this.state = 339;
            this.castExpression();
            break;

        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function MultiplicativeExpressionContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_multiplicativeExpression;
    return this;
}

MultiplicativeExpressionContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
MultiplicativeExpressionContext.prototype.constructor = MultiplicativeExpressionContext;

MultiplicativeExpressionContext.prototype.castExpression = function() {
    return this.getTypedRuleContext(CastExpressionContext,0);
};

MultiplicativeExpressionContext.prototype.multiplicativeExpression = function() {
    return this.getTypedRuleContext(MultiplicativeExpressionContext,0);
};

MultiplicativeExpressionContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterMultiplicativeExpression(this);
	}
};

MultiplicativeExpressionContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitMultiplicativeExpression(this);
	}
};



CParser.prototype.multiplicativeExpression = function(_p) {
	if(_p===undefined) {
	    _p = 0;
	}
    var _parentctx = this._ctx;
    var _parentState = this.state;
    var localctx = new MultiplicativeExpressionContext(this, this._ctx, _parentState);
    var _prevctx = localctx;
    var _startState = 18;
    this.enterRecursionRule(localctx, 18, CParser.RULE_multiplicativeExpression, _p);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 344;
        this.castExpression();
        this._ctx.stop = this._input.LT(-1);
        this.state = 357;
        this._errHandler.sync(this);
        var _alt = this._interp.adaptivePredict(this._input,13,this._ctx)
        while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
            if(_alt===1) {
                if(this._parseListeners!==null) {
                    this.triggerExitRuleEvent();
                }
                _prevctx = localctx;
                this.state = 355;
                var la_ = this._interp.adaptivePredict(this._input,12,this._ctx);
                switch(la_) {
                case 1:
                    localctx = new MultiplicativeExpressionContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, CParser.RULE_multiplicativeExpression);
                    this.state = 346;
                    if (!( this.precpred(this._ctx, 3))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 3)");
                    }
                    this.state = 347;
                    this.match(CParser.Star);
                    this.state = 348;
                    this.castExpression();
                    break;

                case 2:
                    localctx = new MultiplicativeExpressionContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, CParser.RULE_multiplicativeExpression);
                    this.state = 349;
                    if (!( this.precpred(this._ctx, 2))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 2)");
                    }
                    this.state = 350;
                    this.match(CParser.Div);
                    this.state = 351;
                    this.castExpression();
                    break;

                case 3:
                    localctx = new MultiplicativeExpressionContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, CParser.RULE_multiplicativeExpression);
                    this.state = 352;
                    if (!( this.precpred(this._ctx, 1))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 1)");
                    }
                    this.state = 353;
                    this.match(CParser.Mod);
                    this.state = 354;
                    this.castExpression();
                    break;

                } 
            }
            this.state = 359;
            this._errHandler.sync(this);
            _alt = this._interp.adaptivePredict(this._input,13,this._ctx);
        }

    } catch( error) {
        if(error instanceof antlr4.error.RecognitionException) {
	        localctx.exception = error;
	        this._errHandler.reportError(this, error);
	        this._errHandler.recover(this, error);
	    } else {
	    	throw error;
	    }
    } finally {
        this.unrollRecursionContexts(_parentctx)
    }
    return localctx;
};

function AdditiveExpressionContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_additiveExpression;
    return this;
}

AdditiveExpressionContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
AdditiveExpressionContext.prototype.constructor = AdditiveExpressionContext;

AdditiveExpressionContext.prototype.multiplicativeExpression = function() {
    return this.getTypedRuleContext(MultiplicativeExpressionContext,0);
};

AdditiveExpressionContext.prototype.additiveExpression = function() {
    return this.getTypedRuleContext(AdditiveExpressionContext,0);
};

AdditiveExpressionContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterAdditiveExpression(this);
	}
};

AdditiveExpressionContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitAdditiveExpression(this);
	}
};



CParser.prototype.additiveExpression = function(_p) {
	if(_p===undefined) {
	    _p = 0;
	}
    var _parentctx = this._ctx;
    var _parentState = this.state;
    var localctx = new AdditiveExpressionContext(this, this._ctx, _parentState);
    var _prevctx = localctx;
    var _startState = 20;
    this.enterRecursionRule(localctx, 20, CParser.RULE_additiveExpression, _p);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 361;
        this.multiplicativeExpression(0);
        this._ctx.stop = this._input.LT(-1);
        this.state = 371;
        this._errHandler.sync(this);
        var _alt = this._interp.adaptivePredict(this._input,15,this._ctx)
        while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
            if(_alt===1) {
                if(this._parseListeners!==null) {
                    this.triggerExitRuleEvent();
                }
                _prevctx = localctx;
                this.state = 369;
                var la_ = this._interp.adaptivePredict(this._input,14,this._ctx);
                switch(la_) {
                case 1:
                    localctx = new AdditiveExpressionContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, CParser.RULE_additiveExpression);
                    this.state = 363;
                    if (!( this.precpred(this._ctx, 2))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 2)");
                    }
                    this.state = 364;
                    this.match(CParser.Plus);
                    this.state = 365;
                    this.multiplicativeExpression(0);
                    break;

                case 2:
                    localctx = new AdditiveExpressionContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, CParser.RULE_additiveExpression);
                    this.state = 366;
                    if (!( this.precpred(this._ctx, 1))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 1)");
                    }
                    this.state = 367;
                    this.match(CParser.Minus);
                    this.state = 368;
                    this.multiplicativeExpression(0);
                    break;

                } 
            }
            this.state = 373;
            this._errHandler.sync(this);
            _alt = this._interp.adaptivePredict(this._input,15,this._ctx);
        }

    } catch( error) {
        if(error instanceof antlr4.error.RecognitionException) {
	        localctx.exception = error;
	        this._errHandler.reportError(this, error);
	        this._errHandler.recover(this, error);
	    } else {
	    	throw error;
	    }
    } finally {
        this.unrollRecursionContexts(_parentctx)
    }
    return localctx;
};

function ShiftExpressionContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_shiftExpression;
    return this;
}

ShiftExpressionContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
ShiftExpressionContext.prototype.constructor = ShiftExpressionContext;

ShiftExpressionContext.prototype.additiveExpression = function() {
    return this.getTypedRuleContext(AdditiveExpressionContext,0);
};

ShiftExpressionContext.prototype.shiftExpression = function() {
    return this.getTypedRuleContext(ShiftExpressionContext,0);
};

ShiftExpressionContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterShiftExpression(this);
	}
};

ShiftExpressionContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitShiftExpression(this);
	}
};



CParser.prototype.shiftExpression = function(_p) {
	if(_p===undefined) {
	    _p = 0;
	}
    var _parentctx = this._ctx;
    var _parentState = this.state;
    var localctx = new ShiftExpressionContext(this, this._ctx, _parentState);
    var _prevctx = localctx;
    var _startState = 22;
    this.enterRecursionRule(localctx, 22, CParser.RULE_shiftExpression, _p);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 375;
        this.additiveExpression(0);
        this._ctx.stop = this._input.LT(-1);
        this.state = 385;
        this._errHandler.sync(this);
        var _alt = this._interp.adaptivePredict(this._input,17,this._ctx)
        while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
            if(_alt===1) {
                if(this._parseListeners!==null) {
                    this.triggerExitRuleEvent();
                }
                _prevctx = localctx;
                this.state = 383;
                var la_ = this._interp.adaptivePredict(this._input,16,this._ctx);
                switch(la_) {
                case 1:
                    localctx = new ShiftExpressionContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, CParser.RULE_shiftExpression);
                    this.state = 377;
                    if (!( this.precpred(this._ctx, 2))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 2)");
                    }
                    this.state = 378;
                    this.match(CParser.LeftShift);
                    this.state = 379;
                    this.additiveExpression(0);
                    break;

                case 2:
                    localctx = new ShiftExpressionContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, CParser.RULE_shiftExpression);
                    this.state = 380;
                    if (!( this.precpred(this._ctx, 1))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 1)");
                    }
                    this.state = 381;
                    this.match(CParser.RightShift);
                    this.state = 382;
                    this.additiveExpression(0);
                    break;

                } 
            }
            this.state = 387;
            this._errHandler.sync(this);
            _alt = this._interp.adaptivePredict(this._input,17,this._ctx);
        }

    } catch( error) {
        if(error instanceof antlr4.error.RecognitionException) {
	        localctx.exception = error;
	        this._errHandler.reportError(this, error);
	        this._errHandler.recover(this, error);
	    } else {
	    	throw error;
	    }
    } finally {
        this.unrollRecursionContexts(_parentctx)
    }
    return localctx;
};

function RelationalExpressionContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_relationalExpression;
    return this;
}

RelationalExpressionContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
RelationalExpressionContext.prototype.constructor = RelationalExpressionContext;

RelationalExpressionContext.prototype.shiftExpression = function() {
    return this.getTypedRuleContext(ShiftExpressionContext,0);
};

RelationalExpressionContext.prototype.relationalExpression = function() {
    return this.getTypedRuleContext(RelationalExpressionContext,0);
};

RelationalExpressionContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterRelationalExpression(this);
	}
};

RelationalExpressionContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitRelationalExpression(this);
	}
};



CParser.prototype.relationalExpression = function(_p) {
	if(_p===undefined) {
	    _p = 0;
	}
    var _parentctx = this._ctx;
    var _parentState = this.state;
    var localctx = new RelationalExpressionContext(this, this._ctx, _parentState);
    var _prevctx = localctx;
    var _startState = 24;
    this.enterRecursionRule(localctx, 24, CParser.RULE_relationalExpression, _p);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 389;
        this.shiftExpression(0);
        this._ctx.stop = this._input.LT(-1);
        this.state = 405;
        this._errHandler.sync(this);
        var _alt = this._interp.adaptivePredict(this._input,19,this._ctx)
        while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
            if(_alt===1) {
                if(this._parseListeners!==null) {
                    this.triggerExitRuleEvent();
                }
                _prevctx = localctx;
                this.state = 403;
                var la_ = this._interp.adaptivePredict(this._input,18,this._ctx);
                switch(la_) {
                case 1:
                    localctx = new RelationalExpressionContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, CParser.RULE_relationalExpression);
                    this.state = 391;
                    if (!( this.precpred(this._ctx, 4))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 4)");
                    }
                    this.state = 392;
                    this.match(CParser.Less);
                    this.state = 393;
                    this.shiftExpression(0);
                    break;

                case 2:
                    localctx = new RelationalExpressionContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, CParser.RULE_relationalExpression);
                    this.state = 394;
                    if (!( this.precpred(this._ctx, 3))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 3)");
                    }
                    this.state = 395;
                    this.match(CParser.Greater);
                    this.state = 396;
                    this.shiftExpression(0);
                    break;

                case 3:
                    localctx = new RelationalExpressionContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, CParser.RULE_relationalExpression);
                    this.state = 397;
                    if (!( this.precpred(this._ctx, 2))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 2)");
                    }
                    this.state = 398;
                    this.match(CParser.LessEqual);
                    this.state = 399;
                    this.shiftExpression(0);
                    break;

                case 4:
                    localctx = new RelationalExpressionContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, CParser.RULE_relationalExpression);
                    this.state = 400;
                    if (!( this.precpred(this._ctx, 1))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 1)");
                    }
                    this.state = 401;
                    this.match(CParser.GreaterEqual);
                    this.state = 402;
                    this.shiftExpression(0);
                    break;

                } 
            }
            this.state = 407;
            this._errHandler.sync(this);
            _alt = this._interp.adaptivePredict(this._input,19,this._ctx);
        }

    } catch( error) {
        if(error instanceof antlr4.error.RecognitionException) {
	        localctx.exception = error;
	        this._errHandler.reportError(this, error);
	        this._errHandler.recover(this, error);
	    } else {
	    	throw error;
	    }
    } finally {
        this.unrollRecursionContexts(_parentctx)
    }
    return localctx;
};

function EqualityExpressionContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_equalityExpression;
    return this;
}

EqualityExpressionContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
EqualityExpressionContext.prototype.constructor = EqualityExpressionContext;

EqualityExpressionContext.prototype.relationalExpression = function() {
    return this.getTypedRuleContext(RelationalExpressionContext,0);
};

EqualityExpressionContext.prototype.equalityExpression = function() {
    return this.getTypedRuleContext(EqualityExpressionContext,0);
};

EqualityExpressionContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterEqualityExpression(this);
	}
};

EqualityExpressionContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitEqualityExpression(this);
	}
};



CParser.prototype.equalityExpression = function(_p) {
	if(_p===undefined) {
	    _p = 0;
	}
    var _parentctx = this._ctx;
    var _parentState = this.state;
    var localctx = new EqualityExpressionContext(this, this._ctx, _parentState);
    var _prevctx = localctx;
    var _startState = 26;
    this.enterRecursionRule(localctx, 26, CParser.RULE_equalityExpression, _p);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 409;
        this.relationalExpression(0);
        this._ctx.stop = this._input.LT(-1);
        this.state = 419;
        this._errHandler.sync(this);
        var _alt = this._interp.adaptivePredict(this._input,21,this._ctx)
        while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
            if(_alt===1) {
                if(this._parseListeners!==null) {
                    this.triggerExitRuleEvent();
                }
                _prevctx = localctx;
                this.state = 417;
                var la_ = this._interp.adaptivePredict(this._input,20,this._ctx);
                switch(la_) {
                case 1:
                    localctx = new EqualityExpressionContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, CParser.RULE_equalityExpression);
                    this.state = 411;
                    if (!( this.precpred(this._ctx, 2))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 2)");
                    }
                    this.state = 412;
                    this.match(CParser.Equal);
                    this.state = 413;
                    this.relationalExpression(0);
                    break;

                case 2:
                    localctx = new EqualityExpressionContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, CParser.RULE_equalityExpression);
                    this.state = 414;
                    if (!( this.precpred(this._ctx, 1))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 1)");
                    }
                    this.state = 415;
                    this.match(CParser.NotEqual);
                    this.state = 416;
                    this.relationalExpression(0);
                    break;

                } 
            }
            this.state = 421;
            this._errHandler.sync(this);
            _alt = this._interp.adaptivePredict(this._input,21,this._ctx);
        }

    } catch( error) {
        if(error instanceof antlr4.error.RecognitionException) {
	        localctx.exception = error;
	        this._errHandler.reportError(this, error);
	        this._errHandler.recover(this, error);
	    } else {
	    	throw error;
	    }
    } finally {
        this.unrollRecursionContexts(_parentctx)
    }
    return localctx;
};

function AndExpressionContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_andExpression;
    return this;
}

AndExpressionContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
AndExpressionContext.prototype.constructor = AndExpressionContext;

AndExpressionContext.prototype.equalityExpression = function() {
    return this.getTypedRuleContext(EqualityExpressionContext,0);
};

AndExpressionContext.prototype.andExpression = function() {
    return this.getTypedRuleContext(AndExpressionContext,0);
};

AndExpressionContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterAndExpression(this);
	}
};

AndExpressionContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitAndExpression(this);
	}
};



CParser.prototype.andExpression = function(_p) {
	if(_p===undefined) {
	    _p = 0;
	}
    var _parentctx = this._ctx;
    var _parentState = this.state;
    var localctx = new AndExpressionContext(this, this._ctx, _parentState);
    var _prevctx = localctx;
    var _startState = 28;
    this.enterRecursionRule(localctx, 28, CParser.RULE_andExpression, _p);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 423;
        this.equalityExpression(0);
        this._ctx.stop = this._input.LT(-1);
        this.state = 430;
        this._errHandler.sync(this);
        var _alt = this._interp.adaptivePredict(this._input,22,this._ctx)
        while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
            if(_alt===1) {
                if(this._parseListeners!==null) {
                    this.triggerExitRuleEvent();
                }
                _prevctx = localctx;
                localctx = new AndExpressionContext(this, _parentctx, _parentState);
                this.pushNewRecursionContext(localctx, _startState, CParser.RULE_andExpression);
                this.state = 425;
                if (!( this.precpred(this._ctx, 1))) {
                    throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 1)");
                }
                this.state = 426;
                this.match(CParser.And);
                this.state = 427;
                this.equalityExpression(0); 
            }
            this.state = 432;
            this._errHandler.sync(this);
            _alt = this._interp.adaptivePredict(this._input,22,this._ctx);
        }

    } catch( error) {
        if(error instanceof antlr4.error.RecognitionException) {
	        localctx.exception = error;
	        this._errHandler.reportError(this, error);
	        this._errHandler.recover(this, error);
	    } else {
	    	throw error;
	    }
    } finally {
        this.unrollRecursionContexts(_parentctx)
    }
    return localctx;
};

function ExclusiveOrExpressionContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_exclusiveOrExpression;
    return this;
}

ExclusiveOrExpressionContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
ExclusiveOrExpressionContext.prototype.constructor = ExclusiveOrExpressionContext;

ExclusiveOrExpressionContext.prototype.andExpression = function() {
    return this.getTypedRuleContext(AndExpressionContext,0);
};

ExclusiveOrExpressionContext.prototype.exclusiveOrExpression = function() {
    return this.getTypedRuleContext(ExclusiveOrExpressionContext,0);
};

ExclusiveOrExpressionContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterExclusiveOrExpression(this);
	}
};

ExclusiveOrExpressionContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitExclusiveOrExpression(this);
	}
};



CParser.prototype.exclusiveOrExpression = function(_p) {
	if(_p===undefined) {
	    _p = 0;
	}
    var _parentctx = this._ctx;
    var _parentState = this.state;
    var localctx = new ExclusiveOrExpressionContext(this, this._ctx, _parentState);
    var _prevctx = localctx;
    var _startState = 30;
    this.enterRecursionRule(localctx, 30, CParser.RULE_exclusiveOrExpression, _p);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 434;
        this.andExpression(0);
        this._ctx.stop = this._input.LT(-1);
        this.state = 441;
        this._errHandler.sync(this);
        var _alt = this._interp.adaptivePredict(this._input,23,this._ctx)
        while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
            if(_alt===1) {
                if(this._parseListeners!==null) {
                    this.triggerExitRuleEvent();
                }
                _prevctx = localctx;
                localctx = new ExclusiveOrExpressionContext(this, _parentctx, _parentState);
                this.pushNewRecursionContext(localctx, _startState, CParser.RULE_exclusiveOrExpression);
                this.state = 436;
                if (!( this.precpred(this._ctx, 1))) {
                    throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 1)");
                }
                this.state = 437;
                this.match(CParser.Caret);
                this.state = 438;
                this.andExpression(0); 
            }
            this.state = 443;
            this._errHandler.sync(this);
            _alt = this._interp.adaptivePredict(this._input,23,this._ctx);
        }

    } catch( error) {
        if(error instanceof antlr4.error.RecognitionException) {
	        localctx.exception = error;
	        this._errHandler.reportError(this, error);
	        this._errHandler.recover(this, error);
	    } else {
	    	throw error;
	    }
    } finally {
        this.unrollRecursionContexts(_parentctx)
    }
    return localctx;
};

function InclusiveOrExpressionContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_inclusiveOrExpression;
    return this;
}

InclusiveOrExpressionContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
InclusiveOrExpressionContext.prototype.constructor = InclusiveOrExpressionContext;

InclusiveOrExpressionContext.prototype.exclusiveOrExpression = function() {
    return this.getTypedRuleContext(ExclusiveOrExpressionContext,0);
};

InclusiveOrExpressionContext.prototype.inclusiveOrExpression = function() {
    return this.getTypedRuleContext(InclusiveOrExpressionContext,0);
};

InclusiveOrExpressionContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterInclusiveOrExpression(this);
	}
};

InclusiveOrExpressionContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitInclusiveOrExpression(this);
	}
};



CParser.prototype.inclusiveOrExpression = function(_p) {
	if(_p===undefined) {
	    _p = 0;
	}
    var _parentctx = this._ctx;
    var _parentState = this.state;
    var localctx = new InclusiveOrExpressionContext(this, this._ctx, _parentState);
    var _prevctx = localctx;
    var _startState = 32;
    this.enterRecursionRule(localctx, 32, CParser.RULE_inclusiveOrExpression, _p);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 445;
        this.exclusiveOrExpression(0);
        this._ctx.stop = this._input.LT(-1);
        this.state = 452;
        this._errHandler.sync(this);
        var _alt = this._interp.adaptivePredict(this._input,24,this._ctx)
        while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
            if(_alt===1) {
                if(this._parseListeners!==null) {
                    this.triggerExitRuleEvent();
                }
                _prevctx = localctx;
                localctx = new InclusiveOrExpressionContext(this, _parentctx, _parentState);
                this.pushNewRecursionContext(localctx, _startState, CParser.RULE_inclusiveOrExpression);
                this.state = 447;
                if (!( this.precpred(this._ctx, 1))) {
                    throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 1)");
                }
                this.state = 448;
                this.match(CParser.Or);
                this.state = 449;
                this.exclusiveOrExpression(0); 
            }
            this.state = 454;
            this._errHandler.sync(this);
            _alt = this._interp.adaptivePredict(this._input,24,this._ctx);
        }

    } catch( error) {
        if(error instanceof antlr4.error.RecognitionException) {
	        localctx.exception = error;
	        this._errHandler.reportError(this, error);
	        this._errHandler.recover(this, error);
	    } else {
	    	throw error;
	    }
    } finally {
        this.unrollRecursionContexts(_parentctx)
    }
    return localctx;
};

function LogicalAndExpressionContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_logicalAndExpression;
    return this;
}

LogicalAndExpressionContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
LogicalAndExpressionContext.prototype.constructor = LogicalAndExpressionContext;

LogicalAndExpressionContext.prototype.inclusiveOrExpression = function() {
    return this.getTypedRuleContext(InclusiveOrExpressionContext,0);
};

LogicalAndExpressionContext.prototype.logicalAndExpression = function() {
    return this.getTypedRuleContext(LogicalAndExpressionContext,0);
};

LogicalAndExpressionContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterLogicalAndExpression(this);
	}
};

LogicalAndExpressionContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitLogicalAndExpression(this);
	}
};



CParser.prototype.logicalAndExpression = function(_p) {
	if(_p===undefined) {
	    _p = 0;
	}
    var _parentctx = this._ctx;
    var _parentState = this.state;
    var localctx = new LogicalAndExpressionContext(this, this._ctx, _parentState);
    var _prevctx = localctx;
    var _startState = 34;
    this.enterRecursionRule(localctx, 34, CParser.RULE_logicalAndExpression, _p);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 456;
        this.inclusiveOrExpression(0);
        this._ctx.stop = this._input.LT(-1);
        this.state = 463;
        this._errHandler.sync(this);
        var _alt = this._interp.adaptivePredict(this._input,25,this._ctx)
        while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
            if(_alt===1) {
                if(this._parseListeners!==null) {
                    this.triggerExitRuleEvent();
                }
                _prevctx = localctx;
                localctx = new LogicalAndExpressionContext(this, _parentctx, _parentState);
                this.pushNewRecursionContext(localctx, _startState, CParser.RULE_logicalAndExpression);
                this.state = 458;
                if (!( this.precpred(this._ctx, 1))) {
                    throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 1)");
                }
                this.state = 459;
                this.match(CParser.AndAnd);
                this.state = 460;
                this.inclusiveOrExpression(0); 
            }
            this.state = 465;
            this._errHandler.sync(this);
            _alt = this._interp.adaptivePredict(this._input,25,this._ctx);
        }

    } catch( error) {
        if(error instanceof antlr4.error.RecognitionException) {
	        localctx.exception = error;
	        this._errHandler.reportError(this, error);
	        this._errHandler.recover(this, error);
	    } else {
	    	throw error;
	    }
    } finally {
        this.unrollRecursionContexts(_parentctx)
    }
    return localctx;
};

function LogicalOrExpressionContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_logicalOrExpression;
    return this;
}

LogicalOrExpressionContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
LogicalOrExpressionContext.prototype.constructor = LogicalOrExpressionContext;

LogicalOrExpressionContext.prototype.logicalAndExpression = function() {
    return this.getTypedRuleContext(LogicalAndExpressionContext,0);
};

LogicalOrExpressionContext.prototype.logicalOrExpression = function() {
    return this.getTypedRuleContext(LogicalOrExpressionContext,0);
};

LogicalOrExpressionContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterLogicalOrExpression(this);
	}
};

LogicalOrExpressionContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitLogicalOrExpression(this);
	}
};



CParser.prototype.logicalOrExpression = function(_p) {
	if(_p===undefined) {
	    _p = 0;
	}
    var _parentctx = this._ctx;
    var _parentState = this.state;
    var localctx = new LogicalOrExpressionContext(this, this._ctx, _parentState);
    var _prevctx = localctx;
    var _startState = 36;
    this.enterRecursionRule(localctx, 36, CParser.RULE_logicalOrExpression, _p);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 467;
        this.logicalAndExpression(0);
        this._ctx.stop = this._input.LT(-1);
        this.state = 474;
        this._errHandler.sync(this);
        var _alt = this._interp.adaptivePredict(this._input,26,this._ctx)
        while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
            if(_alt===1) {
                if(this._parseListeners!==null) {
                    this.triggerExitRuleEvent();
                }
                _prevctx = localctx;
                localctx = new LogicalOrExpressionContext(this, _parentctx, _parentState);
                this.pushNewRecursionContext(localctx, _startState, CParser.RULE_logicalOrExpression);
                this.state = 469;
                if (!( this.precpred(this._ctx, 1))) {
                    throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 1)");
                }
                this.state = 470;
                this.match(CParser.OrOr);
                this.state = 471;
                this.logicalAndExpression(0); 
            }
            this.state = 476;
            this._errHandler.sync(this);
            _alt = this._interp.adaptivePredict(this._input,26,this._ctx);
        }

    } catch( error) {
        if(error instanceof antlr4.error.RecognitionException) {
	        localctx.exception = error;
	        this._errHandler.reportError(this, error);
	        this._errHandler.recover(this, error);
	    } else {
	    	throw error;
	    }
    } finally {
        this.unrollRecursionContexts(_parentctx)
    }
    return localctx;
};

function ConditionalExpressionContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_conditionalExpression;
    return this;
}

ConditionalExpressionContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
ConditionalExpressionContext.prototype.constructor = ConditionalExpressionContext;

ConditionalExpressionContext.prototype.logicalOrExpression = function() {
    return this.getTypedRuleContext(LogicalOrExpressionContext,0);
};

ConditionalExpressionContext.prototype.expression = function() {
    return this.getTypedRuleContext(ExpressionContext,0);
};

ConditionalExpressionContext.prototype.conditionalExpression = function() {
    return this.getTypedRuleContext(ConditionalExpressionContext,0);
};

ConditionalExpressionContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterConditionalExpression(this);
	}
};

ConditionalExpressionContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitConditionalExpression(this);
	}
};




CParser.ConditionalExpressionContext = ConditionalExpressionContext;

CParser.prototype.conditionalExpression = function() {

    var localctx = new ConditionalExpressionContext(this, this._ctx, this.state);
    this.enterRule(localctx, 38, CParser.RULE_conditionalExpression);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 477;
        this.logicalOrExpression(0);
        this.state = 483;
        var la_ = this._interp.adaptivePredict(this._input,27,this._ctx);
        if(la_===1) {
            this.state = 478;
            this.match(CParser.Question);
            this.state = 479;
            this.expression(0);
            this.state = 480;
            this.match(CParser.Colon);
            this.state = 481;
            this.conditionalExpression();

        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function AssignmentExpressionContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_assignmentExpression;
    return this;
}

AssignmentExpressionContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
AssignmentExpressionContext.prototype.constructor = AssignmentExpressionContext;

AssignmentExpressionContext.prototype.conditionalExpression = function() {
    return this.getTypedRuleContext(ConditionalExpressionContext,0);
};

AssignmentExpressionContext.prototype.unaryExpression = function() {
    return this.getTypedRuleContext(UnaryExpressionContext,0);
};

AssignmentExpressionContext.prototype.assignmentOperator = function() {
    return this.getTypedRuleContext(AssignmentOperatorContext,0);
};

AssignmentExpressionContext.prototype.assignmentExpression = function() {
    return this.getTypedRuleContext(AssignmentExpressionContext,0);
};

AssignmentExpressionContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterAssignmentExpression(this);
	}
};

AssignmentExpressionContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitAssignmentExpression(this);
	}
};




CParser.AssignmentExpressionContext = AssignmentExpressionContext;

CParser.prototype.assignmentExpression = function() {

    var localctx = new AssignmentExpressionContext(this, this._ctx, this.state);
    this.enterRule(localctx, 40, CParser.RULE_assignmentExpression);
    try {
        this.state = 490;
        var la_ = this._interp.adaptivePredict(this._input,28,this._ctx);
        switch(la_) {
        case 1:
            this.enterOuterAlt(localctx, 1);
            this.state = 485;
            this.conditionalExpression();
            break;

        case 2:
            this.enterOuterAlt(localctx, 2);
            this.state = 486;
            this.unaryExpression();
            this.state = 487;
            this.assignmentOperator();
            this.state = 488;
            this.assignmentExpression();
            break;

        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function AssignmentOperatorContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_assignmentOperator;
    return this;
}

AssignmentOperatorContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
AssignmentOperatorContext.prototype.constructor = AssignmentOperatorContext;


AssignmentOperatorContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterAssignmentOperator(this);
	}
};

AssignmentOperatorContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitAssignmentOperator(this);
	}
};




CParser.AssignmentOperatorContext = AssignmentOperatorContext;

CParser.prototype.assignmentOperator = function() {

    var localctx = new AssignmentOperatorContext(this, this._ctx, this.state);
    this.enterRule(localctx, 42, CParser.RULE_assignmentOperator);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 492;
        _la = this._input.LA(1);
        if(!(((((_la - 89)) & ~0x1f) == 0 && ((1 << (_la - 89)) & ((1 << (CParser.Assign - 89)) | (1 << (CParser.StarAssign - 89)) | (1 << (CParser.DivAssign - 89)) | (1 << (CParser.ModAssign - 89)) | (1 << (CParser.PlusAssign - 89)) | (1 << (CParser.MinusAssign - 89)) | (1 << (CParser.LeftShiftAssign - 89)) | (1 << (CParser.RightShiftAssign - 89)) | (1 << (CParser.AndAssign - 89)) | (1 << (CParser.XorAssign - 89)) | (1 << (CParser.OrAssign - 89)))) !== 0))) {
        this._errHandler.recoverInline(this);
        }
        else {
            this.consume();
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function ExpressionContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_expression;
    return this;
}

ExpressionContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
ExpressionContext.prototype.constructor = ExpressionContext;

ExpressionContext.prototype.assignmentExpression = function() {
    return this.getTypedRuleContext(AssignmentExpressionContext,0);
};

ExpressionContext.prototype.expression = function() {
    return this.getTypedRuleContext(ExpressionContext,0);
};

ExpressionContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterExpression(this);
	}
};

ExpressionContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitExpression(this);
	}
};



CParser.prototype.expression = function(_p) {
	if(_p===undefined) {
	    _p = 0;
	}
    var _parentctx = this._ctx;
    var _parentState = this.state;
    var localctx = new ExpressionContext(this, this._ctx, _parentState);
    var _prevctx = localctx;
    var _startState = 44;
    this.enterRecursionRule(localctx, 44, CParser.RULE_expression, _p);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 495;
        this.assignmentExpression();
        this._ctx.stop = this._input.LT(-1);
        this.state = 502;
        this._errHandler.sync(this);
        var _alt = this._interp.adaptivePredict(this._input,29,this._ctx)
        while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
            if(_alt===1) {
                if(this._parseListeners!==null) {
                    this.triggerExitRuleEvent();
                }
                _prevctx = localctx;
                localctx = new ExpressionContext(this, _parentctx, _parentState);
                this.pushNewRecursionContext(localctx, _startState, CParser.RULE_expression);
                this.state = 497;
                if (!( this.precpred(this._ctx, 1))) {
                    throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 1)");
                }
                this.state = 498;
                this.match(CParser.Comma);
                this.state = 499;
                this.assignmentExpression(); 
            }
            this.state = 504;
            this._errHandler.sync(this);
            _alt = this._interp.adaptivePredict(this._input,29,this._ctx);
        }

    } catch( error) {
        if(error instanceof antlr4.error.RecognitionException) {
	        localctx.exception = error;
	        this._errHandler.reportError(this, error);
	        this._errHandler.recover(this, error);
	    } else {
	    	throw error;
	    }
    } finally {
        this.unrollRecursionContexts(_parentctx)
    }
    return localctx;
};

function ConstantExpressionContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_constantExpression;
    return this;
}

ConstantExpressionContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
ConstantExpressionContext.prototype.constructor = ConstantExpressionContext;

ConstantExpressionContext.prototype.conditionalExpression = function() {
    return this.getTypedRuleContext(ConditionalExpressionContext,0);
};

ConstantExpressionContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterConstantExpression(this);
	}
};

ConstantExpressionContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitConstantExpression(this);
	}
};




CParser.ConstantExpressionContext = ConstantExpressionContext;

CParser.prototype.constantExpression = function() {

    var localctx = new ConstantExpressionContext(this, this._ctx, this.state);
    this.enterRule(localctx, 46, CParser.RULE_constantExpression);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 505;
        this.conditionalExpression();
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function DeclarationContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_declaration;
    return this;
}

DeclarationContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
DeclarationContext.prototype.constructor = DeclarationContext;

DeclarationContext.prototype.declarationSpecifiers = function() {
    return this.getTypedRuleContext(DeclarationSpecifiersContext,0);
};

DeclarationContext.prototype.initDeclaratorList = function() {
    return this.getTypedRuleContext(InitDeclaratorListContext,0);
};

DeclarationContext.prototype.staticAssertDeclaration = function() {
    return this.getTypedRuleContext(StaticAssertDeclarationContext,0);
};

DeclarationContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterDeclaration(this);
	}
};

DeclarationContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitDeclaration(this);
	}
};




CParser.DeclarationContext = DeclarationContext;

CParser.prototype.declaration = function() {

    var localctx = new DeclarationContext(this, this._ctx, this.state);
    this.enterRule(localctx, 48, CParser.RULE_declaration);
    var _la = 0; // Token type
    try {
        this.state = 514;
        switch(this._input.LA(1)) {
        case CParser.T__0:
        case CParser.T__3:
        case CParser.T__4:
        case CParser.T__5:
        case CParser.T__6:
        case CParser.T__7:
        case CParser.T__8:
        case CParser.T__9:
        case CParser.T__11:
        case CParser.Auto:
        case CParser.Char:
        case CParser.Const:
        case CParser.Double:
        case CParser.Enum:
        case CParser.Extern:
        case CParser.Float:
        case CParser.Inline:
        case CParser.Int:
        case CParser.Long:
        case CParser.Register:
        case CParser.Restrict:
        case CParser.Short:
        case CParser.Signed:
        case CParser.Static:
        case CParser.Struct:
        case CParser.Typedef:
        case CParser.Union:
        case CParser.Unsigned:
        case CParser.Void:
        case CParser.Volatile:
        case CParser.Alignas:
        case CParser.Atomic:
        case CParser.Bool:
        case CParser.Complex:
        case CParser.Noreturn:
        case CParser.ThreadLocal:
        case CParser.Identifier:
            this.enterOuterAlt(localctx, 1);
            this.state = 507;
            this.declarationSpecifiers();
            this.state = 509;
            _la = this._input.LA(1);
            if(((((_la - 59)) & ~0x1f) == 0 && ((1 << (_la - 59)) & ((1 << (CParser.LeftParen - 59)) | (1 << (CParser.Star - 59)) | (1 << (CParser.Caret - 59)))) !== 0) || _la===CParser.Identifier) {
                this.state = 508;
                this.initDeclaratorList(0);
            }

            this.state = 511;
            this.match(CParser.Semi);
            break;
        case CParser.StaticAssert:
            this.enterOuterAlt(localctx, 2);
            this.state = 513;
            this.staticAssertDeclaration();
            break;
        default:
            throw new antlr4.error.NoViableAltException(this);
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function DeclarationSpecifiersContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_declarationSpecifiers;
    return this;
}

DeclarationSpecifiersContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
DeclarationSpecifiersContext.prototype.constructor = DeclarationSpecifiersContext;

DeclarationSpecifiersContext.prototype.declarationSpecifier = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(DeclarationSpecifierContext);
    } else {
        return this.getTypedRuleContext(DeclarationSpecifierContext,i);
    }
};

DeclarationSpecifiersContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterDeclarationSpecifiers(this);
	}
};

DeclarationSpecifiersContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitDeclarationSpecifiers(this);
	}
};




CParser.DeclarationSpecifiersContext = DeclarationSpecifiersContext;

CParser.prototype.declarationSpecifiers = function() {

    var localctx = new DeclarationSpecifiersContext(this, this._ctx, this.state);
    this.enterRule(localctx, 50, CParser.RULE_declarationSpecifiers);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 517; 
        this._errHandler.sync(this);
        var _alt = 1;
        do {
        	switch (_alt) {
        	case 1:
        		this.state = 516;
        		this.declarationSpecifier();
        		break;
        	default:
        		throw new antlr4.error.NoViableAltException(this);
        	}
        	this.state = 519; 
        	this._errHandler.sync(this);
        	_alt = this._interp.adaptivePredict(this._input,32, this._ctx);
        } while ( _alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER );
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function DeclarationSpecifiers2Context(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_declarationSpecifiers2;
    return this;
}

DeclarationSpecifiers2Context.prototype = Object.create(antlr4.ParserRuleContext.prototype);
DeclarationSpecifiers2Context.prototype.constructor = DeclarationSpecifiers2Context;

DeclarationSpecifiers2Context.prototype.declarationSpecifier = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(DeclarationSpecifierContext);
    } else {
        return this.getTypedRuleContext(DeclarationSpecifierContext,i);
    }
};

DeclarationSpecifiers2Context.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterDeclarationSpecifiers2(this);
	}
};

DeclarationSpecifiers2Context.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitDeclarationSpecifiers2(this);
	}
};




CParser.DeclarationSpecifiers2Context = DeclarationSpecifiers2Context;

CParser.prototype.declarationSpecifiers2 = function() {

    var localctx = new DeclarationSpecifiers2Context(this, this._ctx, this.state);
    this.enterRule(localctx, 52, CParser.RULE_declarationSpecifiers2);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 522; 
        this._errHandler.sync(this);
        var _alt = 1;
        do {
        	switch (_alt) {
        	case 1:
        		this.state = 521;
        		this.declarationSpecifier();
        		break;
        	default:
        		throw new antlr4.error.NoViableAltException(this);
        	}
        	this.state = 524; 
        	this._errHandler.sync(this);
        	_alt = this._interp.adaptivePredict(this._input,33, this._ctx);
        } while ( _alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER );
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function DeclarationSpecifierContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_declarationSpecifier;
    return this;
}

DeclarationSpecifierContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
DeclarationSpecifierContext.prototype.constructor = DeclarationSpecifierContext;

DeclarationSpecifierContext.prototype.storageClassSpecifier = function() {
    return this.getTypedRuleContext(StorageClassSpecifierContext,0);
};

DeclarationSpecifierContext.prototype.typeSpecifier = function() {
    return this.getTypedRuleContext(TypeSpecifierContext,0);
};

DeclarationSpecifierContext.prototype.typeQualifier = function() {
    return this.getTypedRuleContext(TypeQualifierContext,0);
};

DeclarationSpecifierContext.prototype.functionSpecifier = function() {
    return this.getTypedRuleContext(FunctionSpecifierContext,0);
};

DeclarationSpecifierContext.prototype.alignmentSpecifier = function() {
    return this.getTypedRuleContext(AlignmentSpecifierContext,0);
};

DeclarationSpecifierContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterDeclarationSpecifier(this);
	}
};

DeclarationSpecifierContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitDeclarationSpecifier(this);
	}
};




CParser.DeclarationSpecifierContext = DeclarationSpecifierContext;

CParser.prototype.declarationSpecifier = function() {

    var localctx = new DeclarationSpecifierContext(this, this._ctx, this.state);
    this.enterRule(localctx, 54, CParser.RULE_declarationSpecifier);
    try {
        this.state = 531;
        var la_ = this._interp.adaptivePredict(this._input,34,this._ctx);
        switch(la_) {
        case 1:
            this.enterOuterAlt(localctx, 1);
            this.state = 526;
            this.storageClassSpecifier();
            break;

        case 2:
            this.enterOuterAlt(localctx, 2);
            this.state = 527;
            this.typeSpecifier();
            break;

        case 3:
            this.enterOuterAlt(localctx, 3);
            this.state = 528;
            this.typeQualifier();
            break;

        case 4:
            this.enterOuterAlt(localctx, 4);
            this.state = 529;
            this.functionSpecifier();
            break;

        case 5:
            this.enterOuterAlt(localctx, 5);
            this.state = 530;
            this.alignmentSpecifier();
            break;

        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function InitDeclaratorListContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_initDeclaratorList;
    return this;
}

InitDeclaratorListContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
InitDeclaratorListContext.prototype.constructor = InitDeclaratorListContext;

InitDeclaratorListContext.prototype.initDeclarator = function() {
    return this.getTypedRuleContext(InitDeclaratorContext,0);
};

InitDeclaratorListContext.prototype.initDeclaratorList = function() {
    return this.getTypedRuleContext(InitDeclaratorListContext,0);
};

InitDeclaratorListContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterInitDeclaratorList(this);
	}
};

InitDeclaratorListContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitInitDeclaratorList(this);
	}
};



CParser.prototype.initDeclaratorList = function(_p) {
	if(_p===undefined) {
	    _p = 0;
	}
    var _parentctx = this._ctx;
    var _parentState = this.state;
    var localctx = new InitDeclaratorListContext(this, this._ctx, _parentState);
    var _prevctx = localctx;
    var _startState = 56;
    this.enterRecursionRule(localctx, 56, CParser.RULE_initDeclaratorList, _p);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 534;
        this.initDeclarator();
        this._ctx.stop = this._input.LT(-1);
        this.state = 541;
        this._errHandler.sync(this);
        var _alt = this._interp.adaptivePredict(this._input,35,this._ctx)
        while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
            if(_alt===1) {
                if(this._parseListeners!==null) {
                    this.triggerExitRuleEvent();
                }
                _prevctx = localctx;
                localctx = new InitDeclaratorListContext(this, _parentctx, _parentState);
                this.pushNewRecursionContext(localctx, _startState, CParser.RULE_initDeclaratorList);
                this.state = 536;
                if (!( this.precpred(this._ctx, 1))) {
                    throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 1)");
                }
                this.state = 537;
                this.match(CParser.Comma);
                this.state = 538;
                this.initDeclarator(); 
            }
            this.state = 543;
            this._errHandler.sync(this);
            _alt = this._interp.adaptivePredict(this._input,35,this._ctx);
        }

    } catch( error) {
        if(error instanceof antlr4.error.RecognitionException) {
	        localctx.exception = error;
	        this._errHandler.reportError(this, error);
	        this._errHandler.recover(this, error);
	    } else {
	    	throw error;
	    }
    } finally {
        this.unrollRecursionContexts(_parentctx)
    }
    return localctx;
};

function InitDeclaratorContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_initDeclarator;
    return this;
}

InitDeclaratorContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
InitDeclaratorContext.prototype.constructor = InitDeclaratorContext;

InitDeclaratorContext.prototype.declarator = function() {
    return this.getTypedRuleContext(DeclaratorContext,0);
};

InitDeclaratorContext.prototype.initializer = function() {
    return this.getTypedRuleContext(InitializerContext,0);
};

InitDeclaratorContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterInitDeclarator(this);
	}
};

InitDeclaratorContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitInitDeclarator(this);
	}
};




CParser.InitDeclaratorContext = InitDeclaratorContext;

CParser.prototype.initDeclarator = function() {

    var localctx = new InitDeclaratorContext(this, this._ctx, this.state);
    this.enterRule(localctx, 58, CParser.RULE_initDeclarator);
    try {
        this.state = 549;
        var la_ = this._interp.adaptivePredict(this._input,36,this._ctx);
        switch(la_) {
        case 1:
            this.enterOuterAlt(localctx, 1);
            this.state = 544;
            this.declarator();
            break;

        case 2:
            this.enterOuterAlt(localctx, 2);
            this.state = 545;
            this.declarator();
            this.state = 546;
            this.match(CParser.Assign);
            this.state = 547;
            this.initializer();
            break;

        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function StorageClassSpecifierContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_storageClassSpecifier;
    return this;
}

StorageClassSpecifierContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
StorageClassSpecifierContext.prototype.constructor = StorageClassSpecifierContext;


StorageClassSpecifierContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterStorageClassSpecifier(this);
	}
};

StorageClassSpecifierContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitStorageClassSpecifier(this);
	}
};




CParser.StorageClassSpecifierContext = StorageClassSpecifierContext;

CParser.prototype.storageClassSpecifier = function() {

    var localctx = new StorageClassSpecifierContext(this, this._ctx, this.state);
    this.enterRule(localctx, 60, CParser.RULE_storageClassSpecifier);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 551;
        _la = this._input.LA(1);
        if(!(_la===CParser.Auto || _la===CParser.Extern || ((((_la - 34)) & ~0x1f) == 0 && ((1 << (_la - 34)) & ((1 << (CParser.Register - 34)) | (1 << (CParser.Static - 34)) | (1 << (CParser.Typedef - 34)) | (1 << (CParser.ThreadLocal - 34)))) !== 0))) {
        this._errHandler.recoverInline(this);
        }
        else {
            this.consume();
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function TypeSpecifierContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_typeSpecifier;
    return this;
}

TypeSpecifierContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
TypeSpecifierContext.prototype.constructor = TypeSpecifierContext;

TypeSpecifierContext.prototype.atomicTypeSpecifier = function() {
    return this.getTypedRuleContext(AtomicTypeSpecifierContext,0);
};

TypeSpecifierContext.prototype.structOrUnionSpecifier = function() {
    return this.getTypedRuleContext(StructOrUnionSpecifierContext,0);
};

TypeSpecifierContext.prototype.enumSpecifier = function() {
    return this.getTypedRuleContext(EnumSpecifierContext,0);
};

TypeSpecifierContext.prototype.typedefName = function() {
    return this.getTypedRuleContext(TypedefNameContext,0);
};

TypeSpecifierContext.prototype.constantExpression = function() {
    return this.getTypedRuleContext(ConstantExpressionContext,0);
};

TypeSpecifierContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterTypeSpecifier(this);
	}
};

TypeSpecifierContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitTypeSpecifier(this);
	}
};




CParser.TypeSpecifierContext = TypeSpecifierContext;

CParser.prototype.typeSpecifier = function() {

    var localctx = new TypeSpecifierContext(this, this._ctx, this.state);
    this.enterRule(localctx, 62, CParser.RULE_typeSpecifier);
    var _la = 0; // Token type
    try {
        this.state = 567;
        switch(this._input.LA(1)) {
        case CParser.T__3:
        case CParser.T__4:
        case CParser.T__5:
        case CParser.Char:
        case CParser.Double:
        case CParser.Float:
        case CParser.Int:
        case CParser.Long:
        case CParser.Short:
        case CParser.Signed:
        case CParser.Unsigned:
        case CParser.Void:
        case CParser.Bool:
        case CParser.Complex:
            this.enterOuterAlt(localctx, 1);
            this.state = 553;
            _la = this._input.LA(1);
            if(!((((_la) & ~0x1f) == 0 && ((1 << _la) & ((1 << CParser.T__3) | (1 << CParser.T__4) | (1 << CParser.T__5) | (1 << CParser.Char) | (1 << CParser.Double) | (1 << CParser.Float))) !== 0) || ((((_la - 32)) & ~0x1f) == 0 && ((1 << (_la - 32)) & ((1 << (CParser.Int - 32)) | (1 << (CParser.Long - 32)) | (1 << (CParser.Short - 32)) | (1 << (CParser.Signed - 32)) | (1 << (CParser.Unsigned - 32)) | (1 << (CParser.Void - 32)) | (1 << (CParser.Bool - 32)) | (1 << (CParser.Complex - 32)))) !== 0))) {
            this._errHandler.recoverInline(this);
            }
            else {
                this.consume();
            }
            break;
        case CParser.T__0:
            this.enterOuterAlt(localctx, 2);
            this.state = 554;
            this.match(CParser.T__0);
            this.state = 555;
            this.match(CParser.LeftParen);
            this.state = 556;
            _la = this._input.LA(1);
            if(!((((_la) & ~0x1f) == 0 && ((1 << _la) & ((1 << CParser.T__3) | (1 << CParser.T__4) | (1 << CParser.T__5))) !== 0))) {
            this._errHandler.recoverInline(this);
            }
            else {
                this.consume();
            }
            this.state = 557;
            this.match(CParser.RightParen);
            break;
        case CParser.Atomic:
            this.enterOuterAlt(localctx, 3);
            this.state = 558;
            this.atomicTypeSpecifier();
            break;
        case CParser.Struct:
        case CParser.Union:
            this.enterOuterAlt(localctx, 4);
            this.state = 559;
            this.structOrUnionSpecifier();
            break;
        case CParser.Enum:
            this.enterOuterAlt(localctx, 5);
            this.state = 560;
            this.enumSpecifier();
            break;
        case CParser.Identifier:
            this.enterOuterAlt(localctx, 6);
            this.state = 561;
            this.typedefName();
            break;
        case CParser.T__6:
            this.enterOuterAlt(localctx, 7);
            this.state = 562;
            this.match(CParser.T__6);
            this.state = 563;
            this.match(CParser.LeftParen);
            this.state = 564;
            this.constantExpression();
            this.state = 565;
            this.match(CParser.RightParen);
            break;
        default:
            throw new antlr4.error.NoViableAltException(this);
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function StructOrUnionSpecifierContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_structOrUnionSpecifier;
    return this;
}

StructOrUnionSpecifierContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
StructOrUnionSpecifierContext.prototype.constructor = StructOrUnionSpecifierContext;

StructOrUnionSpecifierContext.prototype.structOrUnion = function() {
    return this.getTypedRuleContext(StructOrUnionContext,0);
};

StructOrUnionSpecifierContext.prototype.structDeclarationList = function() {
    return this.getTypedRuleContext(StructDeclarationListContext,0);
};

StructOrUnionSpecifierContext.prototype.Identifier = function() {
    return this.getToken(CParser.Identifier, 0);
};

StructOrUnionSpecifierContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterStructOrUnionSpecifier(this);
	}
};

StructOrUnionSpecifierContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitStructOrUnionSpecifier(this);
	}
};




CParser.StructOrUnionSpecifierContext = StructOrUnionSpecifierContext;

CParser.prototype.structOrUnionSpecifier = function() {

    var localctx = new StructOrUnionSpecifierContext(this, this._ctx, this.state);
    this.enterRule(localctx, 64, CParser.RULE_structOrUnionSpecifier);
    var _la = 0; // Token type
    try {
        this.state = 580;
        var la_ = this._interp.adaptivePredict(this._input,39,this._ctx);
        switch(la_) {
        case 1:
            this.enterOuterAlt(localctx, 1);
            this.state = 569;
            this.structOrUnion();
            this.state = 571;
            _la = this._input.LA(1);
            if(_la===CParser.Identifier) {
                this.state = 570;
                this.match(CParser.Identifier);
            }

            this.state = 573;
            this.match(CParser.LeftBrace);
            this.state = 574;
            this.structDeclarationList(0);
            this.state = 575;
            this.match(CParser.RightBrace);
            break;

        case 2:
            this.enterOuterAlt(localctx, 2);
            this.state = 577;
            this.structOrUnion();
            this.state = 578;
            this.match(CParser.Identifier);
            break;

        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function StructOrUnionContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_structOrUnion;
    return this;
}

StructOrUnionContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
StructOrUnionContext.prototype.constructor = StructOrUnionContext;


StructOrUnionContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterStructOrUnion(this);
	}
};

StructOrUnionContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitStructOrUnion(this);
	}
};




CParser.StructOrUnionContext = StructOrUnionContext;

CParser.prototype.structOrUnion = function() {

    var localctx = new StructOrUnionContext(this, this._ctx, this.state);
    this.enterRule(localctx, 66, CParser.RULE_structOrUnion);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 582;
        _la = this._input.LA(1);
        if(!(_la===CParser.Struct || _la===CParser.Union)) {
        this._errHandler.recoverInline(this);
        }
        else {
            this.consume();
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function StructDeclarationListContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_structDeclarationList;
    return this;
}

StructDeclarationListContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
StructDeclarationListContext.prototype.constructor = StructDeclarationListContext;

StructDeclarationListContext.prototype.structDeclaration = function() {
    return this.getTypedRuleContext(StructDeclarationContext,0);
};

StructDeclarationListContext.prototype.structDeclarationList = function() {
    return this.getTypedRuleContext(StructDeclarationListContext,0);
};

StructDeclarationListContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterStructDeclarationList(this);
	}
};

StructDeclarationListContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitStructDeclarationList(this);
	}
};



CParser.prototype.structDeclarationList = function(_p) {
	if(_p===undefined) {
	    _p = 0;
	}
    var _parentctx = this._ctx;
    var _parentState = this.state;
    var localctx = new StructDeclarationListContext(this, this._ctx, _parentState);
    var _prevctx = localctx;
    var _startState = 68;
    this.enterRecursionRule(localctx, 68, CParser.RULE_structDeclarationList, _p);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 585;
        this.structDeclaration();
        this._ctx.stop = this._input.LT(-1);
        this.state = 591;
        this._errHandler.sync(this);
        var _alt = this._interp.adaptivePredict(this._input,40,this._ctx)
        while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
            if(_alt===1) {
                if(this._parseListeners!==null) {
                    this.triggerExitRuleEvent();
                }
                _prevctx = localctx;
                localctx = new StructDeclarationListContext(this, _parentctx, _parentState);
                this.pushNewRecursionContext(localctx, _startState, CParser.RULE_structDeclarationList);
                this.state = 587;
                if (!( this.precpred(this._ctx, 1))) {
                    throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 1)");
                }
                this.state = 588;
                this.structDeclaration(); 
            }
            this.state = 593;
            this._errHandler.sync(this);
            _alt = this._interp.adaptivePredict(this._input,40,this._ctx);
        }

    } catch( error) {
        if(error instanceof antlr4.error.RecognitionException) {
	        localctx.exception = error;
	        this._errHandler.reportError(this, error);
	        this._errHandler.recover(this, error);
	    } else {
	    	throw error;
	    }
    } finally {
        this.unrollRecursionContexts(_parentctx)
    }
    return localctx;
};

function StructDeclarationContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_structDeclaration;
    return this;
}

StructDeclarationContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
StructDeclarationContext.prototype.constructor = StructDeclarationContext;

StructDeclarationContext.prototype.specifierQualifierList = function() {
    return this.getTypedRuleContext(SpecifierQualifierListContext,0);
};

StructDeclarationContext.prototype.structDeclaratorList = function() {
    return this.getTypedRuleContext(StructDeclaratorListContext,0);
};

StructDeclarationContext.prototype.staticAssertDeclaration = function() {
    return this.getTypedRuleContext(StaticAssertDeclarationContext,0);
};

StructDeclarationContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterStructDeclaration(this);
	}
};

StructDeclarationContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitStructDeclaration(this);
	}
};




CParser.StructDeclarationContext = StructDeclarationContext;

CParser.prototype.structDeclaration = function() {

    var localctx = new StructDeclarationContext(this, this._ctx, this.state);
    this.enterRule(localctx, 70, CParser.RULE_structDeclaration);
    var _la = 0; // Token type
    try {
        this.state = 601;
        switch(this._input.LA(1)) {
        case CParser.T__0:
        case CParser.T__3:
        case CParser.T__4:
        case CParser.T__5:
        case CParser.T__6:
        case CParser.Char:
        case CParser.Const:
        case CParser.Double:
        case CParser.Enum:
        case CParser.Float:
        case CParser.Int:
        case CParser.Long:
        case CParser.Restrict:
        case CParser.Short:
        case CParser.Signed:
        case CParser.Struct:
        case CParser.Union:
        case CParser.Unsigned:
        case CParser.Void:
        case CParser.Volatile:
        case CParser.Atomic:
        case CParser.Bool:
        case CParser.Complex:
        case CParser.Identifier:
            this.enterOuterAlt(localctx, 1);
            this.state = 594;
            this.specifierQualifierList();
            this.state = 596;
            _la = this._input.LA(1);
            if(((((_la - 59)) & ~0x1f) == 0 && ((1 << (_la - 59)) & ((1 << (CParser.LeftParen - 59)) | (1 << (CParser.Star - 59)) | (1 << (CParser.Caret - 59)) | (1 << (CParser.Colon - 59)))) !== 0) || _la===CParser.Identifier) {
                this.state = 595;
                this.structDeclaratorList(0);
            }

            this.state = 598;
            this.match(CParser.Semi);
            break;
        case CParser.StaticAssert:
            this.enterOuterAlt(localctx, 2);
            this.state = 600;
            this.staticAssertDeclaration();
            break;
        default:
            throw new antlr4.error.NoViableAltException(this);
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function SpecifierQualifierListContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_specifierQualifierList;
    return this;
}

SpecifierQualifierListContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
SpecifierQualifierListContext.prototype.constructor = SpecifierQualifierListContext;

SpecifierQualifierListContext.prototype.typeSpecifier = function() {
    return this.getTypedRuleContext(TypeSpecifierContext,0);
};

SpecifierQualifierListContext.prototype.specifierQualifierList = function() {
    return this.getTypedRuleContext(SpecifierQualifierListContext,0);
};

SpecifierQualifierListContext.prototype.typeQualifier = function() {
    return this.getTypedRuleContext(TypeQualifierContext,0);
};

SpecifierQualifierListContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterSpecifierQualifierList(this);
	}
};

SpecifierQualifierListContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitSpecifierQualifierList(this);
	}
};




CParser.SpecifierQualifierListContext = SpecifierQualifierListContext;

CParser.prototype.specifierQualifierList = function() {

    var localctx = new SpecifierQualifierListContext(this, this._ctx, this.state);
    this.enterRule(localctx, 72, CParser.RULE_specifierQualifierList);
    try {
        this.state = 611;
        var la_ = this._interp.adaptivePredict(this._input,45,this._ctx);
        switch(la_) {
        case 1:
            this.enterOuterAlt(localctx, 1);
            this.state = 603;
            this.typeSpecifier();
            this.state = 605;
            var la_ = this._interp.adaptivePredict(this._input,43,this._ctx);
            if(la_===1) {
                this.state = 604;
                this.specifierQualifierList();

            }
            break;

        case 2:
            this.enterOuterAlt(localctx, 2);
            this.state = 607;
            this.typeQualifier();
            this.state = 609;
            var la_ = this._interp.adaptivePredict(this._input,44,this._ctx);
            if(la_===1) {
                this.state = 608;
                this.specifierQualifierList();

            }
            break;

        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function StructDeclaratorListContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_structDeclaratorList;
    return this;
}

StructDeclaratorListContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
StructDeclaratorListContext.prototype.constructor = StructDeclaratorListContext;

StructDeclaratorListContext.prototype.structDeclarator = function() {
    return this.getTypedRuleContext(StructDeclaratorContext,0);
};

StructDeclaratorListContext.prototype.structDeclaratorList = function() {
    return this.getTypedRuleContext(StructDeclaratorListContext,0);
};

StructDeclaratorListContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterStructDeclaratorList(this);
	}
};

StructDeclaratorListContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitStructDeclaratorList(this);
	}
};



CParser.prototype.structDeclaratorList = function(_p) {
	if(_p===undefined) {
	    _p = 0;
	}
    var _parentctx = this._ctx;
    var _parentState = this.state;
    var localctx = new StructDeclaratorListContext(this, this._ctx, _parentState);
    var _prevctx = localctx;
    var _startState = 74;
    this.enterRecursionRule(localctx, 74, CParser.RULE_structDeclaratorList, _p);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 614;
        this.structDeclarator();
        this._ctx.stop = this._input.LT(-1);
        this.state = 621;
        this._errHandler.sync(this);
        var _alt = this._interp.adaptivePredict(this._input,46,this._ctx)
        while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
            if(_alt===1) {
                if(this._parseListeners!==null) {
                    this.triggerExitRuleEvent();
                }
                _prevctx = localctx;
                localctx = new StructDeclaratorListContext(this, _parentctx, _parentState);
                this.pushNewRecursionContext(localctx, _startState, CParser.RULE_structDeclaratorList);
                this.state = 616;
                if (!( this.precpred(this._ctx, 1))) {
                    throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 1)");
                }
                this.state = 617;
                this.match(CParser.Comma);
                this.state = 618;
                this.structDeclarator(); 
            }
            this.state = 623;
            this._errHandler.sync(this);
            _alt = this._interp.adaptivePredict(this._input,46,this._ctx);
        }

    } catch( error) {
        if(error instanceof antlr4.error.RecognitionException) {
	        localctx.exception = error;
	        this._errHandler.reportError(this, error);
	        this._errHandler.recover(this, error);
	    } else {
	    	throw error;
	    }
    } finally {
        this.unrollRecursionContexts(_parentctx)
    }
    return localctx;
};

function StructDeclaratorContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_structDeclarator;
    return this;
}

StructDeclaratorContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
StructDeclaratorContext.prototype.constructor = StructDeclaratorContext;

StructDeclaratorContext.prototype.declarator = function() {
    return this.getTypedRuleContext(DeclaratorContext,0);
};

StructDeclaratorContext.prototype.constantExpression = function() {
    return this.getTypedRuleContext(ConstantExpressionContext,0);
};

StructDeclaratorContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterStructDeclarator(this);
	}
};

StructDeclaratorContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitStructDeclarator(this);
	}
};




CParser.StructDeclaratorContext = StructDeclaratorContext;

CParser.prototype.structDeclarator = function() {

    var localctx = new StructDeclaratorContext(this, this._ctx, this.state);
    this.enterRule(localctx, 76, CParser.RULE_structDeclarator);
    var _la = 0; // Token type
    try {
        this.state = 630;
        var la_ = this._interp.adaptivePredict(this._input,48,this._ctx);
        switch(la_) {
        case 1:
            this.enterOuterAlt(localctx, 1);
            this.state = 624;
            this.declarator();
            break;

        case 2:
            this.enterOuterAlt(localctx, 2);
            this.state = 626;
            _la = this._input.LA(1);
            if(((((_la - 59)) & ~0x1f) == 0 && ((1 << (_la - 59)) & ((1 << (CParser.LeftParen - 59)) | (1 << (CParser.Star - 59)) | (1 << (CParser.Caret - 59)))) !== 0) || _la===CParser.Identifier) {
                this.state = 625;
                this.declarator();
            }

            this.state = 628;
            this.match(CParser.Colon);
            this.state = 629;
            this.constantExpression();
            break;

        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function EnumSpecifierContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_enumSpecifier;
    return this;
}

EnumSpecifierContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
EnumSpecifierContext.prototype.constructor = EnumSpecifierContext;

EnumSpecifierContext.prototype.enumeratorList = function() {
    return this.getTypedRuleContext(EnumeratorListContext,0);
};

EnumSpecifierContext.prototype.Identifier = function() {
    return this.getToken(CParser.Identifier, 0);
};

EnumSpecifierContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterEnumSpecifier(this);
	}
};

EnumSpecifierContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitEnumSpecifier(this);
	}
};




CParser.EnumSpecifierContext = EnumSpecifierContext;

CParser.prototype.enumSpecifier = function() {

    var localctx = new EnumSpecifierContext(this, this._ctx, this.state);
    this.enterRule(localctx, 78, CParser.RULE_enumSpecifier);
    var _la = 0; // Token type
    try {
        this.state = 651;
        var la_ = this._interp.adaptivePredict(this._input,51,this._ctx);
        switch(la_) {
        case 1:
            this.enterOuterAlt(localctx, 1);
            this.state = 632;
            this.match(CParser.Enum);
            this.state = 634;
            _la = this._input.LA(1);
            if(_la===CParser.Identifier) {
                this.state = 633;
                this.match(CParser.Identifier);
            }

            this.state = 636;
            this.match(CParser.LeftBrace);
            this.state = 637;
            this.enumeratorList(0);
            this.state = 638;
            this.match(CParser.RightBrace);
            break;

        case 2:
            this.enterOuterAlt(localctx, 2);
            this.state = 640;
            this.match(CParser.Enum);
            this.state = 642;
            _la = this._input.LA(1);
            if(_la===CParser.Identifier) {
                this.state = 641;
                this.match(CParser.Identifier);
            }

            this.state = 644;
            this.match(CParser.LeftBrace);
            this.state = 645;
            this.enumeratorList(0);
            this.state = 646;
            this.match(CParser.Comma);
            this.state = 647;
            this.match(CParser.RightBrace);
            break;

        case 3:
            this.enterOuterAlt(localctx, 3);
            this.state = 649;
            this.match(CParser.Enum);
            this.state = 650;
            this.match(CParser.Identifier);
            break;

        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function EnumeratorListContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_enumeratorList;
    return this;
}

EnumeratorListContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
EnumeratorListContext.prototype.constructor = EnumeratorListContext;

EnumeratorListContext.prototype.enumerator = function() {
    return this.getTypedRuleContext(EnumeratorContext,0);
};

EnumeratorListContext.prototype.enumeratorList = function() {
    return this.getTypedRuleContext(EnumeratorListContext,0);
};

EnumeratorListContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterEnumeratorList(this);
	}
};

EnumeratorListContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitEnumeratorList(this);
	}
};



CParser.prototype.enumeratorList = function(_p) {
	if(_p===undefined) {
	    _p = 0;
	}
    var _parentctx = this._ctx;
    var _parentState = this.state;
    var localctx = new EnumeratorListContext(this, this._ctx, _parentState);
    var _prevctx = localctx;
    var _startState = 80;
    this.enterRecursionRule(localctx, 80, CParser.RULE_enumeratorList, _p);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 654;
        this.enumerator();
        this._ctx.stop = this._input.LT(-1);
        this.state = 661;
        this._errHandler.sync(this);
        var _alt = this._interp.adaptivePredict(this._input,52,this._ctx)
        while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
            if(_alt===1) {
                if(this._parseListeners!==null) {
                    this.triggerExitRuleEvent();
                }
                _prevctx = localctx;
                localctx = new EnumeratorListContext(this, _parentctx, _parentState);
                this.pushNewRecursionContext(localctx, _startState, CParser.RULE_enumeratorList);
                this.state = 656;
                if (!( this.precpred(this._ctx, 1))) {
                    throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 1)");
                }
                this.state = 657;
                this.match(CParser.Comma);
                this.state = 658;
                this.enumerator(); 
            }
            this.state = 663;
            this._errHandler.sync(this);
            _alt = this._interp.adaptivePredict(this._input,52,this._ctx);
        }

    } catch( error) {
        if(error instanceof antlr4.error.RecognitionException) {
	        localctx.exception = error;
	        this._errHandler.reportError(this, error);
	        this._errHandler.recover(this, error);
	    } else {
	    	throw error;
	    }
    } finally {
        this.unrollRecursionContexts(_parentctx)
    }
    return localctx;
};

function EnumeratorContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_enumerator;
    return this;
}

EnumeratorContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
EnumeratorContext.prototype.constructor = EnumeratorContext;

EnumeratorContext.prototype.enumerationConstant = function() {
    return this.getTypedRuleContext(EnumerationConstantContext,0);
};

EnumeratorContext.prototype.constantExpression = function() {
    return this.getTypedRuleContext(ConstantExpressionContext,0);
};

EnumeratorContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterEnumerator(this);
	}
};

EnumeratorContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitEnumerator(this);
	}
};




CParser.EnumeratorContext = EnumeratorContext;

CParser.prototype.enumerator = function() {

    var localctx = new EnumeratorContext(this, this._ctx, this.state);
    this.enterRule(localctx, 82, CParser.RULE_enumerator);
    try {
        this.state = 669;
        var la_ = this._interp.adaptivePredict(this._input,53,this._ctx);
        switch(la_) {
        case 1:
            this.enterOuterAlt(localctx, 1);
            this.state = 664;
            this.enumerationConstant();
            break;

        case 2:
            this.enterOuterAlt(localctx, 2);
            this.state = 665;
            this.enumerationConstant();
            this.state = 666;
            this.match(CParser.Assign);
            this.state = 667;
            this.constantExpression();
            break;

        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function EnumerationConstantContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_enumerationConstant;
    return this;
}

EnumerationConstantContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
EnumerationConstantContext.prototype.constructor = EnumerationConstantContext;

EnumerationConstantContext.prototype.Identifier = function() {
    return this.getToken(CParser.Identifier, 0);
};

EnumerationConstantContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterEnumerationConstant(this);
	}
};

EnumerationConstantContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitEnumerationConstant(this);
	}
};




CParser.EnumerationConstantContext = EnumerationConstantContext;

CParser.prototype.enumerationConstant = function() {

    var localctx = new EnumerationConstantContext(this, this._ctx, this.state);
    this.enterRule(localctx, 84, CParser.RULE_enumerationConstant);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 671;
        this.match(CParser.Identifier);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function AtomicTypeSpecifierContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_atomicTypeSpecifier;
    return this;
}

AtomicTypeSpecifierContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
AtomicTypeSpecifierContext.prototype.constructor = AtomicTypeSpecifierContext;

AtomicTypeSpecifierContext.prototype.typeName = function() {
    return this.getTypedRuleContext(TypeNameContext,0);
};

AtomicTypeSpecifierContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterAtomicTypeSpecifier(this);
	}
};

AtomicTypeSpecifierContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitAtomicTypeSpecifier(this);
	}
};




CParser.AtomicTypeSpecifierContext = AtomicTypeSpecifierContext;

CParser.prototype.atomicTypeSpecifier = function() {

    var localctx = new AtomicTypeSpecifierContext(this, this._ctx, this.state);
    this.enterRule(localctx, 86, CParser.RULE_atomicTypeSpecifier);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 673;
        this.match(CParser.Atomic);
        this.state = 674;
        this.match(CParser.LeftParen);
        this.state = 675;
        this.typeName();
        this.state = 676;
        this.match(CParser.RightParen);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function TypeQualifierContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_typeQualifier;
    return this;
}

TypeQualifierContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
TypeQualifierContext.prototype.constructor = TypeQualifierContext;


TypeQualifierContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterTypeQualifier(this);
	}
};

TypeQualifierContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitTypeQualifier(this);
	}
};




CParser.TypeQualifierContext = TypeQualifierContext;

CParser.prototype.typeQualifier = function() {

    var localctx = new TypeQualifierContext(this, this._ctx, this.state);
    this.enterRule(localctx, 88, CParser.RULE_typeQualifier);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 678;
        _la = this._input.LA(1);
        if(!(_la===CParser.Const || ((((_la - 35)) & ~0x1f) == 0 && ((1 << (_la - 35)) & ((1 << (CParser.Restrict - 35)) | (1 << (CParser.Volatile - 35)) | (1 << (CParser.Atomic - 35)))) !== 0))) {
        this._errHandler.recoverInline(this);
        }
        else {
            this.consume();
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function FunctionSpecifierContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_functionSpecifier;
    return this;
}

FunctionSpecifierContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
FunctionSpecifierContext.prototype.constructor = FunctionSpecifierContext;

FunctionSpecifierContext.prototype.gccAttributeSpecifier = function() {
    return this.getTypedRuleContext(GccAttributeSpecifierContext,0);
};

FunctionSpecifierContext.prototype.Identifier = function() {
    return this.getToken(CParser.Identifier, 0);
};

FunctionSpecifierContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterFunctionSpecifier(this);
	}
};

FunctionSpecifierContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitFunctionSpecifier(this);
	}
};




CParser.FunctionSpecifierContext = FunctionSpecifierContext;

CParser.prototype.functionSpecifier = function() {

    var localctx = new FunctionSpecifierContext(this, this._ctx, this.state);
    this.enterRule(localctx, 90, CParser.RULE_functionSpecifier);
    var _la = 0; // Token type
    try {
        this.state = 686;
        switch(this._input.LA(1)) {
        case CParser.T__7:
        case CParser.T__8:
        case CParser.Inline:
        case CParser.Noreturn:
            this.enterOuterAlt(localctx, 1);
            this.state = 680;
            _la = this._input.LA(1);
            if(!((((_la) & ~0x1f) == 0 && ((1 << _la) & ((1 << CParser.T__7) | (1 << CParser.T__8) | (1 << CParser.Inline))) !== 0) || _la===CParser.Noreturn)) {
            this._errHandler.recoverInline(this);
            }
            else {
                this.consume();
            }
            break;
        case CParser.T__11:
            this.enterOuterAlt(localctx, 2);
            this.state = 681;
            this.gccAttributeSpecifier();
            break;
        case CParser.T__9:
            this.enterOuterAlt(localctx, 3);
            this.state = 682;
            this.match(CParser.T__9);
            this.state = 683;
            this.match(CParser.LeftParen);
            this.state = 684;
            this.match(CParser.Identifier);
            this.state = 685;
            this.match(CParser.RightParen);
            break;
        default:
            throw new antlr4.error.NoViableAltException(this);
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function AlignmentSpecifierContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_alignmentSpecifier;
    return this;
}

AlignmentSpecifierContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
AlignmentSpecifierContext.prototype.constructor = AlignmentSpecifierContext;

AlignmentSpecifierContext.prototype.typeName = function() {
    return this.getTypedRuleContext(TypeNameContext,0);
};

AlignmentSpecifierContext.prototype.constantExpression = function() {
    return this.getTypedRuleContext(ConstantExpressionContext,0);
};

AlignmentSpecifierContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterAlignmentSpecifier(this);
	}
};

AlignmentSpecifierContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitAlignmentSpecifier(this);
	}
};




CParser.AlignmentSpecifierContext = AlignmentSpecifierContext;

CParser.prototype.alignmentSpecifier = function() {

    var localctx = new AlignmentSpecifierContext(this, this._ctx, this.state);
    this.enterRule(localctx, 92, CParser.RULE_alignmentSpecifier);
    try {
        this.state = 698;
        var la_ = this._interp.adaptivePredict(this._input,55,this._ctx);
        switch(la_) {
        case 1:
            this.enterOuterAlt(localctx, 1);
            this.state = 688;
            this.match(CParser.Alignas);
            this.state = 689;
            this.match(CParser.LeftParen);
            this.state = 690;
            this.typeName();
            this.state = 691;
            this.match(CParser.RightParen);
            break;

        case 2:
            this.enterOuterAlt(localctx, 2);
            this.state = 693;
            this.match(CParser.Alignas);
            this.state = 694;
            this.match(CParser.LeftParen);
            this.state = 695;
            this.constantExpression();
            this.state = 696;
            this.match(CParser.RightParen);
            break;

        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function DeclaratorContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_declarator;
    return this;
}

DeclaratorContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
DeclaratorContext.prototype.constructor = DeclaratorContext;

DeclaratorContext.prototype.directDeclarator = function() {
    return this.getTypedRuleContext(DirectDeclaratorContext,0);
};

DeclaratorContext.prototype.pointer = function() {
    return this.getTypedRuleContext(PointerContext,0);
};

DeclaratorContext.prototype.gccDeclaratorExtension = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(GccDeclaratorExtensionContext);
    } else {
        return this.getTypedRuleContext(GccDeclaratorExtensionContext,i);
    }
};

DeclaratorContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterDeclarator(this);
	}
};

DeclaratorContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitDeclarator(this);
	}
};




CParser.DeclaratorContext = DeclaratorContext;

CParser.prototype.declarator = function() {

    var localctx = new DeclaratorContext(this, this._ctx, this.state);
    this.enterRule(localctx, 94, CParser.RULE_declarator);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 701;
        _la = this._input.LA(1);
        if(_la===CParser.Star || _la===CParser.Caret) {
            this.state = 700;
            this.pointer();
        }

        this.state = 703;
        this.directDeclarator(0);
        this.state = 707;
        this._errHandler.sync(this);
        var _alt = this._interp.adaptivePredict(this._input,57,this._ctx)
        while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
            if(_alt===1) {
                this.state = 704;
                this.gccDeclaratorExtension(); 
            }
            this.state = 709;
            this._errHandler.sync(this);
            _alt = this._interp.adaptivePredict(this._input,57,this._ctx);
        }

    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function DirectDeclaratorContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_directDeclarator;
    return this;
}

DirectDeclaratorContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
DirectDeclaratorContext.prototype.constructor = DirectDeclaratorContext;

DirectDeclaratorContext.prototype.Identifier = function() {
    return this.getToken(CParser.Identifier, 0);
};

DirectDeclaratorContext.prototype.declarator = function() {
    return this.getTypedRuleContext(DeclaratorContext,0);
};

DirectDeclaratorContext.prototype.directDeclarator = function() {
    return this.getTypedRuleContext(DirectDeclaratorContext,0);
};

DirectDeclaratorContext.prototype.typeQualifierList = function() {
    return this.getTypedRuleContext(TypeQualifierListContext,0);
};

DirectDeclaratorContext.prototype.assignmentExpression = function() {
    return this.getTypedRuleContext(AssignmentExpressionContext,0);
};

DirectDeclaratorContext.prototype.parameterTypeList = function() {
    return this.getTypedRuleContext(ParameterTypeListContext,0);
};

DirectDeclaratorContext.prototype.identifierList = function() {
    return this.getTypedRuleContext(IdentifierListContext,0);
};

DirectDeclaratorContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterDirectDeclarator(this);
	}
};

DirectDeclaratorContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitDirectDeclarator(this);
	}
};



CParser.prototype.directDeclarator = function(_p) {
	if(_p===undefined) {
	    _p = 0;
	}
    var _parentctx = this._ctx;
    var _parentState = this.state;
    var localctx = new DirectDeclaratorContext(this, this._ctx, _parentState);
    var _prevctx = localctx;
    var _startState = 96;
    this.enterRecursionRule(localctx, 96, CParser.RULE_directDeclarator, _p);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 716;
        switch(this._input.LA(1)) {
        case CParser.Identifier:
            this.state = 711;
            this.match(CParser.Identifier);
            break;
        case CParser.LeftParen:
            this.state = 712;
            this.match(CParser.LeftParen);
            this.state = 713;
            this.declarator();
            this.state = 714;
            this.match(CParser.RightParen);
            break;
        default:
            throw new antlr4.error.NoViableAltException(this);
        }
        this._ctx.stop = this._input.LT(-1);
        this.state = 763;
        this._errHandler.sync(this);
        var _alt = this._interp.adaptivePredict(this._input,65,this._ctx)
        while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
            if(_alt===1) {
                if(this._parseListeners!==null) {
                    this.triggerExitRuleEvent();
                }
                _prevctx = localctx;
                this.state = 761;
                var la_ = this._interp.adaptivePredict(this._input,64,this._ctx);
                switch(la_) {
                case 1:
                    localctx = new DirectDeclaratorContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, CParser.RULE_directDeclarator);
                    this.state = 718;
                    if (!( this.precpred(this._ctx, 6))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 6)");
                    }
                    this.state = 719;
                    this.match(CParser.LeftBracket);
                    this.state = 721;
                    _la = this._input.LA(1);
                    if(_la===CParser.Const || ((((_la - 35)) & ~0x1f) == 0 && ((1 << (_la - 35)) & ((1 << (CParser.Restrict - 35)) | (1 << (CParser.Volatile - 35)) | (1 << (CParser.Atomic - 35)))) !== 0)) {
                        this.state = 720;
                        this.typeQualifierList(0);
                    }

                    this.state = 724;
                    _la = this._input.LA(1);
                    if((((_la) & ~0x1f) == 0 && ((1 << _la) & ((1 << CParser.T__0) | (1 << CParser.T__1) | (1 << CParser.T__2))) !== 0) || ((((_la - 39)) & ~0x1f) == 0 && ((1 << (_la - 39)) & ((1 << (CParser.Sizeof - 39)) | (1 << (CParser.Alignof - 39)) | (1 << (CParser.Generic - 39)) | (1 << (CParser.LeftParen - 39)))) !== 0) || ((((_la - 71)) & ~0x1f) == 0 && ((1 << (_la - 71)) & ((1 << (CParser.Plus - 71)) | (1 << (CParser.PlusPlus - 71)) | (1 << (CParser.Minus - 71)) | (1 << (CParser.MinusMinus - 71)) | (1 << (CParser.Star - 71)) | (1 << (CParser.And - 71)) | (1 << (CParser.AndAnd - 71)) | (1 << (CParser.Not - 71)) | (1 << (CParser.Tilde - 71)))) !== 0) || ((((_la - 105)) & ~0x1f) == 0 && ((1 << (_la - 105)) & ((1 << (CParser.Identifier - 105)) | (1 << (CParser.Constant - 105)) | (1 << (CParser.StringLiteral - 105)))) !== 0)) {
                        this.state = 723;
                        this.assignmentExpression();
                    }

                    this.state = 726;
                    this.match(CParser.RightBracket);
                    break;

                case 2:
                    localctx = new DirectDeclaratorContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, CParser.RULE_directDeclarator);
                    this.state = 727;
                    if (!( this.precpred(this._ctx, 5))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 5)");
                    }
                    this.state = 728;
                    this.match(CParser.LeftBracket);
                    this.state = 729;
                    this.match(CParser.Static);
                    this.state = 731;
                    _la = this._input.LA(1);
                    if(_la===CParser.Const || ((((_la - 35)) & ~0x1f) == 0 && ((1 << (_la - 35)) & ((1 << (CParser.Restrict - 35)) | (1 << (CParser.Volatile - 35)) | (1 << (CParser.Atomic - 35)))) !== 0)) {
                        this.state = 730;
                        this.typeQualifierList(0);
                    }

                    this.state = 733;
                    this.assignmentExpression();
                    this.state = 734;
                    this.match(CParser.RightBracket);
                    break;

                case 3:
                    localctx = new DirectDeclaratorContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, CParser.RULE_directDeclarator);
                    this.state = 736;
                    if (!( this.precpred(this._ctx, 4))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 4)");
                    }
                    this.state = 737;
                    this.match(CParser.LeftBracket);
                    this.state = 738;
                    this.typeQualifierList(0);
                    this.state = 739;
                    this.match(CParser.Static);
                    this.state = 740;
                    this.assignmentExpression();
                    this.state = 741;
                    this.match(CParser.RightBracket);
                    break;

                case 4:
                    localctx = new DirectDeclaratorContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, CParser.RULE_directDeclarator);
                    this.state = 743;
                    if (!( this.precpred(this._ctx, 3))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 3)");
                    }
                    this.state = 744;
                    this.match(CParser.LeftBracket);
                    this.state = 746;
                    _la = this._input.LA(1);
                    if(_la===CParser.Const || ((((_la - 35)) & ~0x1f) == 0 && ((1 << (_la - 35)) & ((1 << (CParser.Restrict - 35)) | (1 << (CParser.Volatile - 35)) | (1 << (CParser.Atomic - 35)))) !== 0)) {
                        this.state = 745;
                        this.typeQualifierList(0);
                    }

                    this.state = 748;
                    this.match(CParser.Star);
                    this.state = 749;
                    this.match(CParser.RightBracket);
                    break;

                case 5:
                    localctx = new DirectDeclaratorContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, CParser.RULE_directDeclarator);
                    this.state = 750;
                    if (!( this.precpred(this._ctx, 2))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 2)");
                    }
                    this.state = 751;
                    this.match(CParser.LeftParen);
                    this.state = 752;
                    this.parameterTypeList();
                    this.state = 753;
                    this.match(CParser.RightParen);
                    break;

                case 6:
                    localctx = new DirectDeclaratorContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, CParser.RULE_directDeclarator);
                    this.state = 755;
                    if (!( this.precpred(this._ctx, 1))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 1)");
                    }
                    this.state = 756;
                    this.match(CParser.LeftParen);
                    this.state = 758;
                    _la = this._input.LA(1);
                    if(_la===CParser.Identifier) {
                        this.state = 757;
                        this.identifierList(0);
                    }

                    this.state = 760;
                    this.match(CParser.RightParen);
                    break;

                } 
            }
            this.state = 765;
            this._errHandler.sync(this);
            _alt = this._interp.adaptivePredict(this._input,65,this._ctx);
        }

    } catch( error) {
        if(error instanceof antlr4.error.RecognitionException) {
	        localctx.exception = error;
	        this._errHandler.reportError(this, error);
	        this._errHandler.recover(this, error);
	    } else {
	    	throw error;
	    }
    } finally {
        this.unrollRecursionContexts(_parentctx)
    }
    return localctx;
};

function GccDeclaratorExtensionContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_gccDeclaratorExtension;
    return this;
}

GccDeclaratorExtensionContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
GccDeclaratorExtensionContext.prototype.constructor = GccDeclaratorExtensionContext;

GccDeclaratorExtensionContext.prototype.StringLiteral = function(i) {
	if(i===undefined) {
		i = null;
	}
    if(i===null) {
        return this.getTokens(CParser.StringLiteral);
    } else {
        return this.getToken(CParser.StringLiteral, i);
    }
};


GccDeclaratorExtensionContext.prototype.gccAttributeSpecifier = function() {
    return this.getTypedRuleContext(GccAttributeSpecifierContext,0);
};

GccDeclaratorExtensionContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterGccDeclaratorExtension(this);
	}
};

GccDeclaratorExtensionContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitGccDeclaratorExtension(this);
	}
};




CParser.GccDeclaratorExtensionContext = GccDeclaratorExtensionContext;

CParser.prototype.gccDeclaratorExtension = function() {

    var localctx = new GccDeclaratorExtensionContext(this, this._ctx, this.state);
    this.enterRule(localctx, 98, CParser.RULE_gccDeclaratorExtension);
    var _la = 0; // Token type
    try {
        this.state = 775;
        switch(this._input.LA(1)) {
        case CParser.T__10:
            this.enterOuterAlt(localctx, 1);
            this.state = 766;
            this.match(CParser.T__10);
            this.state = 767;
            this.match(CParser.LeftParen);
            this.state = 769; 
            this._errHandler.sync(this);
            _la = this._input.LA(1);
            do {
                this.state = 768;
                this.match(CParser.StringLiteral);
                this.state = 771; 
                this._errHandler.sync(this);
                _la = this._input.LA(1);
            } while(_la===CParser.StringLiteral);
            this.state = 773;
            this.match(CParser.RightParen);
            break;
        case CParser.T__11:
            this.enterOuterAlt(localctx, 2);
            this.state = 774;
            this.gccAttributeSpecifier();
            break;
        default:
            throw new antlr4.error.NoViableAltException(this);
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function GccAttributeSpecifierContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_gccAttributeSpecifier;
    return this;
}

GccAttributeSpecifierContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
GccAttributeSpecifierContext.prototype.constructor = GccAttributeSpecifierContext;

GccAttributeSpecifierContext.prototype.gccAttributeList = function() {
    return this.getTypedRuleContext(GccAttributeListContext,0);
};

GccAttributeSpecifierContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterGccAttributeSpecifier(this);
	}
};

GccAttributeSpecifierContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitGccAttributeSpecifier(this);
	}
};




CParser.GccAttributeSpecifierContext = GccAttributeSpecifierContext;

CParser.prototype.gccAttributeSpecifier = function() {

    var localctx = new GccAttributeSpecifierContext(this, this._ctx, this.state);
    this.enterRule(localctx, 100, CParser.RULE_gccAttributeSpecifier);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 777;
        this.match(CParser.T__11);
        this.state = 778;
        this.match(CParser.LeftParen);
        this.state = 779;
        this.match(CParser.LeftParen);
        this.state = 780;
        this.gccAttributeList();
        this.state = 781;
        this.match(CParser.RightParen);
        this.state = 782;
        this.match(CParser.RightParen);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function GccAttributeListContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_gccAttributeList;
    return this;
}

GccAttributeListContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
GccAttributeListContext.prototype.constructor = GccAttributeListContext;

GccAttributeListContext.prototype.gccAttribute = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(GccAttributeContext);
    } else {
        return this.getTypedRuleContext(GccAttributeContext,i);
    }
};

GccAttributeListContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterGccAttributeList(this);
	}
};

GccAttributeListContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitGccAttributeList(this);
	}
};




CParser.GccAttributeListContext = GccAttributeListContext;

CParser.prototype.gccAttributeList = function() {

    var localctx = new GccAttributeListContext(this, this._ctx, this.state);
    this.enterRule(localctx, 102, CParser.RULE_gccAttributeList);
    var _la = 0; // Token type
    try {
        this.state = 793;
        var la_ = this._interp.adaptivePredict(this._input,69,this._ctx);
        switch(la_) {
        case 1:
            this.enterOuterAlt(localctx, 1);
            this.state = 784;
            this.gccAttribute();
            this.state = 789;
            this._errHandler.sync(this);
            _la = this._input.LA(1);
            while(_la===CParser.Comma) {
                this.state = 785;
                this.match(CParser.Comma);
                this.state = 786;
                this.gccAttribute();
                this.state = 791;
                this._errHandler.sync(this);
                _la = this._input.LA(1);
            }
            break;

        case 2:
            this.enterOuterAlt(localctx, 2);

            break;

        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function GccAttributeContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_gccAttribute;
    return this;
}

GccAttributeContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
GccAttributeContext.prototype.constructor = GccAttributeContext;

GccAttributeContext.prototype.argumentExpressionList = function() {
    return this.getTypedRuleContext(ArgumentExpressionListContext,0);
};

GccAttributeContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterGccAttribute(this);
	}
};

GccAttributeContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitGccAttribute(this);
	}
};




CParser.GccAttributeContext = GccAttributeContext;

CParser.prototype.gccAttribute = function() {

    var localctx = new GccAttributeContext(this, this._ctx, this.state);
    this.enterRule(localctx, 104, CParser.RULE_gccAttribute);
    var _la = 0; // Token type
    try {
        this.state = 804;
        switch(this._input.LA(1)) {
        case CParser.T__0:
        case CParser.T__1:
        case CParser.T__2:
        case CParser.T__3:
        case CParser.T__4:
        case CParser.T__5:
        case CParser.T__6:
        case CParser.T__7:
        case CParser.T__8:
        case CParser.T__9:
        case CParser.T__10:
        case CParser.T__11:
        case CParser.T__12:
        case CParser.T__13:
        case CParser.Auto:
        case CParser.Break:
        case CParser.Case:
        case CParser.Char:
        case CParser.Const:
        case CParser.Continue:
        case CParser.Default:
        case CParser.Do:
        case CParser.Double:
        case CParser.Else:
        case CParser.Enum:
        case CParser.Extern:
        case CParser.Float:
        case CParser.For:
        case CParser.Goto:
        case CParser.If:
        case CParser.Inline:
        case CParser.Int:
        case CParser.Long:
        case CParser.Register:
        case CParser.Restrict:
        case CParser.Return:
        case CParser.Short:
        case CParser.Signed:
        case CParser.Sizeof:
        case CParser.Static:
        case CParser.Struct:
        case CParser.Switch:
        case CParser.Typedef:
        case CParser.Union:
        case CParser.Unsigned:
        case CParser.Void:
        case CParser.Volatile:
        case CParser.While:
        case CParser.Alignas:
        case CParser.Alignof:
        case CParser.Atomic:
        case CParser.Bool:
        case CParser.Complex:
        case CParser.Generic:
        case CParser.Imaginary:
        case CParser.Noreturn:
        case CParser.StaticAssert:
        case CParser.ThreadLocal:
        case CParser.LeftBracket:
        case CParser.RightBracket:
        case CParser.LeftBrace:
        case CParser.RightBrace:
        case CParser.Less:
        case CParser.LessEqual:
        case CParser.Greater:
        case CParser.GreaterEqual:
        case CParser.LeftShift:
        case CParser.RightShift:
        case CParser.Plus:
        case CParser.PlusPlus:
        case CParser.Minus:
        case CParser.MinusMinus:
        case CParser.Star:
        case CParser.Div:
        case CParser.Mod:
        case CParser.And:
        case CParser.Or:
        case CParser.AndAnd:
        case CParser.OrOr:
        case CParser.Caret:
        case CParser.Not:
        case CParser.Tilde:
        case CParser.Question:
        case CParser.Colon:
        case CParser.Semi:
        case CParser.Assign:
        case CParser.StarAssign:
        case CParser.DivAssign:
        case CParser.ModAssign:
        case CParser.PlusAssign:
        case CParser.MinusAssign:
        case CParser.LeftShiftAssign:
        case CParser.RightShiftAssign:
        case CParser.AndAssign:
        case CParser.XorAssign:
        case CParser.OrAssign:
        case CParser.Equal:
        case CParser.NotEqual:
        case CParser.Arrow:
        case CParser.Dot:
        case CParser.Ellipsis:
        case CParser.Identifier:
        case CParser.Constant:
        case CParser.StringLiteral:
        case CParser.LineDirective:
        case CParser.PragmaDirective:
        case CParser.Whitespace:
        case CParser.Newline:
        case CParser.BlockComment:
        case CParser.LineComment:
            this.enterOuterAlt(localctx, 1);
            this.state = 795;
            _la = this._input.LA(1);
            if(_la<=0 || ((((_la - 59)) & ~0x1f) == 0 && ((1 << (_la - 59)) & ((1 << (CParser.LeftParen - 59)) | (1 << (CParser.RightParen - 59)) | (1 << (CParser.Comma - 59)))) !== 0)) {
            this._errHandler.recoverInline(this);
            }
            else {
                this.consume();
            }
            this.state = 801;
            _la = this._input.LA(1);
            if(_la===CParser.LeftParen) {
                this.state = 796;
                this.match(CParser.LeftParen);
                this.state = 798;
                _la = this._input.LA(1);
                if((((_la) & ~0x1f) == 0 && ((1 << _la) & ((1 << CParser.T__0) | (1 << CParser.T__1) | (1 << CParser.T__2))) !== 0) || ((((_la - 39)) & ~0x1f) == 0 && ((1 << (_la - 39)) & ((1 << (CParser.Sizeof - 39)) | (1 << (CParser.Alignof - 39)) | (1 << (CParser.Generic - 39)) | (1 << (CParser.LeftParen - 39)))) !== 0) || ((((_la - 71)) & ~0x1f) == 0 && ((1 << (_la - 71)) & ((1 << (CParser.Plus - 71)) | (1 << (CParser.PlusPlus - 71)) | (1 << (CParser.Minus - 71)) | (1 << (CParser.MinusMinus - 71)) | (1 << (CParser.Star - 71)) | (1 << (CParser.And - 71)) | (1 << (CParser.AndAnd - 71)) | (1 << (CParser.Not - 71)) | (1 << (CParser.Tilde - 71)))) !== 0) || ((((_la - 105)) & ~0x1f) == 0 && ((1 << (_la - 105)) & ((1 << (CParser.Identifier - 105)) | (1 << (CParser.Constant - 105)) | (1 << (CParser.StringLiteral - 105)))) !== 0)) {
                    this.state = 797;
                    this.argumentExpressionList(0);
                }

                this.state = 800;
                this.match(CParser.RightParen);
            }

            break;
        case CParser.RightParen:
        case CParser.Comma:
            this.enterOuterAlt(localctx, 2);

            break;
        default:
            throw new antlr4.error.NoViableAltException(this);
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function NestedParenthesesBlockContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_nestedParenthesesBlock;
    return this;
}

NestedParenthesesBlockContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
NestedParenthesesBlockContext.prototype.constructor = NestedParenthesesBlockContext;

NestedParenthesesBlockContext.prototype.nestedParenthesesBlock = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(NestedParenthesesBlockContext);
    } else {
        return this.getTypedRuleContext(NestedParenthesesBlockContext,i);
    }
};

NestedParenthesesBlockContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterNestedParenthesesBlock(this);
	}
};

NestedParenthesesBlockContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitNestedParenthesesBlock(this);
	}
};




CParser.NestedParenthesesBlockContext = NestedParenthesesBlockContext;

CParser.prototype.nestedParenthesesBlock = function() {

    var localctx = new NestedParenthesesBlockContext(this, this._ctx, this.state);
    this.enterRule(localctx, 106, CParser.RULE_nestedParenthesesBlock);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 813;
        this._errHandler.sync(this);
        _la = this._input.LA(1);
        while((((_la) & ~0x1f) == 0 && ((1 << _la) & ((1 << CParser.T__0) | (1 << CParser.T__1) | (1 << CParser.T__2) | (1 << CParser.T__3) | (1 << CParser.T__4) | (1 << CParser.T__5) | (1 << CParser.T__6) | (1 << CParser.T__7) | (1 << CParser.T__8) | (1 << CParser.T__9) | (1 << CParser.T__10) | (1 << CParser.T__11) | (1 << CParser.T__12) | (1 << CParser.T__13) | (1 << CParser.Auto) | (1 << CParser.Break) | (1 << CParser.Case) | (1 << CParser.Char) | (1 << CParser.Const) | (1 << CParser.Continue) | (1 << CParser.Default) | (1 << CParser.Do) | (1 << CParser.Double) | (1 << CParser.Else) | (1 << CParser.Enum) | (1 << CParser.Extern) | (1 << CParser.Float) | (1 << CParser.For) | (1 << CParser.Goto) | (1 << CParser.If) | (1 << CParser.Inline))) !== 0) || ((((_la - 32)) & ~0x1f) == 0 && ((1 << (_la - 32)) & ((1 << (CParser.Int - 32)) | (1 << (CParser.Long - 32)) | (1 << (CParser.Register - 32)) | (1 << (CParser.Restrict - 32)) | (1 << (CParser.Return - 32)) | (1 << (CParser.Short - 32)) | (1 << (CParser.Signed - 32)) | (1 << (CParser.Sizeof - 32)) | (1 << (CParser.Static - 32)) | (1 << (CParser.Struct - 32)) | (1 << (CParser.Switch - 32)) | (1 << (CParser.Typedef - 32)) | (1 << (CParser.Union - 32)) | (1 << (CParser.Unsigned - 32)) | (1 << (CParser.Void - 32)) | (1 << (CParser.Volatile - 32)) | (1 << (CParser.While - 32)) | (1 << (CParser.Alignas - 32)) | (1 << (CParser.Alignof - 32)) | (1 << (CParser.Atomic - 32)) | (1 << (CParser.Bool - 32)) | (1 << (CParser.Complex - 32)) | (1 << (CParser.Generic - 32)) | (1 << (CParser.Imaginary - 32)) | (1 << (CParser.Noreturn - 32)) | (1 << (CParser.StaticAssert - 32)) | (1 << (CParser.ThreadLocal - 32)) | (1 << (CParser.LeftParen - 32)) | (1 << (CParser.LeftBracket - 32)) | (1 << (CParser.RightBracket - 32)) | (1 << (CParser.LeftBrace - 32)))) !== 0) || ((((_la - 64)) & ~0x1f) == 0 && ((1 << (_la - 64)) & ((1 << (CParser.RightBrace - 64)) | (1 << (CParser.Less - 64)) | (1 << (CParser.LessEqual - 64)) | (1 << (CParser.Greater - 64)) | (1 << (CParser.GreaterEqual - 64)) | (1 << (CParser.LeftShift - 64)) | (1 << (CParser.RightShift - 64)) | (1 << (CParser.Plus - 64)) | (1 << (CParser.PlusPlus - 64)) | (1 << (CParser.Minus - 64)) | (1 << (CParser.MinusMinus - 64)) | (1 << (CParser.Star - 64)) | (1 << (CParser.Div - 64)) | (1 << (CParser.Mod - 64)) | (1 << (CParser.And - 64)) | (1 << (CParser.Or - 64)) | (1 << (CParser.AndAnd - 64)) | (1 << (CParser.OrOr - 64)) | (1 << (CParser.Caret - 64)) | (1 << (CParser.Not - 64)) | (1 << (CParser.Tilde - 64)) | (1 << (CParser.Question - 64)) | (1 << (CParser.Colon - 64)) | (1 << (CParser.Semi - 64)) | (1 << (CParser.Comma - 64)) | (1 << (CParser.Assign - 64)) | (1 << (CParser.StarAssign - 64)) | (1 << (CParser.DivAssign - 64)) | (1 << (CParser.ModAssign - 64)) | (1 << (CParser.PlusAssign - 64)) | (1 << (CParser.MinusAssign - 64)) | (1 << (CParser.LeftShiftAssign - 64)))) !== 0) || ((((_la - 96)) & ~0x1f) == 0 && ((1 << (_la - 96)) & ((1 << (CParser.RightShiftAssign - 96)) | (1 << (CParser.AndAssign - 96)) | (1 << (CParser.XorAssign - 96)) | (1 << (CParser.OrAssign - 96)) | (1 << (CParser.Equal - 96)) | (1 << (CParser.NotEqual - 96)) | (1 << (CParser.Arrow - 96)) | (1 << (CParser.Dot - 96)) | (1 << (CParser.Ellipsis - 96)) | (1 << (CParser.Identifier - 96)) | (1 << (CParser.Constant - 96)) | (1 << (CParser.StringLiteral - 96)) | (1 << (CParser.LineDirective - 96)) | (1 << (CParser.PragmaDirective - 96)) | (1 << (CParser.Whitespace - 96)) | (1 << (CParser.Newline - 96)) | (1 << (CParser.BlockComment - 96)) | (1 << (CParser.LineComment - 96)))) !== 0)) {
            this.state = 811;
            switch(this._input.LA(1)) {
            case CParser.T__0:
            case CParser.T__1:
            case CParser.T__2:
            case CParser.T__3:
            case CParser.T__4:
            case CParser.T__5:
            case CParser.T__6:
            case CParser.T__7:
            case CParser.T__8:
            case CParser.T__9:
            case CParser.T__10:
            case CParser.T__11:
            case CParser.T__12:
            case CParser.T__13:
            case CParser.Auto:
            case CParser.Break:
            case CParser.Case:
            case CParser.Char:
            case CParser.Const:
            case CParser.Continue:
            case CParser.Default:
            case CParser.Do:
            case CParser.Double:
            case CParser.Else:
            case CParser.Enum:
            case CParser.Extern:
            case CParser.Float:
            case CParser.For:
            case CParser.Goto:
            case CParser.If:
            case CParser.Inline:
            case CParser.Int:
            case CParser.Long:
            case CParser.Register:
            case CParser.Restrict:
            case CParser.Return:
            case CParser.Short:
            case CParser.Signed:
            case CParser.Sizeof:
            case CParser.Static:
            case CParser.Struct:
            case CParser.Switch:
            case CParser.Typedef:
            case CParser.Union:
            case CParser.Unsigned:
            case CParser.Void:
            case CParser.Volatile:
            case CParser.While:
            case CParser.Alignas:
            case CParser.Alignof:
            case CParser.Atomic:
            case CParser.Bool:
            case CParser.Complex:
            case CParser.Generic:
            case CParser.Imaginary:
            case CParser.Noreturn:
            case CParser.StaticAssert:
            case CParser.ThreadLocal:
            case CParser.LeftBracket:
            case CParser.RightBracket:
            case CParser.LeftBrace:
            case CParser.RightBrace:
            case CParser.Less:
            case CParser.LessEqual:
            case CParser.Greater:
            case CParser.GreaterEqual:
            case CParser.LeftShift:
            case CParser.RightShift:
            case CParser.Plus:
            case CParser.PlusPlus:
            case CParser.Minus:
            case CParser.MinusMinus:
            case CParser.Star:
            case CParser.Div:
            case CParser.Mod:
            case CParser.And:
            case CParser.Or:
            case CParser.AndAnd:
            case CParser.OrOr:
            case CParser.Caret:
            case CParser.Not:
            case CParser.Tilde:
            case CParser.Question:
            case CParser.Colon:
            case CParser.Semi:
            case CParser.Comma:
            case CParser.Assign:
            case CParser.StarAssign:
            case CParser.DivAssign:
            case CParser.ModAssign:
            case CParser.PlusAssign:
            case CParser.MinusAssign:
            case CParser.LeftShiftAssign:
            case CParser.RightShiftAssign:
            case CParser.AndAssign:
            case CParser.XorAssign:
            case CParser.OrAssign:
            case CParser.Equal:
            case CParser.NotEqual:
            case CParser.Arrow:
            case CParser.Dot:
            case CParser.Ellipsis:
            case CParser.Identifier:
            case CParser.Constant:
            case CParser.StringLiteral:
            case CParser.LineDirective:
            case CParser.PragmaDirective:
            case CParser.Whitespace:
            case CParser.Newline:
            case CParser.BlockComment:
            case CParser.LineComment:
                this.state = 806;
                _la = this._input.LA(1);
                if(_la<=0 || _la===CParser.LeftParen || _la===CParser.RightParen) {
                this._errHandler.recoverInline(this);
                }
                else {
                    this.consume();
                }
                break;
            case CParser.LeftParen:
                this.state = 807;
                this.match(CParser.LeftParen);
                this.state = 808;
                this.nestedParenthesesBlock();
                this.state = 809;
                this.match(CParser.RightParen);
                break;
            default:
                throw new antlr4.error.NoViableAltException(this);
            }
            this.state = 815;
            this._errHandler.sync(this);
            _la = this._input.LA(1);
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function PointerContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_pointer;
    return this;
}

PointerContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
PointerContext.prototype.constructor = PointerContext;

PointerContext.prototype.typeQualifierList = function() {
    return this.getTypedRuleContext(TypeQualifierListContext,0);
};

PointerContext.prototype.pointer = function() {
    return this.getTypedRuleContext(PointerContext,0);
};

PointerContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterPointer(this);
	}
};

PointerContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitPointer(this);
	}
};




CParser.PointerContext = PointerContext;

CParser.prototype.pointer = function() {

    var localctx = new PointerContext(this, this._ctx, this.state);
    this.enterRule(localctx, 108, CParser.RULE_pointer);
    var _la = 0; // Token type
    try {
        this.state = 834;
        var la_ = this._interp.adaptivePredict(this._input,79,this._ctx);
        switch(la_) {
        case 1:
            this.enterOuterAlt(localctx, 1);
            this.state = 816;
            this.match(CParser.Star);
            this.state = 818;
            var la_ = this._interp.adaptivePredict(this._input,75,this._ctx);
            if(la_===1) {
                this.state = 817;
                this.typeQualifierList(0);

            }
            break;

        case 2:
            this.enterOuterAlt(localctx, 2);
            this.state = 820;
            this.match(CParser.Star);
            this.state = 822;
            _la = this._input.LA(1);
            if(_la===CParser.Const || ((((_la - 35)) & ~0x1f) == 0 && ((1 << (_la - 35)) & ((1 << (CParser.Restrict - 35)) | (1 << (CParser.Volatile - 35)) | (1 << (CParser.Atomic - 35)))) !== 0)) {
                this.state = 821;
                this.typeQualifierList(0);
            }

            this.state = 824;
            this.pointer();
            break;

        case 3:
            this.enterOuterAlt(localctx, 3);
            this.state = 825;
            this.match(CParser.Caret);
            this.state = 827;
            var la_ = this._interp.adaptivePredict(this._input,77,this._ctx);
            if(la_===1) {
                this.state = 826;
                this.typeQualifierList(0);

            }
            break;

        case 4:
            this.enterOuterAlt(localctx, 4);
            this.state = 829;
            this.match(CParser.Caret);
            this.state = 831;
            _la = this._input.LA(1);
            if(_la===CParser.Const || ((((_la - 35)) & ~0x1f) == 0 && ((1 << (_la - 35)) & ((1 << (CParser.Restrict - 35)) | (1 << (CParser.Volatile - 35)) | (1 << (CParser.Atomic - 35)))) !== 0)) {
                this.state = 830;
                this.typeQualifierList(0);
            }

            this.state = 833;
            this.pointer();
            break;

        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function TypeQualifierListContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_typeQualifierList;
    return this;
}

TypeQualifierListContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
TypeQualifierListContext.prototype.constructor = TypeQualifierListContext;

TypeQualifierListContext.prototype.typeQualifier = function() {
    return this.getTypedRuleContext(TypeQualifierContext,0);
};

TypeQualifierListContext.prototype.typeQualifierList = function() {
    return this.getTypedRuleContext(TypeQualifierListContext,0);
};

TypeQualifierListContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterTypeQualifierList(this);
	}
};

TypeQualifierListContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitTypeQualifierList(this);
	}
};



CParser.prototype.typeQualifierList = function(_p) {
	if(_p===undefined) {
	    _p = 0;
	}
    var _parentctx = this._ctx;
    var _parentState = this.state;
    var localctx = new TypeQualifierListContext(this, this._ctx, _parentState);
    var _prevctx = localctx;
    var _startState = 110;
    this.enterRecursionRule(localctx, 110, CParser.RULE_typeQualifierList, _p);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 837;
        this.typeQualifier();
        this._ctx.stop = this._input.LT(-1);
        this.state = 843;
        this._errHandler.sync(this);
        var _alt = this._interp.adaptivePredict(this._input,80,this._ctx)
        while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
            if(_alt===1) {
                if(this._parseListeners!==null) {
                    this.triggerExitRuleEvent();
                }
                _prevctx = localctx;
                localctx = new TypeQualifierListContext(this, _parentctx, _parentState);
                this.pushNewRecursionContext(localctx, _startState, CParser.RULE_typeQualifierList);
                this.state = 839;
                if (!( this.precpred(this._ctx, 1))) {
                    throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 1)");
                }
                this.state = 840;
                this.typeQualifier(); 
            }
            this.state = 845;
            this._errHandler.sync(this);
            _alt = this._interp.adaptivePredict(this._input,80,this._ctx);
        }

    } catch( error) {
        if(error instanceof antlr4.error.RecognitionException) {
	        localctx.exception = error;
	        this._errHandler.reportError(this, error);
	        this._errHandler.recover(this, error);
	    } else {
	    	throw error;
	    }
    } finally {
        this.unrollRecursionContexts(_parentctx)
    }
    return localctx;
};

function ParameterTypeListContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_parameterTypeList;
    return this;
}

ParameterTypeListContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
ParameterTypeListContext.prototype.constructor = ParameterTypeListContext;

ParameterTypeListContext.prototype.parameterList = function() {
    return this.getTypedRuleContext(ParameterListContext,0);
};

ParameterTypeListContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterParameterTypeList(this);
	}
};

ParameterTypeListContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitParameterTypeList(this);
	}
};




CParser.ParameterTypeListContext = ParameterTypeListContext;

CParser.prototype.parameterTypeList = function() {

    var localctx = new ParameterTypeListContext(this, this._ctx, this.state);
    this.enterRule(localctx, 112, CParser.RULE_parameterTypeList);
    try {
        this.state = 851;
        var la_ = this._interp.adaptivePredict(this._input,81,this._ctx);
        switch(la_) {
        case 1:
            this.enterOuterAlt(localctx, 1);
            this.state = 846;
            this.parameterList(0);
            break;

        case 2:
            this.enterOuterAlt(localctx, 2);
            this.state = 847;
            this.parameterList(0);
            this.state = 848;
            this.match(CParser.Comma);
            this.state = 849;
            this.match(CParser.Ellipsis);
            break;

        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function ParameterListContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_parameterList;
    return this;
}

ParameterListContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
ParameterListContext.prototype.constructor = ParameterListContext;

ParameterListContext.prototype.parameterDeclaration = function() {
    return this.getTypedRuleContext(ParameterDeclarationContext,0);
};

ParameterListContext.prototype.parameterList = function() {
    return this.getTypedRuleContext(ParameterListContext,0);
};

ParameterListContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterParameterList(this);
	}
};

ParameterListContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitParameterList(this);
	}
};



CParser.prototype.parameterList = function(_p) {
	if(_p===undefined) {
	    _p = 0;
	}
    var _parentctx = this._ctx;
    var _parentState = this.state;
    var localctx = new ParameterListContext(this, this._ctx, _parentState);
    var _prevctx = localctx;
    var _startState = 114;
    this.enterRecursionRule(localctx, 114, CParser.RULE_parameterList, _p);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 854;
        this.parameterDeclaration();
        this._ctx.stop = this._input.LT(-1);
        this.state = 861;
        this._errHandler.sync(this);
        var _alt = this._interp.adaptivePredict(this._input,82,this._ctx)
        while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
            if(_alt===1) {
                if(this._parseListeners!==null) {
                    this.triggerExitRuleEvent();
                }
                _prevctx = localctx;
                localctx = new ParameterListContext(this, _parentctx, _parentState);
                this.pushNewRecursionContext(localctx, _startState, CParser.RULE_parameterList);
                this.state = 856;
                if (!( this.precpred(this._ctx, 1))) {
                    throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 1)");
                }
                this.state = 857;
                this.match(CParser.Comma);
                this.state = 858;
                this.parameterDeclaration(); 
            }
            this.state = 863;
            this._errHandler.sync(this);
            _alt = this._interp.adaptivePredict(this._input,82,this._ctx);
        }

    } catch( error) {
        if(error instanceof antlr4.error.RecognitionException) {
	        localctx.exception = error;
	        this._errHandler.reportError(this, error);
	        this._errHandler.recover(this, error);
	    } else {
	    	throw error;
	    }
    } finally {
        this.unrollRecursionContexts(_parentctx)
    }
    return localctx;
};

function ParameterDeclarationContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_parameterDeclaration;
    return this;
}

ParameterDeclarationContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
ParameterDeclarationContext.prototype.constructor = ParameterDeclarationContext;

ParameterDeclarationContext.prototype.declarationSpecifiers = function() {
    return this.getTypedRuleContext(DeclarationSpecifiersContext,0);
};

ParameterDeclarationContext.prototype.declarator = function() {
    return this.getTypedRuleContext(DeclaratorContext,0);
};

ParameterDeclarationContext.prototype.declarationSpecifiers2 = function() {
    return this.getTypedRuleContext(DeclarationSpecifiers2Context,0);
};

ParameterDeclarationContext.prototype.abstractDeclarator = function() {
    return this.getTypedRuleContext(AbstractDeclaratorContext,0);
};

ParameterDeclarationContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterParameterDeclaration(this);
	}
};

ParameterDeclarationContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitParameterDeclaration(this);
	}
};




CParser.ParameterDeclarationContext = ParameterDeclarationContext;

CParser.prototype.parameterDeclaration = function() {

    var localctx = new ParameterDeclarationContext(this, this._ctx, this.state);
    this.enterRule(localctx, 116, CParser.RULE_parameterDeclaration);
    try {
        this.state = 871;
        var la_ = this._interp.adaptivePredict(this._input,84,this._ctx);
        switch(la_) {
        case 1:
            this.enterOuterAlt(localctx, 1);
            this.state = 864;
            this.declarationSpecifiers();
            this.state = 865;
            this.declarator();
            break;

        case 2:
            this.enterOuterAlt(localctx, 2);
            this.state = 867;
            this.declarationSpecifiers2();
            this.state = 869;
            var la_ = this._interp.adaptivePredict(this._input,83,this._ctx);
            if(la_===1) {
                this.state = 868;
                this.abstractDeclarator();

            }
            break;

        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function IdentifierListContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_identifierList;
    return this;
}

IdentifierListContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
IdentifierListContext.prototype.constructor = IdentifierListContext;

IdentifierListContext.prototype.Identifier = function() {
    return this.getToken(CParser.Identifier, 0);
};

IdentifierListContext.prototype.identifierList = function() {
    return this.getTypedRuleContext(IdentifierListContext,0);
};

IdentifierListContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterIdentifierList(this);
	}
};

IdentifierListContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitIdentifierList(this);
	}
};



CParser.prototype.identifierList = function(_p) {
	if(_p===undefined) {
	    _p = 0;
	}
    var _parentctx = this._ctx;
    var _parentState = this.state;
    var localctx = new IdentifierListContext(this, this._ctx, _parentState);
    var _prevctx = localctx;
    var _startState = 118;
    this.enterRecursionRule(localctx, 118, CParser.RULE_identifierList, _p);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 874;
        this.match(CParser.Identifier);
        this._ctx.stop = this._input.LT(-1);
        this.state = 881;
        this._errHandler.sync(this);
        var _alt = this._interp.adaptivePredict(this._input,85,this._ctx)
        while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
            if(_alt===1) {
                if(this._parseListeners!==null) {
                    this.triggerExitRuleEvent();
                }
                _prevctx = localctx;
                localctx = new IdentifierListContext(this, _parentctx, _parentState);
                this.pushNewRecursionContext(localctx, _startState, CParser.RULE_identifierList);
                this.state = 876;
                if (!( this.precpred(this._ctx, 1))) {
                    throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 1)");
                }
                this.state = 877;
                this.match(CParser.Comma);
                this.state = 878;
                this.match(CParser.Identifier); 
            }
            this.state = 883;
            this._errHandler.sync(this);
            _alt = this._interp.adaptivePredict(this._input,85,this._ctx);
        }

    } catch( error) {
        if(error instanceof antlr4.error.RecognitionException) {
	        localctx.exception = error;
	        this._errHandler.reportError(this, error);
	        this._errHandler.recover(this, error);
	    } else {
	    	throw error;
	    }
    } finally {
        this.unrollRecursionContexts(_parentctx)
    }
    return localctx;
};

function TypeNameContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_typeName;
    return this;
}

TypeNameContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
TypeNameContext.prototype.constructor = TypeNameContext;

TypeNameContext.prototype.specifierQualifierList = function() {
    return this.getTypedRuleContext(SpecifierQualifierListContext,0);
};

TypeNameContext.prototype.abstractDeclarator = function() {
    return this.getTypedRuleContext(AbstractDeclaratorContext,0);
};

TypeNameContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterTypeName(this);
	}
};

TypeNameContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitTypeName(this);
	}
};




CParser.TypeNameContext = TypeNameContext;

CParser.prototype.typeName = function() {

    var localctx = new TypeNameContext(this, this._ctx, this.state);
    this.enterRule(localctx, 120, CParser.RULE_typeName);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 884;
        this.specifierQualifierList();
        this.state = 886;
        _la = this._input.LA(1);
        if(((((_la - 59)) & ~0x1f) == 0 && ((1 << (_la - 59)) & ((1 << (CParser.LeftParen - 59)) | (1 << (CParser.LeftBracket - 59)) | (1 << (CParser.Star - 59)) | (1 << (CParser.Caret - 59)))) !== 0)) {
            this.state = 885;
            this.abstractDeclarator();
        }

    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function AbstractDeclaratorContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_abstractDeclarator;
    return this;
}

AbstractDeclaratorContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
AbstractDeclaratorContext.prototype.constructor = AbstractDeclaratorContext;

AbstractDeclaratorContext.prototype.pointer = function() {
    return this.getTypedRuleContext(PointerContext,0);
};

AbstractDeclaratorContext.prototype.directAbstractDeclarator = function() {
    return this.getTypedRuleContext(DirectAbstractDeclaratorContext,0);
};

AbstractDeclaratorContext.prototype.gccDeclaratorExtension = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(GccDeclaratorExtensionContext);
    } else {
        return this.getTypedRuleContext(GccDeclaratorExtensionContext,i);
    }
};

AbstractDeclaratorContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterAbstractDeclarator(this);
	}
};

AbstractDeclaratorContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitAbstractDeclarator(this);
	}
};




CParser.AbstractDeclaratorContext = AbstractDeclaratorContext;

CParser.prototype.abstractDeclarator = function() {

    var localctx = new AbstractDeclaratorContext(this, this._ctx, this.state);
    this.enterRule(localctx, 122, CParser.RULE_abstractDeclarator);
    var _la = 0; // Token type
    try {
        this.state = 899;
        var la_ = this._interp.adaptivePredict(this._input,89,this._ctx);
        switch(la_) {
        case 1:
            this.enterOuterAlt(localctx, 1);
            this.state = 888;
            this.pointer();
            break;

        case 2:
            this.enterOuterAlt(localctx, 2);
            this.state = 890;
            _la = this._input.LA(1);
            if(_la===CParser.Star || _la===CParser.Caret) {
                this.state = 889;
                this.pointer();
            }

            this.state = 892;
            this.directAbstractDeclarator(0);
            this.state = 896;
            this._errHandler.sync(this);
            var _alt = this._interp.adaptivePredict(this._input,88,this._ctx)
            while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
                if(_alt===1) {
                    this.state = 893;
                    this.gccDeclaratorExtension(); 
                }
                this.state = 898;
                this._errHandler.sync(this);
                _alt = this._interp.adaptivePredict(this._input,88,this._ctx);
            }

            break;

        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function DirectAbstractDeclaratorContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_directAbstractDeclarator;
    return this;
}

DirectAbstractDeclaratorContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
DirectAbstractDeclaratorContext.prototype.constructor = DirectAbstractDeclaratorContext;

DirectAbstractDeclaratorContext.prototype.abstractDeclarator = function() {
    return this.getTypedRuleContext(AbstractDeclaratorContext,0);
};

DirectAbstractDeclaratorContext.prototype.gccDeclaratorExtension = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(GccDeclaratorExtensionContext);
    } else {
        return this.getTypedRuleContext(GccDeclaratorExtensionContext,i);
    }
};

DirectAbstractDeclaratorContext.prototype.typeQualifierList = function() {
    return this.getTypedRuleContext(TypeQualifierListContext,0);
};

DirectAbstractDeclaratorContext.prototype.assignmentExpression = function() {
    return this.getTypedRuleContext(AssignmentExpressionContext,0);
};

DirectAbstractDeclaratorContext.prototype.parameterTypeList = function() {
    return this.getTypedRuleContext(ParameterTypeListContext,0);
};

DirectAbstractDeclaratorContext.prototype.directAbstractDeclarator = function() {
    return this.getTypedRuleContext(DirectAbstractDeclaratorContext,0);
};

DirectAbstractDeclaratorContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterDirectAbstractDeclarator(this);
	}
};

DirectAbstractDeclaratorContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitDirectAbstractDeclarator(this);
	}
};



CParser.prototype.directAbstractDeclarator = function(_p) {
	if(_p===undefined) {
	    _p = 0;
	}
    var _parentctx = this._ctx;
    var _parentState = this.state;
    var localctx = new DirectAbstractDeclaratorContext(this, this._ctx, _parentState);
    var _prevctx = localctx;
    var _startState = 124;
    this.enterRecursionRule(localctx, 124, CParser.RULE_directAbstractDeclarator, _p);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 947;
        var la_ = this._interp.adaptivePredict(this._input,96,this._ctx);
        switch(la_) {
        case 1:
            this.state = 902;
            this.match(CParser.LeftParen);
            this.state = 903;
            this.abstractDeclarator();
            this.state = 904;
            this.match(CParser.RightParen);
            this.state = 908;
            this._errHandler.sync(this);
            var _alt = this._interp.adaptivePredict(this._input,90,this._ctx)
            while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
                if(_alt===1) {
                    this.state = 905;
                    this.gccDeclaratorExtension(); 
                }
                this.state = 910;
                this._errHandler.sync(this);
                _alt = this._interp.adaptivePredict(this._input,90,this._ctx);
            }

            break;

        case 2:
            this.state = 911;
            this.match(CParser.LeftBracket);
            this.state = 913;
            _la = this._input.LA(1);
            if(_la===CParser.Const || ((((_la - 35)) & ~0x1f) == 0 && ((1 << (_la - 35)) & ((1 << (CParser.Restrict - 35)) | (1 << (CParser.Volatile - 35)) | (1 << (CParser.Atomic - 35)))) !== 0)) {
                this.state = 912;
                this.typeQualifierList(0);
            }

            this.state = 916;
            _la = this._input.LA(1);
            if((((_la) & ~0x1f) == 0 && ((1 << _la) & ((1 << CParser.T__0) | (1 << CParser.T__1) | (1 << CParser.T__2))) !== 0) || ((((_la - 39)) & ~0x1f) == 0 && ((1 << (_la - 39)) & ((1 << (CParser.Sizeof - 39)) | (1 << (CParser.Alignof - 39)) | (1 << (CParser.Generic - 39)) | (1 << (CParser.LeftParen - 39)))) !== 0) || ((((_la - 71)) & ~0x1f) == 0 && ((1 << (_la - 71)) & ((1 << (CParser.Plus - 71)) | (1 << (CParser.PlusPlus - 71)) | (1 << (CParser.Minus - 71)) | (1 << (CParser.MinusMinus - 71)) | (1 << (CParser.Star - 71)) | (1 << (CParser.And - 71)) | (1 << (CParser.AndAnd - 71)) | (1 << (CParser.Not - 71)) | (1 << (CParser.Tilde - 71)))) !== 0) || ((((_la - 105)) & ~0x1f) == 0 && ((1 << (_la - 105)) & ((1 << (CParser.Identifier - 105)) | (1 << (CParser.Constant - 105)) | (1 << (CParser.StringLiteral - 105)))) !== 0)) {
                this.state = 915;
                this.assignmentExpression();
            }

            this.state = 918;
            this.match(CParser.RightBracket);
            break;

        case 3:
            this.state = 919;
            this.match(CParser.LeftBracket);
            this.state = 920;
            this.match(CParser.Static);
            this.state = 922;
            _la = this._input.LA(1);
            if(_la===CParser.Const || ((((_la - 35)) & ~0x1f) == 0 && ((1 << (_la - 35)) & ((1 << (CParser.Restrict - 35)) | (1 << (CParser.Volatile - 35)) | (1 << (CParser.Atomic - 35)))) !== 0)) {
                this.state = 921;
                this.typeQualifierList(0);
            }

            this.state = 924;
            this.assignmentExpression();
            this.state = 925;
            this.match(CParser.RightBracket);
            break;

        case 4:
            this.state = 927;
            this.match(CParser.LeftBracket);
            this.state = 928;
            this.typeQualifierList(0);
            this.state = 929;
            this.match(CParser.Static);
            this.state = 930;
            this.assignmentExpression();
            this.state = 931;
            this.match(CParser.RightBracket);
            break;

        case 5:
            this.state = 933;
            this.match(CParser.LeftBracket);
            this.state = 934;
            this.match(CParser.Star);
            this.state = 935;
            this.match(CParser.RightBracket);
            break;

        case 6:
            this.state = 936;
            this.match(CParser.LeftParen);
            this.state = 938;
            _la = this._input.LA(1);
            if((((_la) & ~0x1f) == 0 && ((1 << _la) & ((1 << CParser.T__0) | (1 << CParser.T__3) | (1 << CParser.T__4) | (1 << CParser.T__5) | (1 << CParser.T__6) | (1 << CParser.T__7) | (1 << CParser.T__8) | (1 << CParser.T__9) | (1 << CParser.T__11) | (1 << CParser.Auto) | (1 << CParser.Char) | (1 << CParser.Const) | (1 << CParser.Double) | (1 << CParser.Enum) | (1 << CParser.Extern) | (1 << CParser.Float) | (1 << CParser.Inline))) !== 0) || ((((_la - 32)) & ~0x1f) == 0 && ((1 << (_la - 32)) & ((1 << (CParser.Int - 32)) | (1 << (CParser.Long - 32)) | (1 << (CParser.Register - 32)) | (1 << (CParser.Restrict - 32)) | (1 << (CParser.Short - 32)) | (1 << (CParser.Signed - 32)) | (1 << (CParser.Static - 32)) | (1 << (CParser.Struct - 32)) | (1 << (CParser.Typedef - 32)) | (1 << (CParser.Union - 32)) | (1 << (CParser.Unsigned - 32)) | (1 << (CParser.Void - 32)) | (1 << (CParser.Volatile - 32)) | (1 << (CParser.Alignas - 32)) | (1 << (CParser.Atomic - 32)) | (1 << (CParser.Bool - 32)) | (1 << (CParser.Complex - 32)) | (1 << (CParser.Noreturn - 32)) | (1 << (CParser.ThreadLocal - 32)))) !== 0) || _la===CParser.Identifier) {
                this.state = 937;
                this.parameterTypeList();
            }

            this.state = 940;
            this.match(CParser.RightParen);
            this.state = 944;
            this._errHandler.sync(this);
            var _alt = this._interp.adaptivePredict(this._input,95,this._ctx)
            while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
                if(_alt===1) {
                    this.state = 941;
                    this.gccDeclaratorExtension(); 
                }
                this.state = 946;
                this._errHandler.sync(this);
                _alt = this._interp.adaptivePredict(this._input,95,this._ctx);
            }

            break;

        }
        this._ctx.stop = this._input.LT(-1);
        this.state = 992;
        this._errHandler.sync(this);
        var _alt = this._interp.adaptivePredict(this._input,103,this._ctx)
        while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
            if(_alt===1) {
                if(this._parseListeners!==null) {
                    this.triggerExitRuleEvent();
                }
                _prevctx = localctx;
                this.state = 990;
                var la_ = this._interp.adaptivePredict(this._input,102,this._ctx);
                switch(la_) {
                case 1:
                    localctx = new DirectAbstractDeclaratorContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, CParser.RULE_directAbstractDeclarator);
                    this.state = 949;
                    if (!( this.precpred(this._ctx, 5))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 5)");
                    }
                    this.state = 950;
                    this.match(CParser.LeftBracket);
                    this.state = 952;
                    _la = this._input.LA(1);
                    if(_la===CParser.Const || ((((_la - 35)) & ~0x1f) == 0 && ((1 << (_la - 35)) & ((1 << (CParser.Restrict - 35)) | (1 << (CParser.Volatile - 35)) | (1 << (CParser.Atomic - 35)))) !== 0)) {
                        this.state = 951;
                        this.typeQualifierList(0);
                    }

                    this.state = 955;
                    _la = this._input.LA(1);
                    if((((_la) & ~0x1f) == 0 && ((1 << _la) & ((1 << CParser.T__0) | (1 << CParser.T__1) | (1 << CParser.T__2))) !== 0) || ((((_la - 39)) & ~0x1f) == 0 && ((1 << (_la - 39)) & ((1 << (CParser.Sizeof - 39)) | (1 << (CParser.Alignof - 39)) | (1 << (CParser.Generic - 39)) | (1 << (CParser.LeftParen - 39)))) !== 0) || ((((_la - 71)) & ~0x1f) == 0 && ((1 << (_la - 71)) & ((1 << (CParser.Plus - 71)) | (1 << (CParser.PlusPlus - 71)) | (1 << (CParser.Minus - 71)) | (1 << (CParser.MinusMinus - 71)) | (1 << (CParser.Star - 71)) | (1 << (CParser.And - 71)) | (1 << (CParser.AndAnd - 71)) | (1 << (CParser.Not - 71)) | (1 << (CParser.Tilde - 71)))) !== 0) || ((((_la - 105)) & ~0x1f) == 0 && ((1 << (_la - 105)) & ((1 << (CParser.Identifier - 105)) | (1 << (CParser.Constant - 105)) | (1 << (CParser.StringLiteral - 105)))) !== 0)) {
                        this.state = 954;
                        this.assignmentExpression();
                    }

                    this.state = 957;
                    this.match(CParser.RightBracket);
                    break;

                case 2:
                    localctx = new DirectAbstractDeclaratorContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, CParser.RULE_directAbstractDeclarator);
                    this.state = 958;
                    if (!( this.precpred(this._ctx, 4))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 4)");
                    }
                    this.state = 959;
                    this.match(CParser.LeftBracket);
                    this.state = 960;
                    this.match(CParser.Static);
                    this.state = 962;
                    _la = this._input.LA(1);
                    if(_la===CParser.Const || ((((_la - 35)) & ~0x1f) == 0 && ((1 << (_la - 35)) & ((1 << (CParser.Restrict - 35)) | (1 << (CParser.Volatile - 35)) | (1 << (CParser.Atomic - 35)))) !== 0)) {
                        this.state = 961;
                        this.typeQualifierList(0);
                    }

                    this.state = 964;
                    this.assignmentExpression();
                    this.state = 965;
                    this.match(CParser.RightBracket);
                    break;

                case 3:
                    localctx = new DirectAbstractDeclaratorContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, CParser.RULE_directAbstractDeclarator);
                    this.state = 967;
                    if (!( this.precpred(this._ctx, 3))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 3)");
                    }
                    this.state = 968;
                    this.match(CParser.LeftBracket);
                    this.state = 969;
                    this.typeQualifierList(0);
                    this.state = 970;
                    this.match(CParser.Static);
                    this.state = 971;
                    this.assignmentExpression();
                    this.state = 972;
                    this.match(CParser.RightBracket);
                    break;

                case 4:
                    localctx = new DirectAbstractDeclaratorContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, CParser.RULE_directAbstractDeclarator);
                    this.state = 974;
                    if (!( this.precpred(this._ctx, 2))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 2)");
                    }
                    this.state = 975;
                    this.match(CParser.LeftBracket);
                    this.state = 976;
                    this.match(CParser.Star);
                    this.state = 977;
                    this.match(CParser.RightBracket);
                    break;

                case 5:
                    localctx = new DirectAbstractDeclaratorContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, CParser.RULE_directAbstractDeclarator);
                    this.state = 978;
                    if (!( this.precpred(this._ctx, 1))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 1)");
                    }
                    this.state = 979;
                    this.match(CParser.LeftParen);
                    this.state = 981;
                    _la = this._input.LA(1);
                    if((((_la) & ~0x1f) == 0 && ((1 << _la) & ((1 << CParser.T__0) | (1 << CParser.T__3) | (1 << CParser.T__4) | (1 << CParser.T__5) | (1 << CParser.T__6) | (1 << CParser.T__7) | (1 << CParser.T__8) | (1 << CParser.T__9) | (1 << CParser.T__11) | (1 << CParser.Auto) | (1 << CParser.Char) | (1 << CParser.Const) | (1 << CParser.Double) | (1 << CParser.Enum) | (1 << CParser.Extern) | (1 << CParser.Float) | (1 << CParser.Inline))) !== 0) || ((((_la - 32)) & ~0x1f) == 0 && ((1 << (_la - 32)) & ((1 << (CParser.Int - 32)) | (1 << (CParser.Long - 32)) | (1 << (CParser.Register - 32)) | (1 << (CParser.Restrict - 32)) | (1 << (CParser.Short - 32)) | (1 << (CParser.Signed - 32)) | (1 << (CParser.Static - 32)) | (1 << (CParser.Struct - 32)) | (1 << (CParser.Typedef - 32)) | (1 << (CParser.Union - 32)) | (1 << (CParser.Unsigned - 32)) | (1 << (CParser.Void - 32)) | (1 << (CParser.Volatile - 32)) | (1 << (CParser.Alignas - 32)) | (1 << (CParser.Atomic - 32)) | (1 << (CParser.Bool - 32)) | (1 << (CParser.Complex - 32)) | (1 << (CParser.Noreturn - 32)) | (1 << (CParser.ThreadLocal - 32)))) !== 0) || _la===CParser.Identifier) {
                        this.state = 980;
                        this.parameterTypeList();
                    }

                    this.state = 983;
                    this.match(CParser.RightParen);
                    this.state = 987;
                    this._errHandler.sync(this);
                    var _alt = this._interp.adaptivePredict(this._input,101,this._ctx)
                    while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
                        if(_alt===1) {
                            this.state = 984;
                            this.gccDeclaratorExtension(); 
                        }
                        this.state = 989;
                        this._errHandler.sync(this);
                        _alt = this._interp.adaptivePredict(this._input,101,this._ctx);
                    }

                    break;

                } 
            }
            this.state = 994;
            this._errHandler.sync(this);
            _alt = this._interp.adaptivePredict(this._input,103,this._ctx);
        }

    } catch( error) {
        if(error instanceof antlr4.error.RecognitionException) {
	        localctx.exception = error;
	        this._errHandler.reportError(this, error);
	        this._errHandler.recover(this, error);
	    } else {
	    	throw error;
	    }
    } finally {
        this.unrollRecursionContexts(_parentctx)
    }
    return localctx;
};

function TypedefNameContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_typedefName;
    return this;
}

TypedefNameContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
TypedefNameContext.prototype.constructor = TypedefNameContext;

TypedefNameContext.prototype.Identifier = function() {
    return this.getToken(CParser.Identifier, 0);
};

TypedefNameContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterTypedefName(this);
	}
};

TypedefNameContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitTypedefName(this);
	}
};




CParser.TypedefNameContext = TypedefNameContext;

CParser.prototype.typedefName = function() {

    var localctx = new TypedefNameContext(this, this._ctx, this.state);
    this.enterRule(localctx, 126, CParser.RULE_typedefName);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 995;
        this.match(CParser.Identifier);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function InitializerContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_initializer;
    return this;
}

InitializerContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
InitializerContext.prototype.constructor = InitializerContext;

InitializerContext.prototype.assignmentExpression = function() {
    return this.getTypedRuleContext(AssignmentExpressionContext,0);
};

InitializerContext.prototype.initializerList = function() {
    return this.getTypedRuleContext(InitializerListContext,0);
};

InitializerContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterInitializer(this);
	}
};

InitializerContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitInitializer(this);
	}
};




CParser.InitializerContext = InitializerContext;

CParser.prototype.initializer = function() {

    var localctx = new InitializerContext(this, this._ctx, this.state);
    this.enterRule(localctx, 128, CParser.RULE_initializer);
    try {
        this.state = 1007;
        var la_ = this._interp.adaptivePredict(this._input,104,this._ctx);
        switch(la_) {
        case 1:
            this.enterOuterAlt(localctx, 1);
            this.state = 997;
            this.assignmentExpression();
            break;

        case 2:
            this.enterOuterAlt(localctx, 2);
            this.state = 998;
            this.match(CParser.LeftBrace);
            this.state = 999;
            this.initializerList(0);
            this.state = 1000;
            this.match(CParser.RightBrace);
            break;

        case 3:
            this.enterOuterAlt(localctx, 3);
            this.state = 1002;
            this.match(CParser.LeftBrace);
            this.state = 1003;
            this.initializerList(0);
            this.state = 1004;
            this.match(CParser.Comma);
            this.state = 1005;
            this.match(CParser.RightBrace);
            break;

        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function InitializerListContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_initializerList;
    return this;
}

InitializerListContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
InitializerListContext.prototype.constructor = InitializerListContext;

InitializerListContext.prototype.initializer = function() {
    return this.getTypedRuleContext(InitializerContext,0);
};

InitializerListContext.prototype.designation = function() {
    return this.getTypedRuleContext(DesignationContext,0);
};

InitializerListContext.prototype.initializerList = function() {
    return this.getTypedRuleContext(InitializerListContext,0);
};

InitializerListContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterInitializerList(this);
	}
};

InitializerListContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitInitializerList(this);
	}
};



CParser.prototype.initializerList = function(_p) {
	if(_p===undefined) {
	    _p = 0;
	}
    var _parentctx = this._ctx;
    var _parentState = this.state;
    var localctx = new InitializerListContext(this, this._ctx, _parentState);
    var _prevctx = localctx;
    var _startState = 130;
    this.enterRecursionRule(localctx, 130, CParser.RULE_initializerList, _p);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 1011;
        _la = this._input.LA(1);
        if(_la===CParser.LeftBracket || _la===CParser.Dot) {
            this.state = 1010;
            this.designation();
        }

        this.state = 1013;
        this.initializer();
        this._ctx.stop = this._input.LT(-1);
        this.state = 1023;
        this._errHandler.sync(this);
        var _alt = this._interp.adaptivePredict(this._input,107,this._ctx)
        while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
            if(_alt===1) {
                if(this._parseListeners!==null) {
                    this.triggerExitRuleEvent();
                }
                _prevctx = localctx;
                localctx = new InitializerListContext(this, _parentctx, _parentState);
                this.pushNewRecursionContext(localctx, _startState, CParser.RULE_initializerList);
                this.state = 1015;
                if (!( this.precpred(this._ctx, 1))) {
                    throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 1)");
                }
                this.state = 1016;
                this.match(CParser.Comma);
                this.state = 1018;
                _la = this._input.LA(1);
                if(_la===CParser.LeftBracket || _la===CParser.Dot) {
                    this.state = 1017;
                    this.designation();
                }

                this.state = 1020;
                this.initializer(); 
            }
            this.state = 1025;
            this._errHandler.sync(this);
            _alt = this._interp.adaptivePredict(this._input,107,this._ctx);
        }

    } catch( error) {
        if(error instanceof antlr4.error.RecognitionException) {
	        localctx.exception = error;
	        this._errHandler.reportError(this, error);
	        this._errHandler.recover(this, error);
	    } else {
	    	throw error;
	    }
    } finally {
        this.unrollRecursionContexts(_parentctx)
    }
    return localctx;
};

function DesignationContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_designation;
    return this;
}

DesignationContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
DesignationContext.prototype.constructor = DesignationContext;

DesignationContext.prototype.designatorList = function() {
    return this.getTypedRuleContext(DesignatorListContext,0);
};

DesignationContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterDesignation(this);
	}
};

DesignationContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitDesignation(this);
	}
};




CParser.DesignationContext = DesignationContext;

CParser.prototype.designation = function() {

    var localctx = new DesignationContext(this, this._ctx, this.state);
    this.enterRule(localctx, 132, CParser.RULE_designation);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 1026;
        this.designatorList(0);
        this.state = 1027;
        this.match(CParser.Assign);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function DesignatorListContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_designatorList;
    return this;
}

DesignatorListContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
DesignatorListContext.prototype.constructor = DesignatorListContext;

DesignatorListContext.prototype.designator = function() {
    return this.getTypedRuleContext(DesignatorContext,0);
};

DesignatorListContext.prototype.designatorList = function() {
    return this.getTypedRuleContext(DesignatorListContext,0);
};

DesignatorListContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterDesignatorList(this);
	}
};

DesignatorListContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitDesignatorList(this);
	}
};



CParser.prototype.designatorList = function(_p) {
	if(_p===undefined) {
	    _p = 0;
	}
    var _parentctx = this._ctx;
    var _parentState = this.state;
    var localctx = new DesignatorListContext(this, this._ctx, _parentState);
    var _prevctx = localctx;
    var _startState = 134;
    this.enterRecursionRule(localctx, 134, CParser.RULE_designatorList, _p);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 1030;
        this.designator();
        this._ctx.stop = this._input.LT(-1);
        this.state = 1036;
        this._errHandler.sync(this);
        var _alt = this._interp.adaptivePredict(this._input,108,this._ctx)
        while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
            if(_alt===1) {
                if(this._parseListeners!==null) {
                    this.triggerExitRuleEvent();
                }
                _prevctx = localctx;
                localctx = new DesignatorListContext(this, _parentctx, _parentState);
                this.pushNewRecursionContext(localctx, _startState, CParser.RULE_designatorList);
                this.state = 1032;
                if (!( this.precpred(this._ctx, 1))) {
                    throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 1)");
                }
                this.state = 1033;
                this.designator(); 
            }
            this.state = 1038;
            this._errHandler.sync(this);
            _alt = this._interp.adaptivePredict(this._input,108,this._ctx);
        }

    } catch( error) {
        if(error instanceof antlr4.error.RecognitionException) {
	        localctx.exception = error;
	        this._errHandler.reportError(this, error);
	        this._errHandler.recover(this, error);
	    } else {
	    	throw error;
	    }
    } finally {
        this.unrollRecursionContexts(_parentctx)
    }
    return localctx;
};

function DesignatorContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_designator;
    return this;
}

DesignatorContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
DesignatorContext.prototype.constructor = DesignatorContext;

DesignatorContext.prototype.constantExpression = function() {
    return this.getTypedRuleContext(ConstantExpressionContext,0);
};

DesignatorContext.prototype.Identifier = function() {
    return this.getToken(CParser.Identifier, 0);
};

DesignatorContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterDesignator(this);
	}
};

DesignatorContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitDesignator(this);
	}
};




CParser.DesignatorContext = DesignatorContext;

CParser.prototype.designator = function() {

    var localctx = new DesignatorContext(this, this._ctx, this.state);
    this.enterRule(localctx, 136, CParser.RULE_designator);
    try {
        this.state = 1045;
        switch(this._input.LA(1)) {
        case CParser.LeftBracket:
            this.enterOuterAlt(localctx, 1);
            this.state = 1039;
            this.match(CParser.LeftBracket);
            this.state = 1040;
            this.constantExpression();
            this.state = 1041;
            this.match(CParser.RightBracket);
            break;
        case CParser.Dot:
            this.enterOuterAlt(localctx, 2);
            this.state = 1043;
            this.match(CParser.Dot);
            this.state = 1044;
            this.match(CParser.Identifier);
            break;
        default:
            throw new antlr4.error.NoViableAltException(this);
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function StaticAssertDeclarationContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_staticAssertDeclaration;
    return this;
}

StaticAssertDeclarationContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
StaticAssertDeclarationContext.prototype.constructor = StaticAssertDeclarationContext;

StaticAssertDeclarationContext.prototype.constantExpression = function() {
    return this.getTypedRuleContext(ConstantExpressionContext,0);
};

StaticAssertDeclarationContext.prototype.StringLiteral = function(i) {
	if(i===undefined) {
		i = null;
	}
    if(i===null) {
        return this.getTokens(CParser.StringLiteral);
    } else {
        return this.getToken(CParser.StringLiteral, i);
    }
};


StaticAssertDeclarationContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterStaticAssertDeclaration(this);
	}
};

StaticAssertDeclarationContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitStaticAssertDeclaration(this);
	}
};




CParser.StaticAssertDeclarationContext = StaticAssertDeclarationContext;

CParser.prototype.staticAssertDeclaration = function() {

    var localctx = new StaticAssertDeclarationContext(this, this._ctx, this.state);
    this.enterRule(localctx, 138, CParser.RULE_staticAssertDeclaration);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 1047;
        this.match(CParser.StaticAssert);
        this.state = 1048;
        this.match(CParser.LeftParen);
        this.state = 1049;
        this.constantExpression();
        this.state = 1050;
        this.match(CParser.Comma);
        this.state = 1052; 
        this._errHandler.sync(this);
        _la = this._input.LA(1);
        do {
            this.state = 1051;
            this.match(CParser.StringLiteral);
            this.state = 1054; 
            this._errHandler.sync(this);
            _la = this._input.LA(1);
        } while(_la===CParser.StringLiteral);
        this.state = 1056;
        this.match(CParser.RightParen);
        this.state = 1057;
        this.match(CParser.Semi);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function StatementContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_statement;
    return this;
}

StatementContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
StatementContext.prototype.constructor = StatementContext;

StatementContext.prototype.labeledStatement = function() {
    return this.getTypedRuleContext(LabeledStatementContext,0);
};

StatementContext.prototype.compoundStatement = function() {
    return this.getTypedRuleContext(CompoundStatementContext,0);
};

StatementContext.prototype.expressionStatement = function() {
    return this.getTypedRuleContext(ExpressionStatementContext,0);
};

StatementContext.prototype.selectionStatement = function() {
    return this.getTypedRuleContext(SelectionStatementContext,0);
};

StatementContext.prototype.iterationStatement = function() {
    return this.getTypedRuleContext(IterationStatementContext,0);
};

StatementContext.prototype.jumpStatement = function() {
    return this.getTypedRuleContext(JumpStatementContext,0);
};

StatementContext.prototype.logicalOrExpression = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(LogicalOrExpressionContext);
    } else {
        return this.getTypedRuleContext(LogicalOrExpressionContext,i);
    }
};

StatementContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterStatement(this);
	}
};

StatementContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitStatement(this);
	}
};




CParser.StatementContext = StatementContext;

CParser.prototype.statement = function() {

    var localctx = new StatementContext(this, this._ctx, this.state);
    this.enterRule(localctx, 140, CParser.RULE_statement);
    var _la = 0; // Token type
    try {
        this.state = 1096;
        var la_ = this._interp.adaptivePredict(this._input,116,this._ctx);
        switch(la_) {
        case 1:
            this.enterOuterAlt(localctx, 1);
            this.state = 1059;
            this.labeledStatement();
            break;

        case 2:
            this.enterOuterAlt(localctx, 2);
            this.state = 1060;
            this.compoundStatement();
            break;

        case 3:
            this.enterOuterAlt(localctx, 3);
            this.state = 1061;
            this.expressionStatement();
            break;

        case 4:
            this.enterOuterAlt(localctx, 4);
            this.state = 1062;
            this.selectionStatement();
            break;

        case 5:
            this.enterOuterAlt(localctx, 5);
            this.state = 1063;
            this.iterationStatement();
            break;

        case 6:
            this.enterOuterAlt(localctx, 6);
            this.state = 1064;
            this.jumpStatement();
            break;

        case 7:
            this.enterOuterAlt(localctx, 7);
            this.state = 1065;
            _la = this._input.LA(1);
            if(!(_la===CParser.T__10 || _la===CParser.T__12)) {
            this._errHandler.recoverInline(this);
            }
            else {
                this.consume();
            }
            this.state = 1066;
            _la = this._input.LA(1);
            if(!(_la===CParser.T__13 || _la===CParser.Volatile)) {
            this._errHandler.recoverInline(this);
            }
            else {
                this.consume();
            }
            this.state = 1067;
            this.match(CParser.LeftParen);
            this.state = 1076;
            _la = this._input.LA(1);
            if((((_la) & ~0x1f) == 0 && ((1 << _la) & ((1 << CParser.T__0) | (1 << CParser.T__1) | (1 << CParser.T__2))) !== 0) || ((((_la - 39)) & ~0x1f) == 0 && ((1 << (_la - 39)) & ((1 << (CParser.Sizeof - 39)) | (1 << (CParser.Alignof - 39)) | (1 << (CParser.Generic - 39)) | (1 << (CParser.LeftParen - 39)))) !== 0) || ((((_la - 71)) & ~0x1f) == 0 && ((1 << (_la - 71)) & ((1 << (CParser.Plus - 71)) | (1 << (CParser.PlusPlus - 71)) | (1 << (CParser.Minus - 71)) | (1 << (CParser.MinusMinus - 71)) | (1 << (CParser.Star - 71)) | (1 << (CParser.And - 71)) | (1 << (CParser.AndAnd - 71)) | (1 << (CParser.Not - 71)) | (1 << (CParser.Tilde - 71)))) !== 0) || ((((_la - 105)) & ~0x1f) == 0 && ((1 << (_la - 105)) & ((1 << (CParser.Identifier - 105)) | (1 << (CParser.Constant - 105)) | (1 << (CParser.StringLiteral - 105)))) !== 0)) {
                this.state = 1068;
                this.logicalOrExpression(0);
                this.state = 1073;
                this._errHandler.sync(this);
                _la = this._input.LA(1);
                while(_la===CParser.Comma) {
                    this.state = 1069;
                    this.match(CParser.Comma);
                    this.state = 1070;
                    this.logicalOrExpression(0);
                    this.state = 1075;
                    this._errHandler.sync(this);
                    _la = this._input.LA(1);
                }
            }

            this.state = 1091;
            this._errHandler.sync(this);
            _la = this._input.LA(1);
            while(_la===CParser.Colon) {
                this.state = 1078;
                this.match(CParser.Colon);
                this.state = 1087;
                _la = this._input.LA(1);
                if((((_la) & ~0x1f) == 0 && ((1 << _la) & ((1 << CParser.T__0) | (1 << CParser.T__1) | (1 << CParser.T__2))) !== 0) || ((((_la - 39)) & ~0x1f) == 0 && ((1 << (_la - 39)) & ((1 << (CParser.Sizeof - 39)) | (1 << (CParser.Alignof - 39)) | (1 << (CParser.Generic - 39)) | (1 << (CParser.LeftParen - 39)))) !== 0) || ((((_la - 71)) & ~0x1f) == 0 && ((1 << (_la - 71)) & ((1 << (CParser.Plus - 71)) | (1 << (CParser.PlusPlus - 71)) | (1 << (CParser.Minus - 71)) | (1 << (CParser.MinusMinus - 71)) | (1 << (CParser.Star - 71)) | (1 << (CParser.And - 71)) | (1 << (CParser.AndAnd - 71)) | (1 << (CParser.Not - 71)) | (1 << (CParser.Tilde - 71)))) !== 0) || ((((_la - 105)) & ~0x1f) == 0 && ((1 << (_la - 105)) & ((1 << (CParser.Identifier - 105)) | (1 << (CParser.Constant - 105)) | (1 << (CParser.StringLiteral - 105)))) !== 0)) {
                    this.state = 1079;
                    this.logicalOrExpression(0);
                    this.state = 1084;
                    this._errHandler.sync(this);
                    _la = this._input.LA(1);
                    while(_la===CParser.Comma) {
                        this.state = 1080;
                        this.match(CParser.Comma);
                        this.state = 1081;
                        this.logicalOrExpression(0);
                        this.state = 1086;
                        this._errHandler.sync(this);
                        _la = this._input.LA(1);
                    }
                }

                this.state = 1093;
                this._errHandler.sync(this);
                _la = this._input.LA(1);
            }
            this.state = 1094;
            this.match(CParser.RightParen);
            this.state = 1095;
            this.match(CParser.Semi);
            break;

        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function LabeledStatementContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_labeledStatement;
    return this;
}

LabeledStatementContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
LabeledStatementContext.prototype.constructor = LabeledStatementContext;

LabeledStatementContext.prototype.Identifier = function() {
    return this.getToken(CParser.Identifier, 0);
};

LabeledStatementContext.prototype.statement = function() {
    return this.getTypedRuleContext(StatementContext,0);
};

LabeledStatementContext.prototype.constantExpression = function() {
    return this.getTypedRuleContext(ConstantExpressionContext,0);
};

LabeledStatementContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterLabeledStatement(this);
	}
};

LabeledStatementContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitLabeledStatement(this);
	}
};




CParser.LabeledStatementContext = LabeledStatementContext;

CParser.prototype.labeledStatement = function() {

    var localctx = new LabeledStatementContext(this, this._ctx, this.state);
    this.enterRule(localctx, 142, CParser.RULE_labeledStatement);
    try {
        this.state = 1109;
        switch(this._input.LA(1)) {
        case CParser.Identifier:
            this.enterOuterAlt(localctx, 1);
            this.state = 1098;
            this.match(CParser.Identifier);
            this.state = 1099;
            this.match(CParser.Colon);
            this.state = 1100;
            this.statement();
            break;
        case CParser.Case:
            this.enterOuterAlt(localctx, 2);
            this.state = 1101;
            this.match(CParser.Case);
            this.state = 1102;
            this.constantExpression();
            this.state = 1103;
            this.match(CParser.Colon);
            this.state = 1104;
            this.statement();
            break;
        case CParser.Default:
            this.enterOuterAlt(localctx, 3);
            this.state = 1106;
            this.match(CParser.Default);
            this.state = 1107;
            this.match(CParser.Colon);
            this.state = 1108;
            this.statement();
            break;
        default:
            throw new antlr4.error.NoViableAltException(this);
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function CompoundStatementContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_compoundStatement;
    return this;
}

CompoundStatementContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
CompoundStatementContext.prototype.constructor = CompoundStatementContext;

CompoundStatementContext.prototype.blockItemList = function() {
    return this.getTypedRuleContext(BlockItemListContext,0);
};

CompoundStatementContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterCompoundStatement(this);
	}
};

CompoundStatementContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitCompoundStatement(this);
	}
};




CParser.CompoundStatementContext = CompoundStatementContext;

CParser.prototype.compoundStatement = function() {

    var localctx = new CompoundStatementContext(this, this._ctx, this.state);
    this.enterRule(localctx, 144, CParser.RULE_compoundStatement);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 1111;
        this.match(CParser.LeftBrace);
        this.state = 1113;
        _la = this._input.LA(1);
        if((((_la) & ~0x1f) == 0 && ((1 << _la) & ((1 << CParser.T__0) | (1 << CParser.T__1) | (1 << CParser.T__2) | (1 << CParser.T__3) | (1 << CParser.T__4) | (1 << CParser.T__5) | (1 << CParser.T__6) | (1 << CParser.T__7) | (1 << CParser.T__8) | (1 << CParser.T__9) | (1 << CParser.T__10) | (1 << CParser.T__11) | (1 << CParser.T__12) | (1 << CParser.Auto) | (1 << CParser.Break) | (1 << CParser.Case) | (1 << CParser.Char) | (1 << CParser.Const) | (1 << CParser.Continue) | (1 << CParser.Default) | (1 << CParser.Do) | (1 << CParser.Double) | (1 << CParser.Enum) | (1 << CParser.Extern) | (1 << CParser.Float) | (1 << CParser.For) | (1 << CParser.Goto) | (1 << CParser.If) | (1 << CParser.Inline))) !== 0) || ((((_la - 32)) & ~0x1f) == 0 && ((1 << (_la - 32)) & ((1 << (CParser.Int - 32)) | (1 << (CParser.Long - 32)) | (1 << (CParser.Register - 32)) | (1 << (CParser.Restrict - 32)) | (1 << (CParser.Return - 32)) | (1 << (CParser.Short - 32)) | (1 << (CParser.Signed - 32)) | (1 << (CParser.Sizeof - 32)) | (1 << (CParser.Static - 32)) | (1 << (CParser.Struct - 32)) | (1 << (CParser.Switch - 32)) | (1 << (CParser.Typedef - 32)) | (1 << (CParser.Union - 32)) | (1 << (CParser.Unsigned - 32)) | (1 << (CParser.Void - 32)) | (1 << (CParser.Volatile - 32)) | (1 << (CParser.While - 32)) | (1 << (CParser.Alignas - 32)) | (1 << (CParser.Alignof - 32)) | (1 << (CParser.Atomic - 32)) | (1 << (CParser.Bool - 32)) | (1 << (CParser.Complex - 32)) | (1 << (CParser.Generic - 32)) | (1 << (CParser.Noreturn - 32)) | (1 << (CParser.StaticAssert - 32)) | (1 << (CParser.ThreadLocal - 32)) | (1 << (CParser.LeftParen - 32)) | (1 << (CParser.LeftBrace - 32)))) !== 0) || ((((_la - 71)) & ~0x1f) == 0 && ((1 << (_la - 71)) & ((1 << (CParser.Plus - 71)) | (1 << (CParser.PlusPlus - 71)) | (1 << (CParser.Minus - 71)) | (1 << (CParser.MinusMinus - 71)) | (1 << (CParser.Star - 71)) | (1 << (CParser.And - 71)) | (1 << (CParser.AndAnd - 71)) | (1 << (CParser.Not - 71)) | (1 << (CParser.Tilde - 71)) | (1 << (CParser.Semi - 71)))) !== 0) || ((((_la - 105)) & ~0x1f) == 0 && ((1 << (_la - 105)) & ((1 << (CParser.Identifier - 105)) | (1 << (CParser.Constant - 105)) | (1 << (CParser.StringLiteral - 105)))) !== 0)) {
            this.state = 1112;
            this.blockItemList(0);
        }

        this.state = 1115;
        this.match(CParser.RightBrace);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function BlockItemListContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_blockItemList;
    return this;
}

BlockItemListContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
BlockItemListContext.prototype.constructor = BlockItemListContext;

BlockItemListContext.prototype.blockItem = function() {
    return this.getTypedRuleContext(BlockItemContext,0);
};

BlockItemListContext.prototype.blockItemList = function() {
    return this.getTypedRuleContext(BlockItemListContext,0);
};

BlockItemListContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterBlockItemList(this);
	}
};

BlockItemListContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitBlockItemList(this);
	}
};



CParser.prototype.blockItemList = function(_p) {
	if(_p===undefined) {
	    _p = 0;
	}
    var _parentctx = this._ctx;
    var _parentState = this.state;
    var localctx = new BlockItemListContext(this, this._ctx, _parentState);
    var _prevctx = localctx;
    var _startState = 146;
    this.enterRecursionRule(localctx, 146, CParser.RULE_blockItemList, _p);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 1118;
        this.blockItem();
        this._ctx.stop = this._input.LT(-1);
        this.state = 1124;
        this._errHandler.sync(this);
        var _alt = this._interp.adaptivePredict(this._input,119,this._ctx)
        while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
            if(_alt===1) {
                if(this._parseListeners!==null) {
                    this.triggerExitRuleEvent();
                }
                _prevctx = localctx;
                localctx = new BlockItemListContext(this, _parentctx, _parentState);
                this.pushNewRecursionContext(localctx, _startState, CParser.RULE_blockItemList);
                this.state = 1120;
                if (!( this.precpred(this._ctx, 1))) {
                    throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 1)");
                }
                this.state = 1121;
                this.blockItem(); 
            }
            this.state = 1126;
            this._errHandler.sync(this);
            _alt = this._interp.adaptivePredict(this._input,119,this._ctx);
        }

    } catch( error) {
        if(error instanceof antlr4.error.RecognitionException) {
	        localctx.exception = error;
	        this._errHandler.reportError(this, error);
	        this._errHandler.recover(this, error);
	    } else {
	    	throw error;
	    }
    } finally {
        this.unrollRecursionContexts(_parentctx)
    }
    return localctx;
};

function BlockItemContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_blockItem;
    return this;
}

BlockItemContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
BlockItemContext.prototype.constructor = BlockItemContext;

BlockItemContext.prototype.declaration = function() {
    return this.getTypedRuleContext(DeclarationContext,0);
};

BlockItemContext.prototype.statement = function() {
    return this.getTypedRuleContext(StatementContext,0);
};

BlockItemContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterBlockItem(this);
	}
};

BlockItemContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitBlockItem(this);
	}
};




CParser.BlockItemContext = BlockItemContext;

CParser.prototype.blockItem = function() {

    var localctx = new BlockItemContext(this, this._ctx, this.state);
    this.enterRule(localctx, 148, CParser.RULE_blockItem);
    try {
        this.state = 1129;
        var la_ = this._interp.adaptivePredict(this._input,120,this._ctx);
        switch(la_) {
        case 1:
            this.enterOuterAlt(localctx, 1);
            this.state = 1127;
            this.declaration();
            break;

        case 2:
            this.enterOuterAlt(localctx, 2);
            this.state = 1128;
            this.statement();
            break;

        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function ExpressionStatementContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_expressionStatement;
    return this;
}

ExpressionStatementContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
ExpressionStatementContext.prototype.constructor = ExpressionStatementContext;

ExpressionStatementContext.prototype.expression = function() {
    return this.getTypedRuleContext(ExpressionContext,0);
};

ExpressionStatementContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterExpressionStatement(this);
	}
};

ExpressionStatementContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitExpressionStatement(this);
	}
};




CParser.ExpressionStatementContext = ExpressionStatementContext;

CParser.prototype.expressionStatement = function() {

    var localctx = new ExpressionStatementContext(this, this._ctx, this.state);
    this.enterRule(localctx, 150, CParser.RULE_expressionStatement);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 1132;
        _la = this._input.LA(1);
        if((((_la) & ~0x1f) == 0 && ((1 << _la) & ((1 << CParser.T__0) | (1 << CParser.T__1) | (1 << CParser.T__2))) !== 0) || ((((_la - 39)) & ~0x1f) == 0 && ((1 << (_la - 39)) & ((1 << (CParser.Sizeof - 39)) | (1 << (CParser.Alignof - 39)) | (1 << (CParser.Generic - 39)) | (1 << (CParser.LeftParen - 39)))) !== 0) || ((((_la - 71)) & ~0x1f) == 0 && ((1 << (_la - 71)) & ((1 << (CParser.Plus - 71)) | (1 << (CParser.PlusPlus - 71)) | (1 << (CParser.Minus - 71)) | (1 << (CParser.MinusMinus - 71)) | (1 << (CParser.Star - 71)) | (1 << (CParser.And - 71)) | (1 << (CParser.AndAnd - 71)) | (1 << (CParser.Not - 71)) | (1 << (CParser.Tilde - 71)))) !== 0) || ((((_la - 105)) & ~0x1f) == 0 && ((1 << (_la - 105)) & ((1 << (CParser.Identifier - 105)) | (1 << (CParser.Constant - 105)) | (1 << (CParser.StringLiteral - 105)))) !== 0)) {
            this.state = 1131;
            this.expression(0);
        }

        this.state = 1134;
        this.match(CParser.Semi);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function SelectionStatementContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_selectionStatement;
    return this;
}

SelectionStatementContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
SelectionStatementContext.prototype.constructor = SelectionStatementContext;

SelectionStatementContext.prototype.expression = function() {
    return this.getTypedRuleContext(ExpressionContext,0);
};

SelectionStatementContext.prototype.statement = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(StatementContext);
    } else {
        return this.getTypedRuleContext(StatementContext,i);
    }
};

SelectionStatementContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterSelectionStatement(this);
	}
};

SelectionStatementContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitSelectionStatement(this);
	}
};




CParser.SelectionStatementContext = SelectionStatementContext;

CParser.prototype.selectionStatement = function() {

    var localctx = new SelectionStatementContext(this, this._ctx, this.state);
    this.enterRule(localctx, 152, CParser.RULE_selectionStatement);
    try {
        this.state = 1151;
        switch(this._input.LA(1)) {
        case CParser.If:
            this.enterOuterAlt(localctx, 1);
            this.state = 1136;
            this.match(CParser.If);
            this.state = 1137;
            this.match(CParser.LeftParen);
            this.state = 1138;
            this.expression(0);
            this.state = 1139;
            this.match(CParser.RightParen);
            this.state = 1140;
            this.statement();
            this.state = 1143;
            var la_ = this._interp.adaptivePredict(this._input,122,this._ctx);
            if(la_===1) {
                this.state = 1141;
                this.match(CParser.Else);
                this.state = 1142;
                this.statement();

            }
            break;
        case CParser.Switch:
            this.enterOuterAlt(localctx, 2);
            this.state = 1145;
            this.match(CParser.Switch);
            this.state = 1146;
            this.match(CParser.LeftParen);
            this.state = 1147;
            this.expression(0);
            this.state = 1148;
            this.match(CParser.RightParen);
            this.state = 1149;
            this.statement();
            break;
        default:
            throw new antlr4.error.NoViableAltException(this);
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function IterationStatementContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_iterationStatement;
    return this;
}

IterationStatementContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
IterationStatementContext.prototype.constructor = IterationStatementContext;

IterationStatementContext.prototype.expression = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(ExpressionContext);
    } else {
        return this.getTypedRuleContext(ExpressionContext,i);
    }
};

IterationStatementContext.prototype.statement = function() {
    return this.getTypedRuleContext(StatementContext,0);
};

IterationStatementContext.prototype.declaration = function() {
    return this.getTypedRuleContext(DeclarationContext,0);
};

IterationStatementContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterIterationStatement(this);
	}
};

IterationStatementContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitIterationStatement(this);
	}
};




CParser.IterationStatementContext = IterationStatementContext;

CParser.prototype.iterationStatement = function() {

    var localctx = new IterationStatementContext(this, this._ctx, this.state);
    this.enterRule(localctx, 154, CParser.RULE_iterationStatement);
    var _la = 0; // Token type
    try {
        this.state = 1195;
        var la_ = this._interp.adaptivePredict(this._input,129,this._ctx);
        switch(la_) {
        case 1:
            this.enterOuterAlt(localctx, 1);
            this.state = 1153;
            this.match(CParser.While);
            this.state = 1154;
            this.match(CParser.LeftParen);
            this.state = 1155;
            this.expression(0);
            this.state = 1156;
            this.match(CParser.RightParen);
            this.state = 1157;
            this.statement();
            break;

        case 2:
            this.enterOuterAlt(localctx, 2);
            this.state = 1159;
            this.match(CParser.Do);
            this.state = 1160;
            this.statement();
            this.state = 1161;
            this.match(CParser.While);
            this.state = 1162;
            this.match(CParser.LeftParen);
            this.state = 1163;
            this.expression(0);
            this.state = 1164;
            this.match(CParser.RightParen);
            this.state = 1165;
            this.match(CParser.Semi);
            break;

        case 3:
            this.enterOuterAlt(localctx, 3);
            this.state = 1167;
            this.match(CParser.For);
            this.state = 1168;
            this.match(CParser.LeftParen);
            this.state = 1170;
            _la = this._input.LA(1);
            if((((_la) & ~0x1f) == 0 && ((1 << _la) & ((1 << CParser.T__0) | (1 << CParser.T__1) | (1 << CParser.T__2))) !== 0) || ((((_la - 39)) & ~0x1f) == 0 && ((1 << (_la - 39)) & ((1 << (CParser.Sizeof - 39)) | (1 << (CParser.Alignof - 39)) | (1 << (CParser.Generic - 39)) | (1 << (CParser.LeftParen - 39)))) !== 0) || ((((_la - 71)) & ~0x1f) == 0 && ((1 << (_la - 71)) & ((1 << (CParser.Plus - 71)) | (1 << (CParser.PlusPlus - 71)) | (1 << (CParser.Minus - 71)) | (1 << (CParser.MinusMinus - 71)) | (1 << (CParser.Star - 71)) | (1 << (CParser.And - 71)) | (1 << (CParser.AndAnd - 71)) | (1 << (CParser.Not - 71)) | (1 << (CParser.Tilde - 71)))) !== 0) || ((((_la - 105)) & ~0x1f) == 0 && ((1 << (_la - 105)) & ((1 << (CParser.Identifier - 105)) | (1 << (CParser.Constant - 105)) | (1 << (CParser.StringLiteral - 105)))) !== 0)) {
                this.state = 1169;
                this.expression(0);
            }

            this.state = 1172;
            this.match(CParser.Semi);
            this.state = 1174;
            _la = this._input.LA(1);
            if((((_la) & ~0x1f) == 0 && ((1 << _la) & ((1 << CParser.T__0) | (1 << CParser.T__1) | (1 << CParser.T__2))) !== 0) || ((((_la - 39)) & ~0x1f) == 0 && ((1 << (_la - 39)) & ((1 << (CParser.Sizeof - 39)) | (1 << (CParser.Alignof - 39)) | (1 << (CParser.Generic - 39)) | (1 << (CParser.LeftParen - 39)))) !== 0) || ((((_la - 71)) & ~0x1f) == 0 && ((1 << (_la - 71)) & ((1 << (CParser.Plus - 71)) | (1 << (CParser.PlusPlus - 71)) | (1 << (CParser.Minus - 71)) | (1 << (CParser.MinusMinus - 71)) | (1 << (CParser.Star - 71)) | (1 << (CParser.And - 71)) | (1 << (CParser.AndAnd - 71)) | (1 << (CParser.Not - 71)) | (1 << (CParser.Tilde - 71)))) !== 0) || ((((_la - 105)) & ~0x1f) == 0 && ((1 << (_la - 105)) & ((1 << (CParser.Identifier - 105)) | (1 << (CParser.Constant - 105)) | (1 << (CParser.StringLiteral - 105)))) !== 0)) {
                this.state = 1173;
                this.expression(0);
            }

            this.state = 1176;
            this.match(CParser.Semi);
            this.state = 1178;
            _la = this._input.LA(1);
            if((((_la) & ~0x1f) == 0 && ((1 << _la) & ((1 << CParser.T__0) | (1 << CParser.T__1) | (1 << CParser.T__2))) !== 0) || ((((_la - 39)) & ~0x1f) == 0 && ((1 << (_la - 39)) & ((1 << (CParser.Sizeof - 39)) | (1 << (CParser.Alignof - 39)) | (1 << (CParser.Generic - 39)) | (1 << (CParser.LeftParen - 39)))) !== 0) || ((((_la - 71)) & ~0x1f) == 0 && ((1 << (_la - 71)) & ((1 << (CParser.Plus - 71)) | (1 << (CParser.PlusPlus - 71)) | (1 << (CParser.Minus - 71)) | (1 << (CParser.MinusMinus - 71)) | (1 << (CParser.Star - 71)) | (1 << (CParser.And - 71)) | (1 << (CParser.AndAnd - 71)) | (1 << (CParser.Not - 71)) | (1 << (CParser.Tilde - 71)))) !== 0) || ((((_la - 105)) & ~0x1f) == 0 && ((1 << (_la - 105)) & ((1 << (CParser.Identifier - 105)) | (1 << (CParser.Constant - 105)) | (1 << (CParser.StringLiteral - 105)))) !== 0)) {
                this.state = 1177;
                this.expression(0);
            }

            this.state = 1180;
            this.match(CParser.RightParen);
            this.state = 1181;
            this.statement();
            break;

        case 4:
            this.enterOuterAlt(localctx, 4);
            this.state = 1182;
            this.match(CParser.For);
            this.state = 1183;
            this.match(CParser.LeftParen);
            this.state = 1184;
            this.declaration();
            this.state = 1186;
            _la = this._input.LA(1);
            if((((_la) & ~0x1f) == 0 && ((1 << _la) & ((1 << CParser.T__0) | (1 << CParser.T__1) | (1 << CParser.T__2))) !== 0) || ((((_la - 39)) & ~0x1f) == 0 && ((1 << (_la - 39)) & ((1 << (CParser.Sizeof - 39)) | (1 << (CParser.Alignof - 39)) | (1 << (CParser.Generic - 39)) | (1 << (CParser.LeftParen - 39)))) !== 0) || ((((_la - 71)) & ~0x1f) == 0 && ((1 << (_la - 71)) & ((1 << (CParser.Plus - 71)) | (1 << (CParser.PlusPlus - 71)) | (1 << (CParser.Minus - 71)) | (1 << (CParser.MinusMinus - 71)) | (1 << (CParser.Star - 71)) | (1 << (CParser.And - 71)) | (1 << (CParser.AndAnd - 71)) | (1 << (CParser.Not - 71)) | (1 << (CParser.Tilde - 71)))) !== 0) || ((((_la - 105)) & ~0x1f) == 0 && ((1 << (_la - 105)) & ((1 << (CParser.Identifier - 105)) | (1 << (CParser.Constant - 105)) | (1 << (CParser.StringLiteral - 105)))) !== 0)) {
                this.state = 1185;
                this.expression(0);
            }

            this.state = 1188;
            this.match(CParser.Semi);
            this.state = 1190;
            _la = this._input.LA(1);
            if((((_la) & ~0x1f) == 0 && ((1 << _la) & ((1 << CParser.T__0) | (1 << CParser.T__1) | (1 << CParser.T__2))) !== 0) || ((((_la - 39)) & ~0x1f) == 0 && ((1 << (_la - 39)) & ((1 << (CParser.Sizeof - 39)) | (1 << (CParser.Alignof - 39)) | (1 << (CParser.Generic - 39)) | (1 << (CParser.LeftParen - 39)))) !== 0) || ((((_la - 71)) & ~0x1f) == 0 && ((1 << (_la - 71)) & ((1 << (CParser.Plus - 71)) | (1 << (CParser.PlusPlus - 71)) | (1 << (CParser.Minus - 71)) | (1 << (CParser.MinusMinus - 71)) | (1 << (CParser.Star - 71)) | (1 << (CParser.And - 71)) | (1 << (CParser.AndAnd - 71)) | (1 << (CParser.Not - 71)) | (1 << (CParser.Tilde - 71)))) !== 0) || ((((_la - 105)) & ~0x1f) == 0 && ((1 << (_la - 105)) & ((1 << (CParser.Identifier - 105)) | (1 << (CParser.Constant - 105)) | (1 << (CParser.StringLiteral - 105)))) !== 0)) {
                this.state = 1189;
                this.expression(0);
            }

            this.state = 1192;
            this.match(CParser.RightParen);
            this.state = 1193;
            this.statement();
            break;

        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function JumpStatementContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_jumpStatement;
    return this;
}

JumpStatementContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
JumpStatementContext.prototype.constructor = JumpStatementContext;

JumpStatementContext.prototype.Identifier = function() {
    return this.getToken(CParser.Identifier, 0);
};

JumpStatementContext.prototype.expression = function() {
    return this.getTypedRuleContext(ExpressionContext,0);
};

JumpStatementContext.prototype.unaryExpression = function() {
    return this.getTypedRuleContext(UnaryExpressionContext,0);
};

JumpStatementContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterJumpStatement(this);
	}
};

JumpStatementContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitJumpStatement(this);
	}
};




CParser.JumpStatementContext = JumpStatementContext;

CParser.prototype.jumpStatement = function() {

    var localctx = new JumpStatementContext(this, this._ctx, this.state);
    this.enterRule(localctx, 156, CParser.RULE_jumpStatement);
    var _la = 0; // Token type
    try {
        this.state = 1213;
        var la_ = this._interp.adaptivePredict(this._input,131,this._ctx);
        switch(la_) {
        case 1:
            this.enterOuterAlt(localctx, 1);
            this.state = 1197;
            this.match(CParser.Goto);
            this.state = 1198;
            this.match(CParser.Identifier);
            this.state = 1199;
            this.match(CParser.Semi);
            break;

        case 2:
            this.enterOuterAlt(localctx, 2);
            this.state = 1200;
            this.match(CParser.Continue);
            this.state = 1201;
            this.match(CParser.Semi);
            break;

        case 3:
            this.enterOuterAlt(localctx, 3);
            this.state = 1202;
            this.match(CParser.Break);
            this.state = 1203;
            this.match(CParser.Semi);
            break;

        case 4:
            this.enterOuterAlt(localctx, 4);
            this.state = 1204;
            this.match(CParser.Return);
            this.state = 1206;
            _la = this._input.LA(1);
            if((((_la) & ~0x1f) == 0 && ((1 << _la) & ((1 << CParser.T__0) | (1 << CParser.T__1) | (1 << CParser.T__2))) !== 0) || ((((_la - 39)) & ~0x1f) == 0 && ((1 << (_la - 39)) & ((1 << (CParser.Sizeof - 39)) | (1 << (CParser.Alignof - 39)) | (1 << (CParser.Generic - 39)) | (1 << (CParser.LeftParen - 39)))) !== 0) || ((((_la - 71)) & ~0x1f) == 0 && ((1 << (_la - 71)) & ((1 << (CParser.Plus - 71)) | (1 << (CParser.PlusPlus - 71)) | (1 << (CParser.Minus - 71)) | (1 << (CParser.MinusMinus - 71)) | (1 << (CParser.Star - 71)) | (1 << (CParser.And - 71)) | (1 << (CParser.AndAnd - 71)) | (1 << (CParser.Not - 71)) | (1 << (CParser.Tilde - 71)))) !== 0) || ((((_la - 105)) & ~0x1f) == 0 && ((1 << (_la - 105)) & ((1 << (CParser.Identifier - 105)) | (1 << (CParser.Constant - 105)) | (1 << (CParser.StringLiteral - 105)))) !== 0)) {
                this.state = 1205;
                this.expression(0);
            }

            this.state = 1208;
            this.match(CParser.Semi);
            break;

        case 5:
            this.enterOuterAlt(localctx, 5);
            this.state = 1209;
            this.match(CParser.Goto);
            this.state = 1210;
            this.unaryExpression();
            this.state = 1211;
            this.match(CParser.Semi);
            break;

        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function CompilationUnitContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_compilationUnit;
    return this;
}

CompilationUnitContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
CompilationUnitContext.prototype.constructor = CompilationUnitContext;

CompilationUnitContext.prototype.EOF = function() {
    return this.getToken(CParser.EOF, 0);
};

CompilationUnitContext.prototype.translationUnit = function() {
    return this.getTypedRuleContext(TranslationUnitContext,0);
};

CompilationUnitContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterCompilationUnit(this);
	}
};

CompilationUnitContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitCompilationUnit(this);
	}
};




CParser.CompilationUnitContext = CompilationUnitContext;

CParser.prototype.compilationUnit = function() {

    var localctx = new CompilationUnitContext(this, this._ctx, this.state);
    this.enterRule(localctx, 158, CParser.RULE_compilationUnit);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 1216;
        _la = this._input.LA(1);
        if((((_la) & ~0x1f) == 0 && ((1 << _la) & ((1 << CParser.T__0) | (1 << CParser.T__3) | (1 << CParser.T__4) | (1 << CParser.T__5) | (1 << CParser.T__6) | (1 << CParser.T__7) | (1 << CParser.T__8) | (1 << CParser.T__9) | (1 << CParser.T__11) | (1 << CParser.Auto) | (1 << CParser.Char) | (1 << CParser.Const) | (1 << CParser.Double) | (1 << CParser.Enum) | (1 << CParser.Extern) | (1 << CParser.Float) | (1 << CParser.Inline))) !== 0) || ((((_la - 32)) & ~0x1f) == 0 && ((1 << (_la - 32)) & ((1 << (CParser.Int - 32)) | (1 << (CParser.Long - 32)) | (1 << (CParser.Register - 32)) | (1 << (CParser.Restrict - 32)) | (1 << (CParser.Short - 32)) | (1 << (CParser.Signed - 32)) | (1 << (CParser.Static - 32)) | (1 << (CParser.Struct - 32)) | (1 << (CParser.Typedef - 32)) | (1 << (CParser.Union - 32)) | (1 << (CParser.Unsigned - 32)) | (1 << (CParser.Void - 32)) | (1 << (CParser.Volatile - 32)) | (1 << (CParser.Alignas - 32)) | (1 << (CParser.Atomic - 32)) | (1 << (CParser.Bool - 32)) | (1 << (CParser.Complex - 32)) | (1 << (CParser.Noreturn - 32)) | (1 << (CParser.StaticAssert - 32)) | (1 << (CParser.ThreadLocal - 32)) | (1 << (CParser.LeftParen - 32)))) !== 0) || ((((_la - 75)) & ~0x1f) == 0 && ((1 << (_la - 75)) & ((1 << (CParser.Star - 75)) | (1 << (CParser.Caret - 75)) | (1 << (CParser.Semi - 75)) | (1 << (CParser.Identifier - 75)))) !== 0)) {
            this.state = 1215;
            this.translationUnit(0);
        }

        this.state = 1218;
        this.match(CParser.EOF);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function TranslationUnitContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_translationUnit;
    return this;
}

TranslationUnitContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
TranslationUnitContext.prototype.constructor = TranslationUnitContext;

TranslationUnitContext.prototype.externalDeclaration = function() {
    return this.getTypedRuleContext(ExternalDeclarationContext,0);
};

TranslationUnitContext.prototype.translationUnit = function() {
    return this.getTypedRuleContext(TranslationUnitContext,0);
};

TranslationUnitContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterTranslationUnit(this);
	}
};

TranslationUnitContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitTranslationUnit(this);
	}
};



CParser.prototype.translationUnit = function(_p) {
	if(_p===undefined) {
	    _p = 0;
	}
    var _parentctx = this._ctx;
    var _parentState = this.state;
    var localctx = new TranslationUnitContext(this, this._ctx, _parentState);
    var _prevctx = localctx;
    var _startState = 160;
    this.enterRecursionRule(localctx, 160, CParser.RULE_translationUnit, _p);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 1221;
        this.externalDeclaration();
        this._ctx.stop = this._input.LT(-1);
        this.state = 1227;
        this._errHandler.sync(this);
        var _alt = this._interp.adaptivePredict(this._input,133,this._ctx)
        while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
            if(_alt===1) {
                if(this._parseListeners!==null) {
                    this.triggerExitRuleEvent();
                }
                _prevctx = localctx;
                localctx = new TranslationUnitContext(this, _parentctx, _parentState);
                this.pushNewRecursionContext(localctx, _startState, CParser.RULE_translationUnit);
                this.state = 1223;
                if (!( this.precpred(this._ctx, 1))) {
                    throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 1)");
                }
                this.state = 1224;
                this.externalDeclaration(); 
            }
            this.state = 1229;
            this._errHandler.sync(this);
            _alt = this._interp.adaptivePredict(this._input,133,this._ctx);
        }

    } catch( error) {
        if(error instanceof antlr4.error.RecognitionException) {
	        localctx.exception = error;
	        this._errHandler.reportError(this, error);
	        this._errHandler.recover(this, error);
	    } else {
	    	throw error;
	    }
    } finally {
        this.unrollRecursionContexts(_parentctx)
    }
    return localctx;
};

function ExternalDeclarationContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_externalDeclaration;
    return this;
}

ExternalDeclarationContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
ExternalDeclarationContext.prototype.constructor = ExternalDeclarationContext;

ExternalDeclarationContext.prototype.functionDefinition = function() {
    return this.getTypedRuleContext(FunctionDefinitionContext,0);
};

ExternalDeclarationContext.prototype.declaration = function() {
    return this.getTypedRuleContext(DeclarationContext,0);
};

ExternalDeclarationContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterExternalDeclaration(this);
	}
};

ExternalDeclarationContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitExternalDeclaration(this);
	}
};




CParser.ExternalDeclarationContext = ExternalDeclarationContext;

CParser.prototype.externalDeclaration = function() {

    var localctx = new ExternalDeclarationContext(this, this._ctx, this.state);
    this.enterRule(localctx, 162, CParser.RULE_externalDeclaration);
    try {
        this.state = 1233;
        var la_ = this._interp.adaptivePredict(this._input,134,this._ctx);
        switch(la_) {
        case 1:
            this.enterOuterAlt(localctx, 1);
            this.state = 1230;
            this.functionDefinition();
            break;

        case 2:
            this.enterOuterAlt(localctx, 2);
            this.state = 1231;
            this.declaration();
            break;

        case 3:
            this.enterOuterAlt(localctx, 3);
            this.state = 1232;
            this.match(CParser.Semi);
            break;

        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function FunctionDefinitionContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_functionDefinition;
    return this;
}

FunctionDefinitionContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
FunctionDefinitionContext.prototype.constructor = FunctionDefinitionContext;

FunctionDefinitionContext.prototype.declarator = function() {
    return this.getTypedRuleContext(DeclaratorContext,0);
};

FunctionDefinitionContext.prototype.compoundStatement = function() {
    return this.getTypedRuleContext(CompoundStatementContext,0);
};

FunctionDefinitionContext.prototype.declarationSpecifiers = function() {
    return this.getTypedRuleContext(DeclarationSpecifiersContext,0);
};

FunctionDefinitionContext.prototype.declarationList = function() {
    return this.getTypedRuleContext(DeclarationListContext,0);
};

FunctionDefinitionContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterFunctionDefinition(this);
	}
};

FunctionDefinitionContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitFunctionDefinition(this);
	}
};




CParser.FunctionDefinitionContext = FunctionDefinitionContext;

CParser.prototype.functionDefinition = function() {

    var localctx = new FunctionDefinitionContext(this, this._ctx, this.state);
    this.enterRule(localctx, 164, CParser.RULE_functionDefinition);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 1236;
        var la_ = this._interp.adaptivePredict(this._input,135,this._ctx);
        if(la_===1) {
            this.state = 1235;
            this.declarationSpecifiers();

        }
        this.state = 1238;
        this.declarator();
        this.state = 1240;
        _la = this._input.LA(1);
        if((((_la) & ~0x1f) == 0 && ((1 << _la) & ((1 << CParser.T__0) | (1 << CParser.T__3) | (1 << CParser.T__4) | (1 << CParser.T__5) | (1 << CParser.T__6) | (1 << CParser.T__7) | (1 << CParser.T__8) | (1 << CParser.T__9) | (1 << CParser.T__11) | (1 << CParser.Auto) | (1 << CParser.Char) | (1 << CParser.Const) | (1 << CParser.Double) | (1 << CParser.Enum) | (1 << CParser.Extern) | (1 << CParser.Float) | (1 << CParser.Inline))) !== 0) || ((((_la - 32)) & ~0x1f) == 0 && ((1 << (_la - 32)) & ((1 << (CParser.Int - 32)) | (1 << (CParser.Long - 32)) | (1 << (CParser.Register - 32)) | (1 << (CParser.Restrict - 32)) | (1 << (CParser.Short - 32)) | (1 << (CParser.Signed - 32)) | (1 << (CParser.Static - 32)) | (1 << (CParser.Struct - 32)) | (1 << (CParser.Typedef - 32)) | (1 << (CParser.Union - 32)) | (1 << (CParser.Unsigned - 32)) | (1 << (CParser.Void - 32)) | (1 << (CParser.Volatile - 32)) | (1 << (CParser.Alignas - 32)) | (1 << (CParser.Atomic - 32)) | (1 << (CParser.Bool - 32)) | (1 << (CParser.Complex - 32)) | (1 << (CParser.Noreturn - 32)) | (1 << (CParser.StaticAssert - 32)) | (1 << (CParser.ThreadLocal - 32)))) !== 0) || _la===CParser.Identifier) {
            this.state = 1239;
            this.declarationList(0);
        }

        this.state = 1242;
        this.compoundStatement();
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function DeclarationListContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = CParser.RULE_declarationList;
    return this;
}

DeclarationListContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
DeclarationListContext.prototype.constructor = DeclarationListContext;

DeclarationListContext.prototype.declaration = function() {
    return this.getTypedRuleContext(DeclarationContext,0);
};

DeclarationListContext.prototype.declarationList = function() {
    return this.getTypedRuleContext(DeclarationListContext,0);
};

DeclarationListContext.prototype.enterRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.enterDeclarationList(this);
	}
};

DeclarationListContext.prototype.exitRule = function(listener) {
    if(listener instanceof CListener ) {
        listener.exitDeclarationList(this);
	}
};



CParser.prototype.declarationList = function(_p) {
	if(_p===undefined) {
	    _p = 0;
	}
    var _parentctx = this._ctx;
    var _parentState = this.state;
    var localctx = new DeclarationListContext(this, this._ctx, _parentState);
    var _prevctx = localctx;
    var _startState = 166;
    this.enterRecursionRule(localctx, 166, CParser.RULE_declarationList, _p);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 1245;
        this.declaration();
        this._ctx.stop = this._input.LT(-1);
        this.state = 1251;
        this._errHandler.sync(this);
        var _alt = this._interp.adaptivePredict(this._input,137,this._ctx)
        while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
            if(_alt===1) {
                if(this._parseListeners!==null) {
                    this.triggerExitRuleEvent();
                }
                _prevctx = localctx;
                localctx = new DeclarationListContext(this, _parentctx, _parentState);
                this.pushNewRecursionContext(localctx, _startState, CParser.RULE_declarationList);
                this.state = 1247;
                if (!( this.precpred(this._ctx, 1))) {
                    throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 1)");
                }
                this.state = 1248;
                this.declaration(); 
            }
            this.state = 1253;
            this._errHandler.sync(this);
            _alt = this._interp.adaptivePredict(this._input,137,this._ctx);
        }

    } catch( error) {
        if(error instanceof antlr4.error.RecognitionException) {
	        localctx.exception = error;
	        this._errHandler.reportError(this, error);
	        this._errHandler.recover(this, error);
	    } else {
	    	throw error;
	    }
    } finally {
        this.unrollRecursionContexts(_parentctx)
    }
    return localctx;
};


CParser.prototype.sempred = function(localctx, ruleIndex, predIndex) {
	switch(ruleIndex) {
	case 2:
			return this.genericAssocList_sempred(localctx, predIndex);
	case 4:
			return this.postfixExpression_sempred(localctx, predIndex);
	case 5:
			return this.argumentExpressionList_sempred(localctx, predIndex);
	case 9:
			return this.multiplicativeExpression_sempred(localctx, predIndex);
	case 10:
			return this.additiveExpression_sempred(localctx, predIndex);
	case 11:
			return this.shiftExpression_sempred(localctx, predIndex);
	case 12:
			return this.relationalExpression_sempred(localctx, predIndex);
	case 13:
			return this.equalityExpression_sempred(localctx, predIndex);
	case 14:
			return this.andExpression_sempred(localctx, predIndex);
	case 15:
			return this.exclusiveOrExpression_sempred(localctx, predIndex);
	case 16:
			return this.inclusiveOrExpression_sempred(localctx, predIndex);
	case 17:
			return this.logicalAndExpression_sempred(localctx, predIndex);
	case 18:
			return this.logicalOrExpression_sempred(localctx, predIndex);
	case 22:
			return this.expression_sempred(localctx, predIndex);
	case 28:
			return this.initDeclaratorList_sempred(localctx, predIndex);
	case 34:
			return this.structDeclarationList_sempred(localctx, predIndex);
	case 37:
			return this.structDeclaratorList_sempred(localctx, predIndex);
	case 40:
			return this.enumeratorList_sempred(localctx, predIndex);
	case 48:
			return this.directDeclarator_sempred(localctx, predIndex);
	case 55:
			return this.typeQualifierList_sempred(localctx, predIndex);
	case 57:
			return this.parameterList_sempred(localctx, predIndex);
	case 59:
			return this.identifierList_sempred(localctx, predIndex);
	case 62:
			return this.directAbstractDeclarator_sempred(localctx, predIndex);
	case 65:
			return this.initializerList_sempred(localctx, predIndex);
	case 67:
			return this.designatorList_sempred(localctx, predIndex);
	case 73:
			return this.blockItemList_sempred(localctx, predIndex);
	case 80:
			return this.translationUnit_sempred(localctx, predIndex);
	case 83:
			return this.declarationList_sempred(localctx, predIndex);
    default:
        throw "No predicate with index:" + ruleIndex;
   }
};

CParser.prototype.genericAssocList_sempred = function(localctx, predIndex) {
	switch(predIndex) {
		case 0:
			return this.precpred(this._ctx, 1);
		default:
			throw "No predicate with index:" + predIndex;
	}
};

CParser.prototype.postfixExpression_sempred = function(localctx, predIndex) {
	switch(predIndex) {
		case 1:
			return this.precpred(this._ctx, 10);
		case 2:
			return this.precpred(this._ctx, 9);
		case 3:
			return this.precpred(this._ctx, 8);
		case 4:
			return this.precpred(this._ctx, 7);
		case 5:
			return this.precpred(this._ctx, 6);
		case 6:
			return this.precpred(this._ctx, 5);
		default:
			throw "No predicate with index:" + predIndex;
	}
};

CParser.prototype.argumentExpressionList_sempred = function(localctx, predIndex) {
	switch(predIndex) {
		case 7:
			return this.precpred(this._ctx, 1);
		default:
			throw "No predicate with index:" + predIndex;
	}
};

CParser.prototype.multiplicativeExpression_sempred = function(localctx, predIndex) {
	switch(predIndex) {
		case 8:
			return this.precpred(this._ctx, 3);
		case 9:
			return this.precpred(this._ctx, 2);
		case 10:
			return this.precpred(this._ctx, 1);
		default:
			throw "No predicate with index:" + predIndex;
	}
};

CParser.prototype.additiveExpression_sempred = function(localctx, predIndex) {
	switch(predIndex) {
		case 11:
			return this.precpred(this._ctx, 2);
		case 12:
			return this.precpred(this._ctx, 1);
		default:
			throw "No predicate with index:" + predIndex;
	}
};

CParser.prototype.shiftExpression_sempred = function(localctx, predIndex) {
	switch(predIndex) {
		case 13:
			return this.precpred(this._ctx, 2);
		case 14:
			return this.precpred(this._ctx, 1);
		default:
			throw "No predicate with index:" + predIndex;
	}
};

CParser.prototype.relationalExpression_sempred = function(localctx, predIndex) {
	switch(predIndex) {
		case 15:
			return this.precpred(this._ctx, 4);
		case 16:
			return this.precpred(this._ctx, 3);
		case 17:
			return this.precpred(this._ctx, 2);
		case 18:
			return this.precpred(this._ctx, 1);
		default:
			throw "No predicate with index:" + predIndex;
	}
};

CParser.prototype.equalityExpression_sempred = function(localctx, predIndex) {
	switch(predIndex) {
		case 19:
			return this.precpred(this._ctx, 2);
		case 20:
			return this.precpred(this._ctx, 1);
		default:
			throw "No predicate with index:" + predIndex;
	}
};

CParser.prototype.andExpression_sempred = function(localctx, predIndex) {
	switch(predIndex) {
		case 21:
			return this.precpred(this._ctx, 1);
		default:
			throw "No predicate with index:" + predIndex;
	}
};

CParser.prototype.exclusiveOrExpression_sempred = function(localctx, predIndex) {
	switch(predIndex) {
		case 22:
			return this.precpred(this._ctx, 1);
		default:
			throw "No predicate with index:" + predIndex;
	}
};

CParser.prototype.inclusiveOrExpression_sempred = function(localctx, predIndex) {
	switch(predIndex) {
		case 23:
			return this.precpred(this._ctx, 1);
		default:
			throw "No predicate with index:" + predIndex;
	}
};

CParser.prototype.logicalAndExpression_sempred = function(localctx, predIndex) {
	switch(predIndex) {
		case 24:
			return this.precpred(this._ctx, 1);
		default:
			throw "No predicate with index:" + predIndex;
	}
};

CParser.prototype.logicalOrExpression_sempred = function(localctx, predIndex) {
	switch(predIndex) {
		case 25:
			return this.precpred(this._ctx, 1);
		default:
			throw "No predicate with index:" + predIndex;
	}
};

CParser.prototype.expression_sempred = function(localctx, predIndex) {
	switch(predIndex) {
		case 26:
			return this.precpred(this._ctx, 1);
		default:
			throw "No predicate with index:" + predIndex;
	}
};

CParser.prototype.initDeclaratorList_sempred = function(localctx, predIndex) {
	switch(predIndex) {
		case 27:
			return this.precpred(this._ctx, 1);
		default:
			throw "No predicate with index:" + predIndex;
	}
};

CParser.prototype.structDeclarationList_sempred = function(localctx, predIndex) {
	switch(predIndex) {
		case 28:
			return this.precpred(this._ctx, 1);
		default:
			throw "No predicate with index:" + predIndex;
	}
};

CParser.prototype.structDeclaratorList_sempred = function(localctx, predIndex) {
	switch(predIndex) {
		case 29:
			return this.precpred(this._ctx, 1);
		default:
			throw "No predicate with index:" + predIndex;
	}
};

CParser.prototype.enumeratorList_sempred = function(localctx, predIndex) {
	switch(predIndex) {
		case 30:
			return this.precpred(this._ctx, 1);
		default:
			throw "No predicate with index:" + predIndex;
	}
};

CParser.prototype.directDeclarator_sempred = function(localctx, predIndex) {
	switch(predIndex) {
		case 31:
			return this.precpred(this._ctx, 6);
		case 32:
			return this.precpred(this._ctx, 5);
		case 33:
			return this.precpred(this._ctx, 4);
		case 34:
			return this.precpred(this._ctx, 3);
		case 35:
			return this.precpred(this._ctx, 2);
		case 36:
			return this.precpred(this._ctx, 1);
		default:
			throw "No predicate with index:" + predIndex;
	}
};

CParser.prototype.typeQualifierList_sempred = function(localctx, predIndex) {
	switch(predIndex) {
		case 37:
			return this.precpred(this._ctx, 1);
		default:
			throw "No predicate with index:" + predIndex;
	}
};

CParser.prototype.parameterList_sempred = function(localctx, predIndex) {
	switch(predIndex) {
		case 38:
			return this.precpred(this._ctx, 1);
		default:
			throw "No predicate with index:" + predIndex;
	}
};

CParser.prototype.identifierList_sempred = function(localctx, predIndex) {
	switch(predIndex) {
		case 39:
			return this.precpred(this._ctx, 1);
		default:
			throw "No predicate with index:" + predIndex;
	}
};

CParser.prototype.directAbstractDeclarator_sempred = function(localctx, predIndex) {
	switch(predIndex) {
		case 40:
			return this.precpred(this._ctx, 5);
		case 41:
			return this.precpred(this._ctx, 4);
		case 42:
			return this.precpred(this._ctx, 3);
		case 43:
			return this.precpred(this._ctx, 2);
		case 44:
			return this.precpred(this._ctx, 1);
		default:
			throw "No predicate with index:" + predIndex;
	}
};

CParser.prototype.initializerList_sempred = function(localctx, predIndex) {
	switch(predIndex) {
		case 45:
			return this.precpred(this._ctx, 1);
		default:
			throw "No predicate with index:" + predIndex;
	}
};

CParser.prototype.designatorList_sempred = function(localctx, predIndex) {
	switch(predIndex) {
		case 46:
			return this.precpred(this._ctx, 1);
		default:
			throw "No predicate with index:" + predIndex;
	}
};

CParser.prototype.blockItemList_sempred = function(localctx, predIndex) {
	switch(predIndex) {
		case 47:
			return this.precpred(this._ctx, 1);
		default:
			throw "No predicate with index:" + predIndex;
	}
};

CParser.prototype.translationUnit_sempred = function(localctx, predIndex) {
	switch(predIndex) {
		case 48:
			return this.precpred(this._ctx, 1);
		default:
			throw "No predicate with index:" + predIndex;
	}
};

CParser.prototype.declarationList_sempred = function(localctx, predIndex) {
	switch(predIndex) {
		case 49:
			return this.precpred(this._ctx, 1);
		default:
			throw "No predicate with index:" + predIndex;
	}
};


exports.CParser = CParser;
