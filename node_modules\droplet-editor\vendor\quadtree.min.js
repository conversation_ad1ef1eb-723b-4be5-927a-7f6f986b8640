var QUAD={};QUAD.init=function(args){var node;var TOP_LEFT=0;var TOP_RIGHT=1;var BOTTOM_LEFT=2;var BOTTOM_RIGHT=3;var PARENT=4;args.maxChildren=args.maxChildren||2;args.maxDepth=args.maxDepth||4;node=function(x,y,w,h,depth,maxChildren,maxDepth){var items=[],nodes=[];return{x:x,y:y,w:w,h:h,depth:depth,retrieve:function(item,callback,instance){for(var i=0;i<items.length;++i){instance?callback.call(instance,items[i]):callback(items[i])}if(nodes.length){this.findOverlappingNodes(item,function(dir){nodes[dir].retrieve(item,callback,instance)})}},insert:function(item){var i;if(nodes.length){i=this.findInsertNode(item);if(i===PARENT){items.push(item)}else{nodes[i].insert(item)}}else{items.push(item);if(items.length>maxChildren&&this.depth<maxDepth){this.divide()}}},findInsertNode:function(item){if(item.x+item.w<x+w/2){if(item.y+item.h<y+h/2){return TOP_LEFT}if(item.y>=y+h/2){return BOTTOM_LEFT}return PARENT}if(item.x>=x+w/2){if(item.y+item.h<y+h/2){return TOP_RIGHT}if(item.y>=y+h/2){return BOTTOM_RIGHT}return PARENT}return PARENT},findOverlappingNodes:function(item,callback){if(item.x<x+w/2){if(item.y<y+h/2){callback(TOP_LEFT)}if(item.y+item.h>=y+h/2){callback(BOTTOM_LEFT)}}if(item.x+item.w>=x+w/2){if(item.y<y+h/2){callback(TOP_RIGHT)}if(item.y+item.h>=y+h/2){callback(BOTTOM_RIGHT)}}},divide:function(){var width,height,i,oldChildren;var childrenDepth=this.depth+1;width=w/2;height=h/2;nodes.push(node(this.x,this.y,width,height,childrenDepth,maxChildren,maxDepth));nodes.push(node(this.x+width,this.y,width,height,childrenDepth,maxChildren,maxDepth));nodes.push(node(this.x,this.y+height,width,height,childrenDepth,maxChildren,maxDepth));nodes.push(node(this.x+width,this.y+height,width,height,childrenDepth,maxChildren,maxDepth));oldChildren=items;items=[];for(i=0;i<oldChildren.length;i++){this.insert(oldChildren[i])}},clear:function(){var i;for(i=0;i<nodes.length;i++){nodes[i].clear()}items.length=0;nodes.length=0},getNodes:function(){return nodes.length?nodes:false}}};return{root:function(){return node(args.x,args.y,args.w,args.h,0,args.maxChildren,args.maxDepth)}(),insert:function(item){var len,i;if(item instanceof Array){len=item.length;for(i=0;i<len;i++){this.root.insert(item[i])}}else{this.root.insert(item)}},retrieve:function(selector,callback,instance){return this.root.retrieve(selector,callback,instance)},clear:function(){this.root.clear()}}};
