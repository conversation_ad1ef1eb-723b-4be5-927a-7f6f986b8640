# Installation and Setup Guide

This guide will walk you through installing and setting up the Droplet Visual Programming extension for VS Code.

## Prerequisites

- **Visual Studio Code** version 1.74.0 or higher
- **Node.js** version 16.x or higher (for development)
- **npm** or **yarn** package manager

## Installation Methods

### Method 1: From Source (Development)

1. **Clone the repository:**
   ```bash
   git clone https://github.com/your-username/vscode-droplet.git
   cd vscode-droplet
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Compile the extension:**
   ```bash
   npm run compile
   ```

4. **Launch VS Code with the extension:**
   - Open the project in VS Code
   - Press `F5` to launch a new Extension Development Host window
   - The extension will be automatically loaded in the new window

### Method 2: Package and Install

1. **Install VSCE (Visual Studio Code Extension manager):**
   ```bash
   npm install -g vsce
   ```

2. **Package the extension:**
   ```bash
   vsce package
   ```

3. **Install the packaged extension:**
   ```bash
   code --install-extension vscode-droplet-0.1.0.vsix
   ```

### Method 3: Manual Installation

1. Copy the entire extension folder to your VS Code extensions directory:
   - **Windows:** `%USERPROFILE%\.vscode\extensions\`
   - **macOS:** `~/.vscode/extensions/`
   - **Linux:** `~/.vscode/extensions/`

2. Restart VS Code

## Verification

After installation, verify the extension is working:

1. **Check Extension List:**
   - Open VS Code
   - Go to Extensions view (`Ctrl+Shift+X`)
   - Search for "Droplet Visual Programming"
   - Ensure it's enabled

2. **Test Commands:**
   - Open Command Palette (`Ctrl+Shift+P`)
   - Type "Droplet" to see available commands:
     - `Droplet: Create New Visual Programming File`
     - `Droplet: Open in Droplet Visual Editor`
     - `Droplet: Toggle Text/Visual Mode`

3. **Test File Association:**
   - Create a test file with extension `.visual.js`
   - Right-click and verify "Open in Droplet Visual Editor" appears in context menu

## First Use

### Creating Your First Visual Program

1. **Open Command Palette** (`Ctrl+Shift+P`)
2. **Run:** `Droplet: Create New Visual Programming File`
3. **Select Language:** Choose JavaScript, Python, or CoffeeScript
4. **Enter Filename:** e.g., "my-first-program"
5. **Start Programming:** The visual editor will open with a basic template

### Converting Existing Code

1. **Open an existing** `.js`, `.py`, or `.coffee` file
2. **Right-click** in the file explorer
3. **Select:** "Open in Droplet Visual Editor"
4. **View your code** as visual blocks

## Configuration

### Workspace Settings

Add these settings to your workspace `.vscode/settings.json`:

```json
{
  "files.associations": {
    "*.visual.js": "javascript",
    "*.visual.py": "python",
    "*.visual.coffee": "coffeescript"
  },
  "droplet.defaultLanguage": "javascript",
  "droplet.autoSave": true
}
```

### User Settings

For global configuration, add to your user settings:

```json
{
  "droplet.enableContextMenu": true,
  "droplet.showWelcomeMessage": true
}
```

## Troubleshooting

### Common Issues

**Extension not loading:**
- Check VS Code version (must be 1.74.0+)
- Restart VS Code
- Check Developer Console for errors

**Droplet editor not displaying:**
- Ensure internet connection (for CDN resources)
- Check browser console in webview
- Try reloading the window

**Files not opening in visual editor:**
- Verify file extension is supported
- Check file size (large files may have issues)
- Ensure file contains valid syntax

### Debug Mode

To enable debug logging:

1. Open VS Code settings
2. Search for "droplet"
3. Enable "Droplet: Debug Mode"
4. Check Output panel for debug information

### Getting Help

- **GitHub Issues:** Report bugs and feature requests
- **Documentation:** Check README.md for detailed usage
- **Examples:** See the `examples/` folder for sample files

## Development Setup

For contributing to the extension:

1. **Fork the repository**
2. **Clone your fork**
3. **Install dependencies:** `npm install`
4. **Start development:** `npm run watch`
5. **Run tests:** `npm test`
6. **Debug:** Press `F5` in VS Code

### Building from Source

```bash
# Install dependencies
npm install

# Compile TypeScript
npm run compile

# Watch for changes (development)
npm run watch

# Run tests
npm test

# Package extension
vsce package
```

## Next Steps

- Read the [README.md](README.md) for detailed usage instructions
- Check out [examples/](examples/) for sample programs
- Visit the [Droplet documentation](https://github.com/droplet-editor/droplet) to learn about the underlying editor
- Join the community and contribute to the project!
