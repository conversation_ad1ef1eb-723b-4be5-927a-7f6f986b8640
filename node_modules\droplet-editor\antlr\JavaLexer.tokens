ABSTRACT=1
ASSERT=2
BOOLEAN=3
BREAK=4
BYTE=5
CASE=6
CATCH=7
CHAR=8
CLASS=9
CONST=10
CONTINUE=11
DEFAULT=12
DO=13
DOUBLE=14
ELSE=15
ENUM=16
EXTENDS=17
FINAL=18
FINALLY=19
FLOAT=20
FOR=21
IF=22
GOTO=23
IMPLEMENTS=24
IMPORT=25
INSTANCEOF=26
INT=27
INTERFACE=28
LONG=29
NATIVE=30
NEW=31
PACKAGE=32
PRIVATE=33
PROTECTED=34
PUBLIC=35
RETURN=36
SHORT=37
STATIC=38
STRICTFP=39
SUPER=40
SWITCH=41
SYNCHRONIZED=42
THIS=43
THROW=44
THROWS=45
TRANSIENT=46
TRY=47
VOID=48
VOLATILE=49
WHILE=50
IntegerLiteral=51
FloatingPointLiteral=52
BooleanLiteral=53
CharacterLiteral=54
StringLiteral=55
NullLiteral=56
LPAREN=57
RPAREN=58
LBRACE=59
RBRACE=60
LBRACK=61
RBRACK=62
SEMI=63
COMMA=64
DOT=65
ASSIGN=66
GT=67
LT=68
BANG=69
TILDE=70
QUESTION=71
COLON=72
EQUAL=73
LE=74
GE=75
NOTEQUAL=76
AND=77
OR=78
INC=79
DEC=80
ADD=81
SUB=82
MUL=83
DIV=84
BITAND=85
BITOR=86
CARET=87
MOD=88
ADD_ASSIGN=89
SUB_ASSIGN=90
MUL_ASSIGN=91
DIV_ASSIGN=92
AND_ASSIGN=93
OR_ASSIGN=94
XOR_ASSIGN=95
MOD_ASSIGN=96
LSHIFT_ASSIGN=97
RSHIFT_ASSIGN=98
URSHIFT_ASSIGN=99
Identifier=100
AT=101
ELLIPSIS=102
WS=103
COMMENT=104
LINE_COMMENT=105
'abstract'=1
'assert'=2
'boolean'=3
'break'=4
'byte'=5
'case'=6
'catch'=7
'char'=8
'class'=9
'const'=10
'continue'=11
'default'=12
'do'=13
'double'=14
'else'=15
'enum'=16
'extends'=17
'final'=18
'finally'=19
'float'=20
'for'=21
'if'=22
'goto'=23
'implements'=24
'import'=25
'instanceof'=26
'int'=27
'interface'=28
'long'=29
'native'=30
'new'=31
'package'=32
'private'=33
'protected'=34
'public'=35
'return'=36
'short'=37
'static'=38
'strictfp'=39
'super'=40
'switch'=41
'synchronized'=42
'this'=43
'throw'=44
'throws'=45
'transient'=46
'try'=47
'void'=48
'volatile'=49
'while'=50
'null'=56
'('=57
')'=58
'{'=59
'}'=60
'['=61
']'=62
';'=63
','=64
'.'=65
'='=66
'>'=67
'<'=68
'!'=69
'~'=70
'?'=71
':'=72
'=='=73
'<='=74
'>='=75
'!='=76
'&&'=77
'||'=78
'++'=79
'--'=80
'+'=81
'-'=82
'*'=83
'/'=84
'&'=85
'|'=86
'^'=87
'%'=88
'+='=89
'-='=90
'*='=91
'/='=92
'&='=93
'|='=94
'^='=95
'%='=96
'<<='=97
'>>='=98
'>>>='=99
'@'=101
'...'=102
