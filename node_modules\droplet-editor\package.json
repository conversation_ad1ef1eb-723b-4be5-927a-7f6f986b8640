{"name": "droplet-editor", "version": "0.0.2", "devDependencies": {"browserify": "^10.2.4", "browserify-shim": "^3.8.8", "coffeeify": "^1.1.0", "grunt": "0.4.5", "grunt-banner": "latest", "grunt-bowercopy": "latest", "grunt-browserify": "^3.8.0", "grunt-cli": "latest", "grunt-contrib-coffee": "latest", "grunt-contrib-concat": "latest", "grunt-contrib-connect": "latest", "grunt-contrib-cssmin": "latest", "grunt-contrib-qunit": "<=0.5.2", "grunt-contrib-uglify": "latest", "grunt-contrib-watch": "latest", "grunt-docco": "latest", "grunt-mocha-spawn": "latest", "grunt-mocha-test": "^0.12.7", "mocha": "^2.2.5", "seedrandom": "^2.4.0", "tiny-lr": "^0.1.5", "watchify": "^3.2.2"}, "description": "A text editor in blocks", "main": "dist/droplet-full.min.js", "directories": {"example": "example", "test": "test"}, "scripts": {"test": "grunt all"}, "repository": {"type": "git", "url": "https://github.com/dabbler0/droplet.git"}, "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/dabbler0/droplet/issues"}, "browserify": {"transform": ["browserify-shim"]}, "browserify-shim": {"./vendor/quadtree.js": "QUAD"}, "homepage": "https://github.com/dabbler0/droplet", "dependencies": {"acorn": "^1.2.2", "sax": "^1.1.1", "antlr4": "latest", "parse5": "latest"}}