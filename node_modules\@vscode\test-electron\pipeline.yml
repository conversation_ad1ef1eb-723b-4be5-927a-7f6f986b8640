name: $(Date:yyyyMMdd)$(Rev:.r)

trigger:
  branches:
    include:
      - main

resources:
  repositories:
    - repository: templates
      type: github
      name: microsoft/vscode-engineering
      ref: main
      endpoint: Monaco

parameters:
  - name: publishPackage
    displayName: 🚀 Publish test-electron
    type: boolean
    default: false

extends:
  template: azure-pipelines/npm-package/pipeline.yml@templates
  parameters:
    npmPackages:
      - name: test-electron
        ghCreateTag: false
        buildSteps:
          - script: npm ci
            displayName: Install dependencies

        testPlatforms:
          - name: Linux
            nodeVersions:
              - 20.x
          - name: MacOS
            nodeVersions:
              - 20.x
          - name: Windows
            nodeVersions:
              - 20.x

        testSteps:
          - script: npm ci
            displayName: Install dependencies

          - script: npm test
            displayName: Run own tests

          - script: cd sample && npm ci
            displayName: Install dependencies (fs-provider)

          - bash: |
              /usr/bin/Xvfb :99 -screen 0 1024x768x24 > /dev/null 2>&1 &
              echo ">>> Started xvfb"
            displayName: Start xvfb
            condition: eq(variables['Agent.OS'], 'Linux')

          - task: NodeTool@0
            displayName: Switch to Node 16
            inputs:
              versionSpec: 16.x

          - script: cd sample && npm run test
            displayName: Test package
            env:
              DISPLAY: ':99.0'

        publishPackage: ${{ parameters.publishPackage }}
