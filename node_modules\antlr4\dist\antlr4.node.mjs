import{createRequire as t}from"node:module";var e={d:(t,n)=>{for(var s in n)e.o(n,s)&&!e.o(t,s)&&Object.defineProperty(t,s,{enumerable:!0,get:n[s]})},o:(t,e)=>Object.prototype.hasOwnProperty.call(t,e)},n={};e.d(n,{MG:()=>j,fr:()=>yt,sR:()=>Ce,Zo:()=>Ne,iH:()=>Oe,rt:()=>vt,jB:()=>Pe,M8:()=>ae,$t:()=>_e,aq:()=>Se,pG:()=>Lt,eP:()=>xe,tf:()=>Le,KU:()=>fe,zW:()=>ke,IX:()=>m,mY:()=>E,a7:()=>W,JG:()=>Mt,ay:()=>$t,X2:()=>te,WU:()=>ue,Uw:()=>de,gw:()=>ge,iX:()=>De,re:()=>ne,Pg:()=>Ue,tD:()=>se,R$:()=>Qt,Dj:()=>Dt,m7:()=>M,NZ:()=>v,xo:()=>P,ou:()=>s,qC:()=>Be,mD:()=>u,Ay:()=>Ke});class s{constructor(){this.source=null,this.type=null,this.channel=null,this.start=null,this.stop=null,this.tokenIndex=null,this.line=null,this.column=null,this._text=null}getTokenSource(){return this.source[0]}getInputStream(){return this.source[1]}get text(){return this._text}set text(t){this._text=t}}function i(t,e){if(!Array.isArray(t)||!Array.isArray(e))return!1;if(t===e)return!0;if(t.length!==e.length)return!1;for(let n=0;n<t.length;n++)if(!(t[n]===e[n]||t[n].equals&&t[n].equals(e[n])))return!1;return!0}s.INVALID_TYPE=0,s.EPSILON=-2,s.MIN_USER_TOKEN_TYPE=1,s.EOF=-1,s.DEFAULT_CHANNEL=0,s.HIDDEN_CHANNEL=1;const r=Math.round(Math.random()*Math.pow(2,32));function o(t){if(!t)return 0;const e=typeof t,n="string"===e?t:!("object"!==e||!t.toString)&&t.toString();if(!n)return 0;let s,i;const o=3&n.length,a=n.length-o;let l=r;const h=3432918353,c=461845907;let u=0;for(;u<a;)i=255&n.charCodeAt(u)|(255&n.charCodeAt(++u))<<8|(255&n.charCodeAt(++u))<<16|(255&n.charCodeAt(++u))<<24,++u,i=(65535&i)*h+(((i>>>16)*h&65535)<<16)&4294967295,i=i<<15|i>>>17,i=(65535&i)*c+(((i>>>16)*c&65535)<<16)&4294967295,l^=i,l=l<<13|l>>>19,s=5*(65535&l)+((5*(l>>>16)&65535)<<16)&4294967295,l=27492+(65535&s)+((58964+(s>>>16)&65535)<<16);switch(i=0,o){case 3:i^=(255&n.charCodeAt(u+2))<<16;case 2:i^=(255&n.charCodeAt(u+1))<<8;case 1:i^=255&n.charCodeAt(u),i=(65535&i)*h+(((i>>>16)*h&65535)<<16)&4294967295,i=i<<15|i>>>17,i=(65535&i)*c+(((i>>>16)*c&65535)<<16)&4294967295,l^=i}return l^=n.length,l^=l>>>16,l=2246822507*(65535&l)+((2246822507*(l>>>16)&65535)<<16)&4294967295,l^=l>>>13,l=3266489909*(65535&l)+((3266489909*(l>>>16)&65535)<<16)&4294967295,l^=l>>>16,l>>>0}class a{constructor(){this.count=0,this.hash=0}update(){for(let t=0;t<arguments.length;t++){const e=arguments[t];if(null!=e)if(Array.isArray(e))this.update.apply(this,e);else{let t=0;switch(typeof e){case"undefined":case"function":continue;case"number":case"boolean":t=e;break;case"string":t=o(e);break;default:e.updateHashCode?e.updateHashCode(this):console.log("No updateHashCode for "+e.toString());continue}t*=3432918353,t=t<<15|t>>>17,t*=461845907,this.count=this.count+1;let n=this.hash^t;n=n<<13|n>>>19,n=5*n+3864292196,this.hash=n}}}finish(){let t=this.hash^4*this.count;return t^=t>>>16,t*=2246822507,t^=t>>>13,t*=3266489909,t^=t>>>16,t}static hashStuff(){const t=new a;return t.update.apply(t,arguments),t.finish()}}function l(t){return t?"string"==typeof t?o(t):t.hashCode():-1}function h(t,e){return t&&t.equals?t.equals(e):t===e}function c(t){return null===t?"null":t}function u(t){return Array.isArray(t)?"["+t.map(c).join(", ")+"]":"null"}class d{constructor(t,e){this.buckets=new Array(16),this.threshold=Math.floor(12),this.itemCount=0,this.hashFunction=t||l,this.equalsFunction=e||h}get(t){if(null==t)return t;const e=this._getBucket(t);if(!e)return null;for(const n of e)if(this.equalsFunction(n,t))return n;return null}add(t){return this.getOrAdd(t)===t}getOrAdd(t){this._expand();const e=this._getSlot(t);let n=this.buckets[e];if(!n)return n=[t],this.buckets[e]=n,this.itemCount++,t;for(const e of n)if(this.equalsFunction(e,t))return e;return n.push(t),this.itemCount++,t}has(t){return null!=this.get(t)}values(){return this.buckets.filter((t=>null!=t)).flat(1)}toString(){return u(this.values())}get length(){return this.itemCount}_getSlot(t){return this.hashFunction(t)&this.buckets.length-1}_getBucket(t){return this.buckets[this._getSlot(t)]}_expand(){if(this.itemCount<=this.threshold)return;const t=this.buckets,e=2*this.buckets.length;this.buckets=new Array(e),this.threshold=Math.floor(.75*e);for(const e of t)if(e)for(const t of e){const e=this._getSlot(t);let n=this.buckets[e];n||(n=[],this.buckets[e]=n),n.push(t)}}}class g{hashCode(){const t=new a;return this.updateHashCode(t),t.finish()}evaluate(t,e){}evalPrecedence(t,e){return this}static andContext(t,e){if(null===t||t===g.NONE)return e;if(null===e||e===g.NONE)return t;const n=new p(t,e);return 1===n.opnds.length?n.opnds[0]:n}static orContext(t,e){if(null===t)return e;if(null===e)return t;if(t===g.NONE||e===g.NONE)return g.NONE;const n=new f(t,e);return 1===n.opnds.length?n.opnds[0]:n}}class p extends g{constructor(t,e){super();const n=new d;t instanceof p?t.opnds.map((function(t){n.add(t)})):n.add(t),e instanceof p?e.opnds.map((function(t){n.add(t)})):n.add(e);const s=x(n);if(s.length>0){let t=null;s.map((function(e){(null===t||e.precedence<t.precedence)&&(t=e)})),n.add(t)}this.opnds=Array.from(n.values())}equals(t){return this===t||t instanceof p&&i(this.opnds,t.opnds)}updateHashCode(t){t.update(this.opnds,"AND")}evaluate(t,e){for(let n=0;n<this.opnds.length;n++)if(!this.opnds[n].evaluate(t,e))return!1;return!0}evalPrecedence(t,e){let n=!1;const s=[];for(let i=0;i<this.opnds.length;i++){const r=this.opnds[i],o=r.evalPrecedence(t,e);if(n|=o!==r,null===o)return null;o!==g.NONE&&s.push(o)}if(!n)return this;if(0===s.length)return g.NONE;let i=null;return s.map((function(t){i=null===i?t:g.andContext(i,t)})),i}toString(){const t=this.opnds.map((t=>t.toString()));return(t.length>3?t.slice(3):t).join("&&")}}class f extends g{constructor(t,e){super();const n=new d;t instanceof f?t.opnds.map((function(t){n.add(t)})):n.add(t),e instanceof f?e.opnds.map((function(t){n.add(t)})):n.add(e);const s=x(n);if(s.length>0){const t=s.sort((function(t,e){return t.compareTo(e)})),e=t[t.length-1];n.add(e)}this.opnds=Array.from(n.values())}equals(t){return this===t||t instanceof f&&i(this.opnds,t.opnds)}updateHashCode(t){t.update(this.opnds,"OR")}evaluate(t,e){for(let n=0;n<this.opnds.length;n++)if(this.opnds[n].evaluate(t,e))return!0;return!1}evalPrecedence(t,e){let n=!1;const s=[];for(let i=0;i<this.opnds.length;i++){const r=this.opnds[i],o=r.evalPrecedence(t,e);if(n|=o!==r,o===g.NONE)return g.NONE;null!==o&&s.push(o)}if(!n)return this;if(0===s.length)return null;return s.map((function(t){return t})),null}toString(){const t=this.opnds.map((t=>t.toString()));return(t.length>3?t.slice(3):t).join("||")}}function x(t){const e=[];return t.values().map((function(t){t instanceof g.PrecedencePredicate&&e.push(t)})),e}function T(t,e){if(null===t){const t={state:null,alt:null,context:null,semanticContext:null};return e&&(t.reachesIntoOuterContext=0),t}{const n={};return n.state=t.state||null,n.alt=void 0===t.alt?null:t.alt,n.context=t.context||null,n.semanticContext=t.semanticContext||null,e&&(n.reachesIntoOuterContext=t.reachesIntoOuterContext||0,n.precedenceFilterSuppressed=t.precedenceFilterSuppressed||!1),n}}class S{constructor(t,e){this.checkContext(t,e),t=T(t),e=T(e,!0),this.state=null!==t.state?t.state:e.state,this.alt=null!==t.alt?t.alt:e.alt,this.context=null!==t.context?t.context:e.context,this.semanticContext=null!==t.semanticContext?t.semanticContext:null!==e.semanticContext?e.semanticContext:g.NONE,this.reachesIntoOuterContext=e.reachesIntoOuterContext,this.precedenceFilterSuppressed=e.precedenceFilterSuppressed}checkContext(t,e){null!==t.context&&void 0!==t.context||null!==e&&null!==e.context&&void 0!==e.context||(this.context=null)}hashCode(){const t=new a;return this.updateHashCode(t),t.finish()}updateHashCode(t){t.update(this.state.stateNumber,this.alt,this.context,this.semanticContext)}equals(t){return this===t||t instanceof S&&this.state.stateNumber===t.state.stateNumber&&this.alt===t.alt&&(null===this.context?null===t.context:this.context.equals(t.context))&&this.semanticContext.equals(t.semanticContext)&&this.precedenceFilterSuppressed===t.precedenceFilterSuppressed}hashCodeForConfigSet(){const t=new a;return t.update(this.state.stateNumber,this.alt,this.semanticContext),t.finish()}equalsForConfigSet(t){return this===t||t instanceof S&&this.state.stateNumber===t.state.stateNumber&&this.alt===t.alt&&this.semanticContext.equals(t.semanticContext)}toString(){return"("+this.state+","+this.alt+(null!==this.context?",["+this.context.toString()+"]":"")+(this.semanticContext!==g.NONE?","+this.semanticContext.toString():"")+(this.reachesIntoOuterContext>0?",up="+this.reachesIntoOuterContext:"")+")"}}class m{constructor(t,e){this.start=t,this.stop=e}clone(){return new m(this.start,this.stop)}contains(t){return t>=this.start&&t<this.stop}toString(){return this.start===this.stop-1?this.start.toString():this.start.toString()+".."+(this.stop-1).toString()}get length(){return this.stop-this.start}}m.INVALID_INTERVAL=new m(-1,-2);class E{constructor(){this.intervals=null,this.readOnly=!1}first(t){return null===this.intervals||0===this.intervals.length?s.INVALID_TYPE:this.intervals[0].start}addOne(t){this.addInterval(new m(t,t+1))}addRange(t,e){this.addInterval(new m(t,e+1))}addInterval(t){if(null===this.intervals)this.intervals=[],this.intervals.push(t.clone());else{for(let e=0;e<this.intervals.length;e++){const n=this.intervals[e];if(t.stop<n.start)return void this.intervals.splice(e,0,t);if(t.stop===n.start)return void(this.intervals[e]=new m(t.start,n.stop));if(t.start<=n.stop)return this.intervals[e]=new m(Math.min(n.start,t.start),Math.max(n.stop,t.stop)),void this.reduce(e)}this.intervals.push(t.clone())}}addSet(t){return null!==t.intervals&&t.intervals.forEach((t=>this.addInterval(t)),this),this}reduce(t){if(t<this.intervals.length-1){const e=this.intervals[t],n=this.intervals[t+1];e.stop>=n.stop?(this.intervals.splice(t+1,1),this.reduce(t)):e.stop>=n.start&&(this.intervals[t]=new m(e.start,n.stop),this.intervals.splice(t+1,1))}}complement(t,e){const n=new E;return n.addInterval(new m(t,e+1)),null!==this.intervals&&this.intervals.forEach((t=>n.removeRange(t))),n}contains(t){if(null===this.intervals)return!1;for(let e=0;e<this.intervals.length;e++)if(this.intervals[e].contains(t))return!0;return!1}removeRange(t){if(t.start===t.stop-1)this.removeOne(t.start);else if(null!==this.intervals){let e=0;for(let n=0;n<this.intervals.length;n++){const n=this.intervals[e];if(t.stop<=n.start)return;if(t.start>n.start&&t.stop<n.stop){this.intervals[e]=new m(n.start,t.start);const s=new m(t.stop,n.stop);return void this.intervals.splice(e,0,s)}t.start<=n.start&&t.stop>=n.stop?(this.intervals.splice(e,1),e-=1):t.start<n.stop?this.intervals[e]=new m(n.start,t.start):t.stop<n.stop&&(this.intervals[e]=new m(t.stop,n.stop)),e+=1}}}removeOne(t){if(null!==this.intervals)for(let e=0;e<this.intervals.length;e++){const n=this.intervals[e];if(t<n.start)return;if(t===n.start&&t===n.stop-1)return void this.intervals.splice(e,1);if(t===n.start)return void(this.intervals[e]=new m(n.start+1,n.stop));if(t===n.stop-1)return void(this.intervals[e]=new m(n.start,n.stop-1));if(t<n.stop-1){const s=new m(n.start,t);return n.start=t+1,void this.intervals.splice(e,0,s)}}}toString(t,e,n){return t=t||null,e=e||null,n=n||!1,null===this.intervals?"{}":null!==t||null!==e?this.toTokenString(t,e):n?this.toCharString():this.toIndexString()}toCharString(){const t=[];for(let e=0;e<this.intervals.length;e++){const n=this.intervals[e];n.stop===n.start+1?n.start===s.EOF?t.push("<EOF>"):t.push("'"+String.fromCharCode(n.start)+"'"):t.push("'"+String.fromCharCode(n.start)+"'..'"+String.fromCharCode(n.stop-1)+"'")}return t.length>1?"{"+t.join(", ")+"}":t[0]}toIndexString(){const t=[];for(let e=0;e<this.intervals.length;e++){const n=this.intervals[e];n.stop===n.start+1?n.start===s.EOF?t.push("<EOF>"):t.push(n.start.toString()):t.push(n.start.toString()+".."+(n.stop-1).toString())}return t.length>1?"{"+t.join(", ")+"}":t[0]}toTokenString(t,e){const n=[];for(let s=0;s<this.intervals.length;s++){const i=this.intervals[s];for(let s=i.start;s<i.stop;s++)n.push(this.elementName(t,e,s))}return n.length>1?"{"+n.join(", ")+"}":n[0]}elementName(t,e,n){return n===s.EOF?"<EOF>":n===s.EPSILON?"<EPSILON>":t[n]||e[n]}get length(){return this.intervals.map((t=>t.length)).reduce(((t,e)=>t+e))}}class _{constructor(){this.atn=null,this.stateNumber=_.INVALID_STATE_NUMBER,this.stateType=null,this.ruleIndex=0,this.epsilonOnlyTransitions=!1,this.transitions=[],this.nextTokenWithinRule=null}toString(){return this.stateNumber}equals(t){return t instanceof _&&this.stateNumber===t.stateNumber}isNonGreedyExitState(){return!1}addTransition(t,e){void 0===e&&(e=-1),0===this.transitions.length?this.epsilonOnlyTransitions=t.isEpsilon:this.epsilonOnlyTransitions!==t.isEpsilon&&(this.epsilonOnlyTransitions=!1),-1===e?this.transitions.push(t):this.transitions.splice(e,1,t)}}_.INVALID_TYPE=0,_.BASIC=1,_.RULE_START=2,_.BLOCK_START=3,_.PLUS_BLOCK_START=4,_.STAR_BLOCK_START=5,_.TOKEN_START=6,_.RULE_STOP=7,_.BLOCK_END=8,_.STAR_LOOP_BACK=9,_.STAR_LOOP_ENTRY=10,_.PLUS_LOOP_BACK=11,_.LOOP_END=12,_.serializationNames=["INVALID","BASIC","RULE_START","BLOCK_START","PLUS_BLOCK_START","STAR_BLOCK_START","TOKEN_START","RULE_STOP","BLOCK_END","STAR_LOOP_BACK","STAR_LOOP_ENTRY","PLUS_LOOP_BACK","LOOP_END"],_.INVALID_STATE_NUMBER=-1;class C extends _{constructor(){return super(),this.stateType=_.RULE_STOP,this}}class A{constructor(t){if(null==t)throw"target cannot be null.";this.target=t,this.isEpsilon=!1,this.label=null}}A.EPSILON=1,A.RANGE=2,A.RULE=3,A.PREDICATE=4,A.ATOM=5,A.ACTION=6,A.SET=7,A.NOT_SET=8,A.WILDCARD=9,A.PRECEDENCE=10,A.serializationNames=["INVALID","EPSILON","RANGE","RULE","PREDICATE","ATOM","ACTION","SET","NOT_SET","WILDCARD","PRECEDENCE"],A.serializationTypes={EpsilonTransition:A.EPSILON,RangeTransition:A.RANGE,RuleTransition:A.RULE,PredicateTransition:A.PREDICATE,AtomTransition:A.ATOM,ActionTransition:A.ACTION,SetTransition:A.SET,NotSetTransition:A.NOT_SET,WildcardTransition:A.WILDCARD,PrecedencePredicateTransition:A.PRECEDENCE};class N extends A{constructor(t,e,n,s){super(t),this.ruleIndex=e,this.precedence=n,this.followState=s,this.serializationType=A.RULE,this.isEpsilon=!0}matches(t,e,n){return!1}}class k extends A{constructor(t,e){super(t),this.serializationType=A.SET,null!=e?this.label=e:(this.label=new E,this.label.addOne(s.INVALID_TYPE))}matches(t,e,n){return this.label.contains(t)}toString(){return this.label.toString()}}class I extends k{constructor(t,e){super(t,e),this.serializationType=A.NOT_SET}matches(t,e,n){return t>=e&&t<=n&&!super.matches(t,e,n)}toString(){return"~"+super.toString()}}class y extends A{constructor(t){super(t),this.serializationType=A.WILDCARD}matches(t,e,n){return t>=e&&t<=n}toString(){return"."}}class L extends A{constructor(t){super(t)}}class O{}class R extends O{}class w extends R{}class v extends w{get ruleContext(){throw new Error("missing interface implementation")}}class P extends w{}class b extends P{}const D={toStringTree:function(t,e,n){e=e||null,null!==(n=n||null)&&(e=n.ruleNames);let s=D.getNodeText(t,e);s=function(t){return t=t.replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r")}(s);const i=t.getChildCount();if(0===i)return s;let r="("+s+" ";i>0&&(s=D.toStringTree(t.getChild(0),e),r=r.concat(s));for(let n=1;n<i;n++)s=D.toStringTree(t.getChild(n),e),r=r.concat(" "+s);return r=r.concat(")"),r},getNodeText:function(t,e,n){if(e=e||null,null!==(n=n||null)&&(e=n.ruleNames),null!==e){if(t instanceof v){const n=t.ruleContext.getAltNumber();return 0!=n?e[t.ruleIndex]+":"+n:e[t.ruleIndex]}if(t instanceof b)return t.toString();if(t instanceof P&&null!==t.symbol)return t.symbol.text}const i=t.getPayload();return i instanceof s?i.text:t.getPayload().toString()},getChildren:function(t){const e=[];for(let n=0;n<t.getChildCount();n++)e.push(t.getChild(n));return e},getAncestors:function(t){let e=[];for(t=t.getParent();null!==t;)e=[t].concat(e),t=t.getParent();return e},findAllTokenNodes:function(t,e){return D.findAllNodes(t,e,!0)},findAllRuleNodes:function(t,e){return D.findAllNodes(t,e,!1)},findAllNodes:function(t,e,n){const s=[];return D._findAllNodes(t,e,n,s),s},_findAllNodes:function(t,e,n,s){n&&t instanceof P?t.symbol.type===e&&s.push(t):!n&&t instanceof v&&t.ruleIndex===e&&s.push(t);for(let i=0;i<t.getChildCount();i++)D._findAllNodes(t.getChild(i),e,n,s)},descendants:function(t){let e=[t];for(let n=0;n<t.getChildCount();n++)e=e.concat(D.descendants(t.getChild(n)));return e}},F=D;class M extends v{constructor(t,e){super(),this.parentCtx=t||null,this.invokingState=e||-1}depth(){let t=0,e=this;for(;null!==e;)e=e.parentCtx,t+=1;return t}isEmpty(){return-1===this.invokingState}getSourceInterval(){return m.INVALID_INTERVAL}get ruleContext(){return this}getPayload(){return this}getText(){return 0===this.getChildCount()?"":this.children.map((function(t){return t.getText()})).join("")}getAltNumber(){return 0}setAltNumber(t){}getChild(t){return null}getChildCount(){return 0}accept(t){return t.visitChildren(this)}toStringTree(t,e){return F.toStringTree(this,t,e)}toString(t,e){t=t||null,e=e||null;let n=this,s="[";for(;null!==n&&n!==e;){if(null===t)n.isEmpty()||(s+=n.invokingState);else{const e=n.ruleIndex;s+=e>=0&&e<t.length?t[e]:""+e}null===n.parentCtx||null===t&&n.parentCtx.isEmpty()||(s+=" "),n=n.parentCtx}return s+="]",s}}class U{constructor(t){this.cachedHashCode=t}isEmpty(){return this===U.EMPTY}hasEmptyPath(){return this.getReturnState(this.length-1)===U.EMPTY_RETURN_STATE}hashCode(){return this.cachedHashCode}updateHashCode(t){t.update(this.cachedHashCode)}}U.EMPTY=null,U.EMPTY_RETURN_STATE=2147483647,U.globalNodeCount=1,U.id=U.globalNodeCount,U.trace_atn_sim=!1;class B extends U{constructor(t,e){const n=new a;return n.update(t,e),super(n.finish()),this.parents=t,this.returnStates=e,this}isEmpty(){return this.returnStates[0]===U.EMPTY_RETURN_STATE}getParent(t){return this.parents[t]}getReturnState(t){return this.returnStates[t]}equals(t){return this===t||t instanceof B&&this.hashCode()===t.hashCode()&&i(this.returnStates,t.returnStates)&&i(this.parents,t.parents)}toString(){if(this.isEmpty())return"[]";{let t="[";for(let e=0;e<this.returnStates.length;e++)e>0&&(t+=", "),this.returnStates[e]!==U.EMPTY_RETURN_STATE?(t+=this.returnStates[e],null!==this.parents[e]?t=t+" "+this.parents[e]:t+="null"):t+="$";return t+"]"}}get length(){return this.returnStates.length}}class z extends U{constructor(t,e){let n=0;const s=new a;null!==t?s.update(t,e):s.update(1),n=s.finish(),super(n),this.parentCtx=t,this.returnState=e}getParent(t){return this.parentCtx}getReturnState(t){return this.returnState}equals(t){return this===t||t instanceof z&&this.hashCode()===t.hashCode()&&this.returnState===t.returnState&&(null==this.parentCtx?null==t.parentCtx:this.parentCtx.equals(t.parentCtx))}toString(){const t=null===this.parentCtx?"":this.parentCtx.toString();return 0===t.length?this.returnState===U.EMPTY_RETURN_STATE?"$":""+this.returnState:this.returnState+" "+t}get length(){return 1}static create(t,e){return e===U.EMPTY_RETURN_STATE&&null===t?U.EMPTY:new z(t,e)}}class V extends z{constructor(){super(null,U.EMPTY_RETURN_STATE)}isEmpty(){return!0}getParent(t){return null}getReturnState(t){return this.returnState}equals(t){return this===t}toString(){return"$"}}U.EMPTY=new V;class q{constructor(t,e){this.buckets=new Array(16),this.threshold=Math.floor(12),this.itemCount=0,this.hashFunction=t||l,this.equalsFunction=e||h}set(t,e){this._expand();const n=this._getSlot(t);let s=this.buckets[n];if(!s)return s=[[t,e]],this.buckets[n]=s,this.itemCount++,e;const i=s.find((e=>this.equalsFunction(e[0],t)),this);if(i){const t=i[1];return i[1]=e,t}return s.push([t,e]),this.itemCount++,e}containsKey(t){const e=this._getBucket(t);return!!e&&!!e.find((e=>this.equalsFunction(e[0],t)),this)}get(t){const e=this._getBucket(t);if(!e)return null;const n=e.find((e=>this.equalsFunction(e[0],t)),this);return n?n[1]:null}entries(){return this.buckets.filter((t=>null!=t)).flat(1)}getKeys(){return this.entries().map((t=>t[0]))}getValues(){return this.entries().map((t=>t[1]))}toString(){return"["+this.entries().map((t=>"{"+t[0]+":"+t[1]+"}")).join(", ")+"]"}get length(){return this.itemCount}_getSlot(t){return this.hashFunction(t)&this.buckets.length-1}_getBucket(t){return this.buckets[this._getSlot(t)]}_expand(){if(this.itemCount<=this.threshold)return;const t=this.buckets,e=2*this.buckets.length;this.buckets=new Array(e),this.threshold=Math.floor(.75*e);for(const e of t)if(e)for(const t of e){const e=this._getSlot(t[0]);let n=this.buckets[e];n||(n=[],this.buckets[e]=n),n.push(t)}}}function H(t,e){if(null==e&&(e=M.EMPTY),null===e.parentCtx||e===M.EMPTY)return U.EMPTY;const n=H(t,e.parentCtx),s=t.states[e.invokingState].transitions[0];return z.create(n,s.followState.stateNumber)}function K(t,e,n){if(t.isEmpty())return t;let s=n.get(t)||null;if(null!==s)return s;if(s=e.get(t),null!==s)return n.set(t,s),s;let i=!1,r=[];for(let s=0;s<r.length;s++){const o=K(t.getParent(s),e,n);if(i||o!==t.getParent(s)){if(!i){r=[];for(let e=0;e<t.length;e++)r[e]=t.getParent(e);i=!0}r[s]=o}}if(!i)return e.add(t),n.set(t,t),t;let o=null;return o=0===r.length?U.EMPTY:1===r.length?z.create(r[0],t.getReturnState(0)):new B(r,t.returnStates),e.add(o),n.set(o,o),n.set(t,o),o}function Y(t,e,n,s){if(t===e)return t;if(t instanceof z&&e instanceof z)return function(t,e,n,s){if(null!==s){let n=s.get(t,e);if(null!==n)return n;if(n=s.get(e,t),null!==n)return n}const i=function(t,e,n){if(n){if(t===U.EMPTY)return U.EMPTY;if(e===U.EMPTY)return U.EMPTY}else{if(t===U.EMPTY&&e===U.EMPTY)return U.EMPTY;if(t===U.EMPTY){const t=[e.returnState,U.EMPTY_RETURN_STATE],n=[e.parentCtx,null];return new B(n,t)}if(e===U.EMPTY){const e=[t.returnState,U.EMPTY_RETURN_STATE],n=[t.parentCtx,null];return new B(n,e)}}return null}(t,e,n);if(null!==i)return null!==s&&s.set(t,e,i),i;if(t.returnState===e.returnState){const i=Y(t.parentCtx,e.parentCtx,n,s);if(i===t.parentCtx)return t;if(i===e.parentCtx)return e;const r=z.create(i,t.returnState);return null!==s&&s.set(t,e,r),r}{let n=null;if((t===e||null!==t.parentCtx&&t.parentCtx===e.parentCtx)&&(n=t.parentCtx),null!==n){const i=[t.returnState,e.returnState];t.returnState>e.returnState&&(i[0]=e.returnState,i[1]=t.returnState);const r=new B([n,n],i);return null!==s&&s.set(t,e,r),r}const i=[t.returnState,e.returnState];let r=[t.parentCtx,e.parentCtx];t.returnState>e.returnState&&(i[0]=e.returnState,i[1]=t.returnState,r=[e.parentCtx,t.parentCtx]);const o=new B(r,i);return null!==s&&s.set(t,e,o),o}}(t,e,n,s);if(n){if(t instanceof V)return t;if(e instanceof V)return e}return t instanceof z&&(t=new B([t.getParent()],[t.returnState])),e instanceof z&&(e=new B([e.getParent()],[e.returnState])),function(t,e,n,s){if(null!==s){let n=s.get(t,e);if(null!==n)return U.trace_atn_sim&&console.log("mergeArrays a="+t+",b="+e+" -> previous"),n;if(n=s.get(e,t),null!==n)return U.trace_atn_sim&&console.log("mergeArrays a="+t+",b="+e+" -> previous"),n}let i=0,r=0,o=0,a=new Array(t.returnStates.length+e.returnStates.length).fill(0),l=new Array(t.returnStates.length+e.returnStates.length).fill(null);for(;i<t.returnStates.length&&r<e.returnStates.length;){const h=t.parents[i],c=e.parents[r];if(t.returnStates[i]===e.returnStates[r]){const e=t.returnStates[i];e===U.EMPTY_RETURN_STATE&&null===h&&null===c||null!==h&&null!==c&&h===c?(l[o]=h,a[o]=e):(l[o]=Y(h,c,n,s),a[o]=e),i+=1,r+=1}else t.returnStates[i]<e.returnStates[r]?(l[o]=h,a[o]=t.returnStates[i],i+=1):(l[o]=c,a[o]=e.returnStates[r],r+=1);o+=1}if(i<t.returnStates.length)for(let e=i;e<t.returnStates.length;e++)l[o]=t.parents[e],a[o]=t.returnStates[e],o+=1;else for(let t=r;t<e.returnStates.length;t++)l[o]=e.parents[t],a[o]=e.returnStates[t],o+=1;if(o<l.length){if(1===o){const n=z.create(l[0],a[0]);return null!==s&&s.set(t,e,n),n}l=l.slice(0,o),a=a.slice(0,o)}const h=new B(l,a);return h.equals(t)?(null!==s&&s.set(t,e,t),U.trace_atn_sim&&console.log("mergeArrays a="+t+",b="+e+" -> a"),t):h.equals(e)?(null!==s&&s.set(t,e,e),U.trace_atn_sim&&console.log("mergeArrays a="+t+",b="+e+" -> b"),e):(function(t){const e=new q;for(let n=0;n<t.length;n++){const s=t[n];e.containsKey(s)||e.set(s,s)}for(let n=0;n<t.length;n++)t[n]=e.get(t[n])}(l),null!==s&&s.set(t,e,h),U.trace_atn_sim&&console.log("mergeArrays a="+t+",b="+e+" -> "+h),h)}(t,e,n,s)}class G{constructor(){this.data=new Uint32Array(1)}set(t){G._checkIndex(t),this._resize(t),this.data[t>>>5]|=1<<t%32}get(t){G._checkIndex(t);const e=t>>>5;return!(e>=this.data.length||!(this.data[e]&1<<t%32))}clear(t){G._checkIndex(t);const e=t>>>5;e<this.data.length&&(this.data[e]&=~(1<<t))}or(t){const e=Math.min(this.data.length,t.data.length);for(let n=0;n<e;++n)this.data[n]|=t.data[n];if(this.data.length<t.data.length){this._resize((t.data.length<<5)-1);const n=t.data.length;for(let s=e;s<n;++s)this.data[s]=t.data[s]}}values(){const t=new Array(this.length);let e=0;const n=this.data.length;for(let s=0;s<n;++s){let n=this.data[s];for(;0!==n;){const i=n&-n;t[e++]=(s<<5)+G._bitCount(i-1),n^=i}}return t}minValue(){for(let t=0;t<this.data.length;++t){let e=this.data[t];if(0!==e){let n=0;for(;!(1&e);)n++,e>>=1;return n+32*t}}return 0}hashCode(){return a.hashStuff(this.values())}equals(t){return t instanceof G&&i(this.data,t.data)}toString(){return"{"+this.values().join(", ")+"}"}get length(){return this.data.map((t=>G._bitCount(t))).reduce(((t,e)=>t+e),0)}_resize(t){const e=t+32>>>5;if(e<=this.data.length)return;const n=new Uint32Array(e);n.set(this.data),n.fill(0,this.data.length),this.data=n}static _checkIndex(t){if(t<0)throw new RangeError("index cannot be negative")}static _bitCount(t){return t=(t=(858993459&(t-=t>>1&1431655765))+(t>>2&858993459))+(t>>4)&252645135,t+=t>>8,0+(t+=t>>16)&63}}class W{constructor(t){this.atn=t}getDecisionLookahead(t){if(null===t)return null;const e=t.transitions.length,n=[];for(let s=0;s<e;s++){n[s]=new E;const e=new d,i=!1;this._LOOK(t.transition(s).target,null,U.EMPTY,n[s],e,new G,i,!1),(0===n[s].length||n[s].contains(W.HIT_PRED))&&(n[s]=null)}return n}LOOK(t,e,n){const s=new E,i=null!==(n=n||null)?H(t.atn,n):null;return this._LOOK(t,e,i,s,new d,new G,!0,!0),s}_LOOK(t,e,n,i,r,o,a,l){const h=new S({state:t,alt:0,context:n},null);if(!r.has(h)){if(r.add(h),t===e){if(null===n)return void i.addOne(s.EPSILON);if(n.isEmpty()&&l)return void i.addOne(s.EOF)}if(t instanceof C){if(null===n)return void i.addOne(s.EPSILON);if(n.isEmpty()&&l)return void i.addOne(s.EOF);if(n!==U.EMPTY){const s=o.get(t.ruleIndex);try{o.clear(t.ruleIndex);for(let t=0;t<n.length;t++){const s=this.atn.states[n.getReturnState(t)];this._LOOK(s,e,n.getParent(t),i,r,o,a,l)}}finally{s&&o.set(t.ruleIndex)}return}}for(let h=0;h<t.transitions.length;h++){const c=t.transitions[h];if(c.constructor===N){if(o.get(c.target.ruleIndex))continue;const t=z.create(n,c.followState.stateNumber);try{o.set(c.target.ruleIndex),this._LOOK(c.target,e,t,i,r,o,a,l)}finally{o.clear(c.target.ruleIndex)}}else if(c instanceof L)a?this._LOOK(c.target,e,n,i,r,o,a,l):i.addOne(W.HIT_PRED);else if(c.isEpsilon)this._LOOK(c.target,e,n,i,r,o,a,l);else if(c.constructor===y)i.addRange(s.MIN_USER_TOKEN_TYPE,this.atn.maxTokenType);else{let t=c.label;null!==t&&(c instanceof I&&(t=t.complement(s.MIN_USER_TOKEN_TYPE,this.atn.maxTokenType)),i.addSet(t))}}}}}W.HIT_PRED=s.INVALID_TYPE;class j{constructor(t,e){this.grammarType=t,this.maxTokenType=e,this.states=[],this.decisionToState=[],this.ruleToStartState=[],this.ruleToStopState=null,this.modeNameToStartState={},this.ruleToTokenType=null,this.lexerActions=null,this.modeToStartState=[]}nextTokensInContext(t,e){return new W(this).LOOK(t,null,e)}nextTokensNoContext(t){return null!==t.nextTokenWithinRule||(t.nextTokenWithinRule=this.nextTokensInContext(t,null),t.nextTokenWithinRule.readOnly=!0),t.nextTokenWithinRule}nextTokens(t,e){return void 0===e?this.nextTokensNoContext(t):this.nextTokensInContext(t,e)}addState(t){null!==t&&(t.atn=this,t.stateNumber=this.states.length),this.states.push(t)}removeState(t){this.states[t.stateNumber]=null}defineDecisionState(t){return this.decisionToState.push(t),t.decision=this.decisionToState.length-1,t.decision}getDecisionState(t){return 0===this.decisionToState.length?null:this.decisionToState[t]}getExpectedTokens(t,e){if(t<0||t>=this.states.length)throw"Invalid state number.";const n=this.states[t];let i=this.nextTokens(n);if(!i.contains(s.EPSILON))return i;const r=new E;for(r.addSet(i),r.removeOne(s.EPSILON);null!==e&&e.invokingState>=0&&i.contains(s.EPSILON);){const t=this.states[e.invokingState].transitions[0];i=this.nextTokens(t.followState),r.addSet(i),r.removeOne(s.EPSILON),e=e.parentCtx}return i.contains(s.EPSILON)&&r.addOne(s.EOF),r}}j.INVALID_ALT_NUMBER=0;class $ extends _{constructor(){super(),this.stateType=_.BASIC}}class X extends _{constructor(){return super(),this.decision=-1,this.nonGreedy=!1,this}}class J extends X{constructor(){return super(),this.endState=null,this}}class Z extends _{constructor(){return super(),this.stateType=_.BLOCK_END,this.startState=null,this}}class Q extends _{constructor(){return super(),this.stateType=_.LOOP_END,this.loopBackState=null,this}}class tt extends _{constructor(){return super(),this.stateType=_.RULE_START,this.stopState=null,this.isPrecedenceRule=!1,this}}class et extends X{constructor(){return super(),this.stateType=_.TOKEN_START,this}}class nt extends X{constructor(){return super(),this.stateType=_.PLUS_LOOP_BACK,this}}class st extends _{constructor(){return super(),this.stateType=_.STAR_LOOP_BACK,this}}class it extends X{constructor(){return super(),this.stateType=_.STAR_LOOP_ENTRY,this.loopBackState=null,this.isPrecedenceDecision=null,this}}class rt extends J{constructor(){return super(),this.stateType=_.PLUS_BLOCK_START,this.loopBackState=null,this}}class ot extends J{constructor(){return super(),this.stateType=_.STAR_BLOCK_START,this}}class at extends J{constructor(){return super(),this.stateType=_.BLOCK_START,this}}class lt extends A{constructor(t,e){super(t),this.label_=e,this.label=this.makeLabel(),this.serializationType=A.ATOM}makeLabel(){const t=new E;return t.addOne(this.label_),t}matches(t,e,n){return this.label_===t}toString(){return this.label_}}class ht extends A{constructor(t,e,n){super(t),this.serializationType=A.RANGE,this.start=e,this.stop=n,this.label=this.makeLabel()}makeLabel(){const t=new E;return t.addRange(this.start,this.stop),t}matches(t,e,n){return t>=this.start&&t<=this.stop}toString(){return"'"+String.fromCharCode(this.start)+"'..'"+String.fromCharCode(this.stop)+"'"}}class ct extends A{constructor(t,e,n,s){super(t),this.serializationType=A.ACTION,this.ruleIndex=e,this.actionIndex=void 0===n?-1:n,this.isCtxDependent=void 0!==s&&s,this.isEpsilon=!0}matches(t,e,n){return!1}toString(){return"action_"+this.ruleIndex+":"+this.actionIndex}}class ut extends A{constructor(t,e){super(t),this.serializationType=A.EPSILON,this.isEpsilon=!0,this.outermostPrecedenceReturn=e}matches(t,e,n){return!1}toString(){return"epsilon"}}class dt extends g{constructor(t,e,n){super(),this.ruleIndex=void 0===t?-1:t,this.predIndex=void 0===e?-1:e,this.isCtxDependent=void 0!==n&&n}evaluate(t,e){const n=this.isCtxDependent?e:null;return t.sempred(n,this.ruleIndex,this.predIndex)}updateHashCode(t){t.update(this.ruleIndex,this.predIndex,this.isCtxDependent)}equals(t){return this===t||t instanceof dt&&this.ruleIndex===t.ruleIndex&&this.predIndex===t.predIndex&&this.isCtxDependent===t.isCtxDependent}toString(){return"{"+this.ruleIndex+":"+this.predIndex+"}?"}}g.NONE=new dt;class gt extends L{constructor(t,e,n,s){super(t),this.serializationType=A.PREDICATE,this.ruleIndex=e,this.predIndex=n,this.isCtxDependent=s,this.isEpsilon=!0}matches(t,e,n){return!1}getPredicate(){return new dt(this.ruleIndex,this.predIndex,this.isCtxDependent)}toString(){return"pred_"+this.ruleIndex+":"+this.predIndex}}class pt extends g{constructor(t){super(),this.precedence=void 0===t?0:t}evaluate(t,e){return t.precpred(e,this.precedence)}evalPrecedence(t,e){return t.precpred(e,this.precedence)?g.NONE:null}compareTo(t){return this.precedence-t.precedence}updateHashCode(t){t.update(this.precedence)}equals(t){return this===t||t instanceof pt&&this.precedence===t.precedence}toString(){return"{"+this.precedence+">=prec}?"}}g.PrecedencePredicate=pt;class ft extends L{constructor(t,e){super(t),this.serializationType=A.PRECEDENCE,this.precedence=e,this.isEpsilon=!0}matches(t,e,n){return!1}getPredicate(){return new pt(this.precedence)}toString(){return this.precedence+" >= _p"}}class xt{constructor(t){void 0===t&&(t=null),this.readOnly=!1,this.verifyATN=null===t||t.verifyATN,this.generateRuleBypassTransitions=null!==t&&t.generateRuleBypassTransitions}}xt.defaultOptions=new xt,xt.defaultOptions.readOnly=!0;class Tt{constructor(t){this.actionType=t,this.isPositionDependent=!1}hashCode(){const t=new a;return this.updateHashCode(t),t.finish()}updateHashCode(t){t.update(this.actionType)}equals(t){return this===t}}class St extends Tt{constructor(){super(6)}execute(t){t.skip()}toString(){return"skip"}}St.INSTANCE=new St;class mt extends Tt{constructor(t){super(0),this.channel=t}execute(t){t._channel=this.channel}updateHashCode(t){t.update(this.actionType,this.channel)}equals(t){return this===t||t instanceof mt&&this.channel===t.channel}toString(){return"channel("+this.channel+")"}}class Et extends Tt{constructor(t,e){super(1),this.ruleIndex=t,this.actionIndex=e,this.isPositionDependent=!0}execute(t){t.action(null,this.ruleIndex,this.actionIndex)}updateHashCode(t){t.update(this.actionType,this.ruleIndex,this.actionIndex)}equals(t){return this===t||t instanceof Et&&this.ruleIndex===t.ruleIndex&&this.actionIndex===t.actionIndex}}class _t extends Tt{constructor(){super(3)}execute(t){t.more()}toString(){return"more"}}_t.INSTANCE=new _t;class Ct extends Tt{constructor(t){super(7),this.type=t}execute(t){t.type=this.type}updateHashCode(t){t.update(this.actionType,this.type)}equals(t){return this===t||t instanceof Ct&&this.type===t.type}toString(){return"type("+this.type+")"}}class At extends Tt{constructor(t){super(5),this.mode=t}execute(t){t.pushMode(this.mode)}updateHashCode(t){t.update(this.actionType,this.mode)}equals(t){return this===t||t instanceof At&&this.mode===t.mode}toString(){return"pushMode("+this.mode+")"}}class Nt extends Tt{constructor(){super(4)}execute(t){t.popMode()}toString(){return"popMode"}}Nt.INSTANCE=new Nt;class kt extends Tt{constructor(t){super(2),this.mode=t}execute(t){t.setMode(this.mode)}updateHashCode(t){t.update(this.actionType,this.mode)}equals(t){return this===t||t instanceof kt&&this.mode===t.mode}toString(){return"mode("+this.mode+")"}}function It(t,e){const n=[];return n[t-1]=e,n.map((function(t){return e}))}class yt{constructor(t){null==t&&(t=xt.defaultOptions),this.deserializationOptions=t,this.stateFactories=null,this.actionFactories=null}deserialize(t){const e=this.reset(t);this.checkVersion(e),e&&this.skipUUID();const n=this.readATN();this.readStates(n,e),this.readRules(n,e),this.readModes(n);const s=[];return this.readSets(n,s,this.readInt.bind(this)),e&&this.readSets(n,s,this.readInt32.bind(this)),this.readEdges(n,s),this.readDecisions(n),this.readLexerActions(n,e),this.markPrecedenceDecisions(n),this.verifyATN(n),this.deserializationOptions.generateRuleBypassTransitions&&1===n.grammarType&&(this.generateRuleBypassTransitions(n),this.verifyATN(n)),n}reset(t){if(3===(t.charCodeAt?t.charCodeAt(0):t[0])){const e=function(t){const e=t.charCodeAt(0);return e>1?e-2:e+65534},n=t.split("").map(e);return n[0]=t.charCodeAt(0),this.data=n,this.pos=0,!0}return this.data=t,this.pos=0,!1}skipUUID(){let t=0;for(;t++<8;)this.readInt()}checkVersion(t){const e=this.readInt();if(!t&&4!==e)throw"Could not deserialize ATN with version "+e+" (expected 4)."}readATN(){const t=this.readInt(),e=this.readInt();return new j(t,e)}readStates(t,e){let n,s,i;const r=[],o=[],a=this.readInt();for(let n=0;n<a;n++){const n=this.readInt();if(n===_.INVALID_TYPE){t.addState(null);continue}let s=this.readInt();e&&65535===s&&(s=-1);const i=this.stateFactory(n,s);if(n===_.LOOP_END){const t=this.readInt();r.push([i,t])}else if(i instanceof J){const t=this.readInt();o.push([i,t])}t.addState(i)}for(n=0;n<r.length;n++)s=r[n],s[0].loopBackState=t.states[s[1]];for(n=0;n<o.length;n++)s=o[n],s[0].endState=t.states[s[1]];let l=this.readInt();for(n=0;n<l;n++)i=this.readInt(),t.states[i].nonGreedy=!0;let h=this.readInt();for(n=0;n<h;n++)i=this.readInt(),t.states[i].isPrecedenceRule=!0}readRules(t,e){let n;const i=this.readInt();for(0===t.grammarType&&(t.ruleToTokenType=It(i,0)),t.ruleToStartState=It(i,0),n=0;n<i;n++){const i=this.readInt();if(t.ruleToStartState[n]=t.states[i],0===t.grammarType){let i=this.readInt();e&&65535===i&&(i=s.EOF),t.ruleToTokenType[n]=i}}for(t.ruleToStopState=It(i,0),n=0;n<t.states.length;n++){const e=t.states[n];e instanceof C&&(t.ruleToStopState[e.ruleIndex]=e,t.ruleToStartState[e.ruleIndex].stopState=e)}}readModes(t){const e=this.readInt();for(let n=0;n<e;n++){let e=this.readInt();t.modeToStartState.push(t.states[e])}}readSets(t,e,n){const s=this.readInt();for(let t=0;t<s;t++){const t=new E;e.push(t);const s=this.readInt();0!==this.readInt()&&t.addOne(-1);for(let e=0;e<s;e++){const e=n(),s=n();t.addRange(e,s)}}}readEdges(t,e){let n,s,i,r,o;const a=this.readInt();for(n=0;n<a;n++){const n=this.readInt(),s=this.readInt(),i=this.readInt(),o=this.readInt(),a=this.readInt(),l=this.readInt();r=this.edgeFactory(t,i,n,s,o,a,l,e),t.states[n].addTransition(r)}for(n=0;n<t.states.length;n++)for(i=t.states[n],s=0;s<i.transitions.length;s++){const e=i.transitions[s];if(!(e instanceof N))continue;let n=-1;t.ruleToStartState[e.target.ruleIndex].isPrecedenceRule&&0===e.precedence&&(n=e.target.ruleIndex),r=new ut(e.followState,n),t.ruleToStopState[e.target.ruleIndex].addTransition(r)}for(n=0;n<t.states.length;n++){if(i=t.states[n],i instanceof J){if(null===i.endState)throw"IllegalState";if(null!==i.endState.startState)throw"IllegalState";i.endState.startState=i}if(i instanceof nt)for(s=0;s<i.transitions.length;s++)o=i.transitions[s].target,o instanceof rt&&(o.loopBackState=i);else if(i instanceof st)for(s=0;s<i.transitions.length;s++)o=i.transitions[s].target,o instanceof it&&(o.loopBackState=i)}}readDecisions(t){const e=this.readInt();for(let n=0;n<e;n++){const e=this.readInt(),s=t.states[e];t.decisionToState.push(s),s.decision=n}}readLexerActions(t,e){if(0===t.grammarType){const n=this.readInt();t.lexerActions=It(n,null);for(let s=0;s<n;s++){const n=this.readInt();let i=this.readInt();e&&65535===i&&(i=-1);let r=this.readInt();e&&65535===r&&(r=-1),t.lexerActions[s]=this.lexerActionFactory(n,i,r)}}}generateRuleBypassTransitions(t){let e;const n=t.ruleToStartState.length;for(e=0;e<n;e++)t.ruleToTokenType[e]=t.maxTokenType+e+1;for(e=0;e<n;e++)this.generateRuleBypassTransition(t,e)}generateRuleBypassTransition(t,e){let n,s;const i=new at;i.ruleIndex=e,t.addState(i);const r=new Z;r.ruleIndex=e,t.addState(r),i.endState=r,t.defineDecisionState(i),r.startState=i;let o=null,a=null;if(t.ruleToStartState[e].isPrecedenceRule){for(a=null,n=0;n<t.states.length;n++)if(s=t.states[n],this.stateIsEndStateFor(s,e)){a=s,o=s.loopBackState.transitions[0];break}if(null===o)throw"Couldn't identify final state of the precedence rule prefix section."}else a=t.ruleToStopState[e];for(n=0;n<t.states.length;n++){s=t.states[n];for(let t=0;t<s.transitions.length;t++){const e=s.transitions[t];e!==o&&e.target===a&&(e.target=r)}}const l=t.ruleToStartState[e],h=l.transitions.length;for(;h>0;)i.addTransition(l.transitions[h-1]),l.transitions=l.transitions.slice(-1);t.ruleToStartState[e].addTransition(new ut(i)),r.addTransition(new ut(a));const c=new $;t.addState(c),c.addTransition(new lt(r,t.ruleToTokenType[e])),i.addTransition(new ut(c))}stateIsEndStateFor(t,e){if(t.ruleIndex!==e)return null;if(!(t instanceof it))return null;const n=t.transitions[t.transitions.length-1].target;return n instanceof Q&&n.epsilonOnlyTransitions&&n.transitions[0].target instanceof C?t:null}markPrecedenceDecisions(t){for(let e=0;e<t.states.length;e++){const n=t.states[e];if(n instanceof it&&t.ruleToStartState[n.ruleIndex].isPrecedenceRule){const t=n.transitions[n.transitions.length-1].target;t instanceof Q&&t.epsilonOnlyTransitions&&t.transitions[0].target instanceof C&&(n.isPrecedenceDecision=!0)}}}verifyATN(t){if(this.deserializationOptions.verifyATN)for(let e=0;e<t.states.length;e++){const n=t.states[e];if(null!==n)if(this.checkCondition(n.epsilonOnlyTransitions||n.transitions.length<=1),n instanceof rt)this.checkCondition(null!==n.loopBackState);else if(n instanceof it)if(this.checkCondition(null!==n.loopBackState),this.checkCondition(2===n.transitions.length),n.transitions[0].target instanceof ot)this.checkCondition(n.transitions[1].target instanceof Q),this.checkCondition(!n.nonGreedy);else{if(!(n.transitions[0].target instanceof Q))throw"IllegalState";this.checkCondition(n.transitions[1].target instanceof ot),this.checkCondition(n.nonGreedy)}else n instanceof st?(this.checkCondition(1===n.transitions.length),this.checkCondition(n.transitions[0].target instanceof it)):n instanceof Q?this.checkCondition(null!==n.loopBackState):n instanceof tt?this.checkCondition(null!==n.stopState):n instanceof J?this.checkCondition(null!==n.endState):n instanceof Z?this.checkCondition(null!==n.startState):n instanceof X?this.checkCondition(n.transitions.length<=1||n.decision>=0):this.checkCondition(n.transitions.length<=1||n instanceof C)}}checkCondition(t,e){if(!t)throw null==e&&(e="IllegalState"),e}readInt(){return this.data[this.pos++]}readInt32(){return this.readInt()|this.readInt()<<16}edgeFactory(t,e,n,i,r,o,a,l){const h=t.states[i];switch(e){case A.EPSILON:return new ut(h);case A.RANGE:return new ht(h,0!==a?s.EOF:r,o);case A.RULE:return new N(t.states[r],o,a,h);case A.PREDICATE:return new gt(h,r,o,0!==a);case A.PRECEDENCE:return new ft(h,r);case A.ATOM:return new lt(h,0!==a?s.EOF:r);case A.ACTION:return new ct(h,r,o,0!==a);case A.SET:return new k(h,l[r]);case A.NOT_SET:return new I(h,l[r]);case A.WILDCARD:return new y(h);default:throw"The specified transition type: "+e+" is not valid."}}stateFactory(t,e){if(null===this.stateFactories){const t=[];t[_.INVALID_TYPE]=null,t[_.BASIC]=()=>new $,t[_.RULE_START]=()=>new tt,t[_.BLOCK_START]=()=>new at,t[_.PLUS_BLOCK_START]=()=>new rt,t[_.STAR_BLOCK_START]=()=>new ot,t[_.TOKEN_START]=()=>new et,t[_.RULE_STOP]=()=>new C,t[_.BLOCK_END]=()=>new Z,t[_.STAR_LOOP_BACK]=()=>new st,t[_.STAR_LOOP_ENTRY]=()=>new it,t[_.PLUS_LOOP_BACK]=()=>new nt,t[_.LOOP_END]=()=>new Q,this.stateFactories=t}if(t>this.stateFactories.length||null===this.stateFactories[t])throw"The specified state type "+t+" is not valid.";{const n=this.stateFactories[t]();if(null!==n)return n.ruleIndex=e,n}}lexerActionFactory(t,e,n){if(null===this.actionFactories){const t=[];t[0]=(t,e)=>new mt(t),t[1]=(t,e)=>new Et(t,e),t[2]=(t,e)=>new kt(t),t[3]=(t,e)=>_t.INSTANCE,t[4]=(t,e)=>Nt.INSTANCE,t[5]=(t,e)=>new At(t),t[6]=(t,e)=>St.INSTANCE,t[7]=(t,e)=>new Ct(t),this.actionFactories=t}if(t>this.actionFactories.length||null===this.actionFactories[t])throw"The specified lexer action type "+t+" is not valid.";return this.actionFactories[t](e,n)}}class Lt{syntaxError(t,e,n,s,i,r){}reportAmbiguity(t,e,n,s,i,r,o){}reportAttemptingFullContext(t,e,n,s,i,r){}reportContextSensitivity(t,e,n,s,i,r){}}class Ot extends Lt{constructor(){super()}syntaxError(t,e,n,s,i,r){console.error("line "+n+":"+s+" "+i)}}Ot.INSTANCE=new Ot;class Rt extends Lt{constructor(t){if(super(),null===t)throw"delegates";return this.delegates=t,this}syntaxError(t,e,n,s,i,r){this.delegates.map((o=>o.syntaxError(t,e,n,s,i,r)))}reportAmbiguity(t,e,n,s,i,r,o){this.delegates.map((a=>a.reportAmbiguity(t,e,n,s,i,r,o)))}reportAttemptingFullContext(t,e,n,s,i,r){this.delegates.map((o=>o.reportAttemptingFullContext(t,e,n,s,i,r)))}reportContextSensitivity(t,e,n,s,i,r){this.delegates.map((o=>o.reportContextSensitivity(t,e,n,s,i,r)))}}class wt{constructor(){this._listeners=[Ot.INSTANCE],this._interp=null,this._stateNumber=-1}checkVersion(t){const e="4.13.2";e!==t&&console.log("ANTLR runtime and generated code versions disagree: "+e+"!="+t)}addErrorListener(t){this._listeners.push(t)}removeErrorListeners(){this._listeners=[]}getLiteralNames(){return Object.getPrototypeOf(this).constructor.literalNames||[]}getSymbolicNames(){return Object.getPrototypeOf(this).constructor.symbolicNames||[]}getTokenNames(){if(!this.tokenNames){const t=this.getLiteralNames(),e=this.getSymbolicNames(),n=t.length>e.length?t.length:e.length;this.tokenNames=[];for(let s=0;s<n;s++)this.tokenNames[s]=t[s]||e[s]||"<INVALID"}return this.tokenNames}getTokenTypeMap(){const t=this.getTokenNames();if(null===t)throw"The current recognizer does not provide a list of token names.";let e=this.tokenTypeMapCache[t];return void 0===e&&(e=t.reduce((function(t,e,n){t[e]=n})),e.EOF=s.EOF,this.tokenTypeMapCache[t]=e),e}getRuleIndexMap(){const t=this.ruleNames;if(null===t)throw"The current recognizer does not provide a list of rule names.";let e=this.ruleIndexMapCache[t];return void 0===e&&(e=t.reduce((function(t,e,n){t[e]=n})),this.ruleIndexMapCache[t]=e),e}getTokenType(t){const e=this.getTokenTypeMap()[t];return void 0!==e?e:s.INVALID_TYPE}getErrorHeader(t){return"line "+t.getOffendingToken().line+":"+t.getOffendingToken().column}getTokenErrorDisplay(t){if(null===t)return"<no token>";let e=t.text;return null===e&&(e=t.type===s.EOF?"<EOF>":"<"+t.type+">"),e=e.replace("\n","\\n").replace("\r","\\r").replace("\t","\\t"),"'"+e+"'"}getErrorListenerDispatch(){return console.warn("Calling deprecated method in Recognizer class: getErrorListenerDispatch()"),this.getErrorListener()}getErrorListener(){return new Rt(this._listeners)}sempred(t,e,n){return!0}precpred(t,e){return!0}get atn(){return this._interp.atn}get state(){return this._stateNumber}set state(t){this._stateNumber=t}}wt.tokenTypeMapCache={},wt.ruleIndexMapCache={};class vt extends s{constructor(t,e,n,i,r){super(),this.source=void 0!==t?t:vt.EMPTY_SOURCE,this.type=void 0!==e?e:null,this.channel=void 0!==n?n:s.DEFAULT_CHANNEL,this.start=void 0!==i?i:-1,this.stop=void 0!==r?r:-1,this.tokenIndex=-1,null!==this.source[0]?(this.line=t[0].line,this.column=t[0].column):this.column=-1}clone(){const t=new vt(this.source,this.type,this.channel,this.start,this.stop);return t.tokenIndex=this.tokenIndex,t.line=this.line,t.column=this.column,t.text=this.text,t}cloneWithType(t){const e=new vt(this.source,t,this.channel,this.start,this.stop);return e.tokenIndex=this.tokenIndex,e.line=this.line,e.column=this.column,t===s.EOF&&(e.text=""),e}toString(){let t=this.text;return t=null!==t?t.replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/\t/g,"\\t"):"<no text>","[@"+this.tokenIndex+","+this.start+":"+this.stop+"='"+t+"',<"+this.type+">"+(this.channel>0?",channel="+this.channel:"")+","+this.line+":"+this.column+"]"}get text(){if(null!==this._text)return this._text;const t=this.getInputStream();if(null===t)return null;const e=t.size;return this.start<e&&this.stop<e?t.getText(this.start,this.stop):"<EOF>"}set text(t){this._text=t}}vt.EMPTY_SOURCE=[null,null];class Pt{}class bt extends Pt{constructor(t){super(),this.copyText=void 0!==t&&t}create(t,e,n,s,i,r,o,a){const l=new vt(t,e,s,i,r);return l.line=o,l.column=a,null!==n?l.text=n:this.copyText&&null!==t[1]&&(l.text=t[1].getText(i,r)),l}createThin(t,e){const n=new vt(null,t);return n.text=e,n}}bt.DEFAULT=new bt;class Dt extends Error{constructor(t){super(t.message),Error.captureStackTrace&&Error.captureStackTrace(this,Dt),this.message=t.message,this.recognizer=t.recognizer,this.input=t.input,this.ctx=t.ctx,this.offendingToken=null,this.offendingState=-1,null!==this.recognizer&&(this.offendingState=this.recognizer.state)}getExpectedTokens(){return null!==this.recognizer?this.recognizer.atn.getExpectedTokens(this.offendingState,this.ctx):null}toString(){return this.message}}class Ft extends Dt{constructor(t,e,n,s){super({message:"",recognizer:t,input:e,ctx:null}),this.startIndex=n,this.deadEndConfigs=s}toString(){let t="";return this.startIndex>=0&&this.startIndex<this.input.size&&(t=this.input.getText(new m(this.startIndex,this.startIndex))),"LexerNoViableAltException"+t}}class Mt extends wt{constructor(t){super(),this._input=t,this._factory=bt.DEFAULT,this._tokenFactorySourcePair=[this,t],this._interp=null,this._token=null,this._tokenStartCharIndex=-1,this._tokenStartLine=-1,this._tokenStartColumn=-1,this._hitEOF=!1,this._channel=s.DEFAULT_CHANNEL,this._type=s.INVALID_TYPE,this._modeStack=[],this._mode=Mt.DEFAULT_MODE,this._text=null}reset(){null!==this._input&&this._input.seek(0),this._token=null,this._type=s.INVALID_TYPE,this._channel=s.DEFAULT_CHANNEL,this._tokenStartCharIndex=-1,this._tokenStartColumn=-1,this._tokenStartLine=-1,this._text=null,this._hitEOF=!1,this._mode=Mt.DEFAULT_MODE,this._modeStack=[],this._interp.reset()}nextToken(){if(null===this._input)throw"nextToken requires a non-null input stream.";const t=this._input.mark();try{for(;;){if(this._hitEOF)return this.emitEOF(),this._token;this._token=null,this._channel=s.DEFAULT_CHANNEL,this._tokenStartCharIndex=this._input.index,this._tokenStartColumn=this._interp.column,this._tokenStartLine=this._interp.line,this._text=null;let t=!1;for(;;){this._type=s.INVALID_TYPE;let e=Mt.SKIP;try{e=this._interp.match(this._input,this._mode)}catch(t){if(!(t instanceof Dt))throw console.log(t.stack),t;this.notifyListeners(t),this.recover(t)}if(this._input.LA(1)===s.EOF&&(this._hitEOF=!0),this._type===s.INVALID_TYPE&&(this._type=e),this._type===Mt.SKIP){t=!0;break}if(this._type!==Mt.MORE)break}if(!t)return null===this._token&&this.emit(),this._token}}finally{this._input.release(t)}}skip(){this._type=Mt.SKIP}more(){this._type=Mt.MORE}mode(t){console.warn("Calling deprecated method in Lexer class: mode(...)"),this.setMode(t)}setMode(t){this._mode=t}getMode(){return this._mode}getModeStack(){return this._modeStack}pushMode(t){this._interp.debug&&console.log("pushMode "+t),this._modeStack.push(this._mode),this.setMode(t)}popMode(){if(0===this._modeStack.length)throw"Empty Stack";return this._interp.debug&&console.log("popMode back to "+this._modeStack.slice(0,-1)),this.setMode(this._modeStack.pop()),this._mode}emitToken(t){this._token=t}emit(){const t=this._factory.create(this._tokenFactorySourcePair,this._type,this._text,this._channel,this._tokenStartCharIndex,this.getCharIndex()-1,this._tokenStartLine,this._tokenStartColumn);return this.emitToken(t),t}emitEOF(){const t=this.column,e=this.line,n=this._factory.create(this._tokenFactorySourcePair,s.EOF,null,s.DEFAULT_CHANNEL,this._input.index,this._input.index-1,e,t);return this.emitToken(n),n}getCharIndex(){return this._input.index}getAllTokens(){const t=[];let e=this.nextToken();for(;e.type!==s.EOF;)t.push(e),e=this.nextToken();return t}notifyListeners(t){const e=this._tokenStartCharIndex,n=this._input.index,s=this._input.getText(e,n),i="token recognition error at: '"+this.getErrorDisplay(s)+"'";this.getErrorListener().syntaxError(this,null,this._tokenStartLine,this._tokenStartColumn,i,t)}getErrorDisplay(t){const e=[];for(let n=0;n<t.length;n++)e.push(t[n]);return e.join("")}getErrorDisplayForChar(t){return t.charCodeAt(0)===s.EOF?"<EOF>":"\n"===t?"\\n":"\t"===t?"\\t":"\r"===t?"\\r":t}getCharErrorDisplay(t){return"'"+this.getErrorDisplayForChar(t)+"'"}recover(t){this._input.LA(1)!==s.EOF&&(t instanceof Ft?this._interp.consume(this._input):this._input.consume())}get inputStream(){return this._input}set inputStream(t){this._input=null,this._tokenFactorySourcePair=[this,this._input],this.reset(),this._input=t,this._tokenFactorySourcePair=[this,this._input]}get sourceName(){return this._input.sourceName}get type(){return this._type}set type(t){this._type=t}get line(){return this._interp.line}set line(t){this._interp.line=t}get column(){return this._interp.column}set column(t){this._interp.column=t}get text(){return null!==this._text?this._text:this._interp.getText(this._input)}set text(t){this._text=t}}function Ut(t){return t.hashCodeForConfigSet()}function Bt(t,e){return t===e||null!==t&&null!==e&&t.equalsForConfigSet(e)}Mt.DEFAULT_MODE=0,Mt.MORE=-2,Mt.SKIP=-3,Mt.DEFAULT_TOKEN_CHANNEL=s.DEFAULT_CHANNEL,Mt.HIDDEN=s.HIDDEN_CHANNEL,Mt.MIN_CHAR_VALUE=0,Mt.MAX_CHAR_VALUE=1114111;class zt{constructor(t){this.configLookup=new d(Ut,Bt),this.fullCtx=void 0===t||t,this.readOnly=!1,this.configs=[],this.uniqueAlt=0,this.conflictingAlts=null,this.hasSemanticContext=!1,this.dipsIntoOuterContext=!1,this.cachedHashCode=-1}add(t,e){if(void 0===e&&(e=null),this.readOnly)throw"This set is readonly";t.semanticContext!==g.NONE&&(this.hasSemanticContext=!0),t.reachesIntoOuterContext>0&&(this.dipsIntoOuterContext=!0);const n=this.configLookup.getOrAdd(t);if(n===t)return this.cachedHashCode=-1,this.configs.push(t),!0;const s=!this.fullCtx,i=Y(n.context,t.context,s,e);return n.reachesIntoOuterContext=Math.max(n.reachesIntoOuterContext,t.reachesIntoOuterContext),t.precedenceFilterSuppressed&&(n.precedenceFilterSuppressed=!0),n.context=i,!0}getStates(){const t=new d;for(let e=0;e<this.configs.length;e++)t.add(this.configs[e].state);return t}getPredicates(){const t=[];for(let e=0;e<this.configs.length;e++){const n=this.configs[e].semanticContext;n!==g.NONE&&t.push(n.semanticContext)}return t}optimizeConfigs(t){if(this.readOnly)throw"This set is readonly";if(0!==this.configLookup.length)for(let e=0;e<this.configs.length;e++){const n=this.configs[e];n.context=t.getCachedContext(n.context)}}addAll(t){for(let e=0;e<t.length;e++)this.add(t[e]);return!1}equals(t){return this===t||t instanceof zt&&i(this.configs,t.configs)&&this.fullCtx===t.fullCtx&&this.uniqueAlt===t.uniqueAlt&&this.conflictingAlts===t.conflictingAlts&&this.hasSemanticContext===t.hasSemanticContext&&this.dipsIntoOuterContext===t.dipsIntoOuterContext}hashCode(){const t=new a;return t.update(this.configs),t.finish()}updateHashCode(t){this.readOnly?(-1===this.cachedHashCode&&(this.cachedHashCode=this.hashCode()),t.update(this.cachedHashCode)):t.update(this.hashCode())}isEmpty(){return 0===this.configs.length}contains(t){if(null===this.configLookup)throw"This method is not implemented for readonly sets.";return this.configLookup.contains(t)}containsFast(t){if(null===this.configLookup)throw"This method is not implemented for readonly sets.";return this.configLookup.containsFast(t)}clear(){if(this.readOnly)throw"This set is readonly";this.configs=[],this.cachedHashCode=-1,this.configLookup=new d}setReadonly(t){this.readOnly=t,t&&(this.configLookup=null)}toString(){return u(this.configs)+(this.hasSemanticContext?",hasSemanticContext="+this.hasSemanticContext:"")+(this.uniqueAlt!==j.INVALID_ALT_NUMBER?",uniqueAlt="+this.uniqueAlt:"")+(null!==this.conflictingAlts?",conflictingAlts="+this.conflictingAlts:"")+(this.dipsIntoOuterContext?",dipsIntoOuterContext":"")}get items(){return this.configs}get length(){return this.configs.length}}class Vt{constructor(t,e){return null===t&&(t=-1),null===e&&(e=new zt),this.stateNumber=t,this.configs=e,this.edges=null,this.isAcceptState=!1,this.prediction=0,this.lexerActionExecutor=null,this.requiresFullContext=!1,this.predicates=null,this}getAltSet(){const t=new d;if(null!==this.configs)for(let e=0;e<this.configs.length;e++){const n=this.configs[e];t.add(n.alt)}return 0===t.length?null:t}equals(t){return this===t||t instanceof Vt&&this.configs.equals(t.configs)}toString(){let t=this.stateNumber+":"+this.configs;return this.isAcceptState&&(t+="=>",null!==this.predicates?t+=this.predicates:t+=this.prediction),t}hashCode(){const t=new a;return t.update(this.configs),t.finish()}}class qt{constructor(t,e){return this.atn=t,this.sharedContextCache=e,this}getCachedContext(t){if(null===this.sharedContextCache)return t;const e=new q;return K(t,this.sharedContextCache,e)}}qt.ERROR=new Vt(2147483647,new zt);class Ht extends zt{constructor(){super(),this.configLookup=new d}}class Kt extends S{constructor(t,e){super(t,e);const n=t.lexerActionExecutor||null;return this.lexerActionExecutor=n||(null!==e?e.lexerActionExecutor:null),this.passedThroughNonGreedyDecision=null!==e&&this.checkNonGreedyDecision(e,this.state),this.hashCodeForConfigSet=Kt.prototype.hashCode,this.equalsForConfigSet=Kt.prototype.equals,this}updateHashCode(t){t.update(this.state.stateNumber,this.alt,this.context,this.semanticContext,this.passedThroughNonGreedyDecision,this.lexerActionExecutor)}equals(t){return this===t||t instanceof Kt&&this.passedThroughNonGreedyDecision===t.passedThroughNonGreedyDecision&&(this.lexerActionExecutor?this.lexerActionExecutor.equals(t.lexerActionExecutor):!t.lexerActionExecutor)&&super.equals(t)}checkNonGreedyDecision(t,e){return t.passedThroughNonGreedyDecision||e instanceof X&&e.nonGreedy}}class Yt extends Tt{constructor(t,e){super(e.actionType),this.offset=t,this.action=e,this.isPositionDependent=!0}execute(t){this.action.execute(t)}updateHashCode(t){t.update(this.actionType,this.offset,this.action)}equals(t){return this===t||t instanceof Yt&&this.offset===t.offset&&this.action===t.action}}class Gt{constructor(t){return this.lexerActions=null===t?[]:t,this.cachedHashCode=a.hashStuff(t),this}fixOffsetBeforeMatch(t){let e=null;for(let n=0;n<this.lexerActions.length;n++)!this.lexerActions[n].isPositionDependent||this.lexerActions[n]instanceof Yt||(null===e&&(e=this.lexerActions.concat([])),e[n]=new Yt(t,this.lexerActions[n]));return null===e?this:new Gt(e)}execute(t,e,n){let s=!1;const i=e.index;try{for(let r=0;r<this.lexerActions.length;r++){let o=this.lexerActions[r];if(o instanceof Yt){const t=o.offset;e.seek(n+t),o=o.action,s=n+t!==i}else o.isPositionDependent&&(e.seek(i),s=!1);o.execute(t)}}finally{s&&e.seek(i)}}hashCode(){return this.cachedHashCode}updateHashCode(t){t.update(this.cachedHashCode)}equals(t){if(this===t)return!0;if(t instanceof Gt){if(this.cachedHashCode!=t.cachedHashCode)return!1;if(this.lexerActions.length!=t.lexerActions.length)return!1;{const e=this.lexerActions.length;for(let n=0;n<e;++n)if(!this.lexerActions[n].equals(t.lexerActions[n]))return!1;return!0}}return!1}static append(t,e){if(null===t)return new Gt([e]);const n=t.lexerActions.concat([e]);return new Gt(n)}}function Wt(t){t.index=-1,t.line=0,t.column=-1,t.dfaState=null}class jt{constructor(){Wt(this)}reset(){Wt(this)}}class $t extends qt{constructor(t,e,n,s){super(e,s),this.decisionToDFA=n,this.recog=t,this.startIndex=-1,this.line=1,this.column=0,this.mode=Mt.DEFAULT_MODE,this.prevAccept=new jt}copyState(t){this.column=t.column,this.line=t.line,this.mode=t.mode,this.startIndex=t.startIndex}match(t,e){this.mode=e;const n=t.mark();try{this.startIndex=t.index,this.prevAccept.reset();const n=this.decisionToDFA[e];return null===n.s0?this.matchATN(t):this.execATN(t,n.s0)}finally{t.release(n)}}reset(){this.prevAccept.reset(),this.startIndex=-1,this.line=1,this.column=0,this.mode=Mt.DEFAULT_MODE}matchATN(t){const e=this.atn.modeToStartState[this.mode];$t.debug&&console.log("matchATN mode "+this.mode+" start: "+e);const n=this.mode,s=this.computeStartState(t,e),i=s.hasSemanticContext;s.hasSemanticContext=!1;const r=this.addDFAState(s);i||(this.decisionToDFA[this.mode].s0=r);const o=this.execATN(t,r);return $t.debug&&console.log("DFA after matchATN: "+this.decisionToDFA[n].toLexerString()),o}execATN(t,e){$t.debug&&console.log("start state closure="+e.configs),e.isAcceptState&&this.captureSimState(this.prevAccept,t,e);let n=t.LA(1),i=e;for(;;){$t.debug&&console.log("execATN loop starting closure: "+i.configs);let e=this.getExistingTargetState(i,n);if(null===e&&(e=this.computeTargetState(t,i,n)),e===qt.ERROR)break;if(n!==s.EOF&&this.consume(t),e.isAcceptState&&(this.captureSimState(this.prevAccept,t,e),n===s.EOF))break;n=t.LA(1),i=e}return this.failOrAccept(this.prevAccept,t,i.configs,n)}getExistingTargetState(t,e){if(null===t.edges||e<$t.MIN_DFA_EDGE||e>$t.MAX_DFA_EDGE)return null;let n=t.edges[e-$t.MIN_DFA_EDGE];return void 0===n&&(n=null),$t.debug&&null!==n&&console.log("reuse state "+t.stateNumber+" edge to "+n.stateNumber),n}computeTargetState(t,e,n){const s=new Ht;return this.getReachableConfigSet(t,e.configs,s,n),0===s.items.length?(s.hasSemanticContext||this.addDFAEdge(e,n,qt.ERROR),qt.ERROR):this.addDFAEdge(e,n,null,s)}failOrAccept(t,e,n,i){if(null!==this.prevAccept.dfaState){const n=t.dfaState.lexerActionExecutor;return this.accept(e,n,this.startIndex,t.index,t.line,t.column),t.dfaState.prediction}if(i===s.EOF&&e.index===this.startIndex)return s.EOF;throw new Ft(this.recog,e,this.startIndex,n)}getReachableConfigSet(t,e,n,i){let r=j.INVALID_ALT_NUMBER;for(let o=0;o<e.items.length;o++){const a=e.items[o],l=a.alt===r;if(!l||!a.passedThroughNonGreedyDecision){$t.debug&&console.log("testing %s at %s\n",this.getTokenName(i),a.toString(this.recog,!0));for(let e=0;e<a.state.transitions.length;e++){const o=a.state.transitions[e],h=this.getReachableTarget(o,i);if(null!==h){let e=a.lexerActionExecutor;null!==e&&(e=e.fixOffsetBeforeMatch(t.index-this.startIndex));const o=i===s.EOF,c=new Kt({state:h,lexerActionExecutor:e},a);this.closure(t,c,n,l,!0,o)&&(r=a.alt)}}}}}accept(t,e,n,s,i,r){$t.debug&&console.log("ACTION %s\n",e),t.seek(s),this.line=i,this.column=r,null!==e&&null!==this.recog&&e.execute(this.recog,t,n)}getReachableTarget(t,e){return t.matches(e,0,Mt.MAX_CHAR_VALUE)?t.target:null}computeStartState(t,e){const n=U.EMPTY,s=new Ht;for(let i=0;i<e.transitions.length;i++){const r=e.transitions[i].target,o=new Kt({state:r,alt:i+1,context:n},null);this.closure(t,o,s,!1,!1,!1)}return s}closure(t,e,n,s,i,r){let o=null;if($t.debug&&console.log("closure("+e.toString(this.recog,!0)+")"),e.state instanceof C){if($t.debug&&(null!==this.recog?console.log("closure at %s rule stop %s\n",this.recog.ruleNames[e.state.ruleIndex],e):console.log("closure at rule stop %s\n",e)),null===e.context||e.context.hasEmptyPath()){if(null===e.context||e.context.isEmpty())return n.add(e),!0;n.add(new Kt({state:e.state,context:U.EMPTY},e)),s=!0}if(null!==e.context&&!e.context.isEmpty())for(let a=0;a<e.context.length;a++)if(e.context.getReturnState(a)!==U.EMPTY_RETURN_STATE){const l=e.context.getParent(a),h=this.atn.states[e.context.getReturnState(a)];o=new Kt({state:h,context:l},e),s=this.closure(t,o,n,s,i,r)}return s}e.state.epsilonOnlyTransitions||s&&e.passedThroughNonGreedyDecision||n.add(e);for(let a=0;a<e.state.transitions.length;a++){const l=e.state.transitions[a];o=this.getEpsilonTarget(t,e,l,n,i,r),null!==o&&(s=this.closure(t,o,n,s,i,r))}return s}getEpsilonTarget(t,e,n,i,r,o){let a=null;if(n.serializationType===A.RULE){const t=z.create(e.context,n.followState.stateNumber);a=new Kt({state:n.target,context:t},e)}else{if(n.serializationType===A.PRECEDENCE)throw"Precedence predicates are not supported in lexers.";if(n.serializationType===A.PREDICATE)$t.debug&&console.log("EVAL rule "+n.ruleIndex+":"+n.predIndex),i.hasSemanticContext=!0,this.evaluatePredicate(t,n.ruleIndex,n.predIndex,r)&&(a=new Kt({state:n.target},e));else if(n.serializationType===A.ACTION)if(null===e.context||e.context.hasEmptyPath()){const t=Gt.append(e.lexerActionExecutor,this.atn.lexerActions[n.actionIndex]);a=new Kt({state:n.target,lexerActionExecutor:t},e)}else a=new Kt({state:n.target},e);else n.serializationType===A.EPSILON?a=new Kt({state:n.target},e):n.serializationType!==A.ATOM&&n.serializationType!==A.RANGE&&n.serializationType!==A.SET||o&&n.matches(s.EOF,0,Mt.MAX_CHAR_VALUE)&&(a=new Kt({state:n.target},e))}return a}evaluatePredicate(t,e,n,s){if(null===this.recog)return!0;if(!s)return this.recog.sempred(null,e,n);const i=this.column,r=this.line,o=t.index,a=t.mark();try{return this.consume(t),this.recog.sempred(null,e,n)}finally{this.column=i,this.line=r,t.seek(o),t.release(a)}}captureSimState(t,e,n){t.index=e.index,t.line=this.line,t.column=this.column,t.dfaState=n}addDFAEdge(t,e,n,s){if(void 0===n&&(n=null),void 0===s&&(s=null),null===n&&null!==s){const t=s.hasSemanticContext;if(s.hasSemanticContext=!1,n=this.addDFAState(s),t)return n}return e<$t.MIN_DFA_EDGE||e>$t.MAX_DFA_EDGE||($t.debug&&console.log("EDGE "+t+" -> "+n+" upon "+e),null===t.edges&&(t.edges=[]),t.edges[e-$t.MIN_DFA_EDGE]=n),n}addDFAState(t){const e=new Vt(null,t);let n=null;for(let e=0;e<t.items.length;e++){const s=t.items[e];if(s.state instanceof C){n=s;break}}null!==n&&(e.isAcceptState=!0,e.lexerActionExecutor=n.lexerActionExecutor,e.prediction=this.atn.ruleToTokenType[n.state.ruleIndex]);const s=this.decisionToDFA[this.mode],i=s.states.get(e);if(null!==i)return i;const r=e;return r.stateNumber=s.states.length,t.setReadonly(!0),r.configs=t,s.states.add(r),r}getDFA(t){return this.decisionToDFA[t]}getText(t){return t.getText(this.startIndex,t.index-1)}consume(t){t.LA(1)==="\n".charCodeAt(0)?(this.line+=1,this.column=0):this.column+=1,t.consume()}getTokenName(t){return-1===t?"EOF":"'"+String.fromCharCode(t)+"'"}}$t.debug=!1,$t.dfa_debug=!1,$t.MIN_DFA_EDGE=0,$t.MAX_DFA_EDGE=127;class Xt{constructor(t,e){this.alt=e,this.pred=t}toString(){return"("+this.pred+", "+this.alt+")"}}class Jt{constructor(){this.data={}}get(t){return this.data["k-"+t]||null}set(t,e){this.data["k-"+t]=e}values(){return Object.keys(this.data).filter((t=>t.startsWith("k-"))).map((t=>this.data[t]),this)}}const Zt={SLL:0,LL:1,LL_EXACT_AMBIG_DETECTION:2,hasSLLConflictTerminatingPrediction:function(t,e){if(Zt.allConfigsInRuleStopStates(e))return!0;if(t===Zt.SLL&&e.hasSemanticContext){const t=new zt;for(let n=0;n<e.items.length;n++){let s=e.items[n];s=new S({semanticContext:g.NONE},s),t.add(s)}e=t}const n=Zt.getConflictingAltSubsets(e);return Zt.hasConflictingAltSet(n)&&!Zt.hasStateAssociatedWithOneAlt(e)},hasConfigInRuleStopState:function(t){for(let e=0;e<t.items.length;e++)if(t.items[e].state instanceof C)return!0;return!1},allConfigsInRuleStopStates:function(t){for(let e=0;e<t.items.length;e++)if(!(t.items[e].state instanceof C))return!1;return!0},resolvesToJustOneViableAlt:function(t){return Zt.getSingleViableAlt(t)},allSubsetsConflict:function(t){return!Zt.hasNonConflictingAltSet(t)},hasNonConflictingAltSet:function(t){for(let e=0;e<t.length;e++)if(1===t[e].length)return!0;return!1},hasConflictingAltSet:function(t){for(let e=0;e<t.length;e++)if(t[e].length>1)return!0;return!1},allSubsetsEqual:function(t){let e=null;for(let n=0;n<t.length;n++){const s=t[n];if(null===e)e=s;else if(s!==e)return!1}return!0},getUniqueAlt:function(t){const e=Zt.getAlts(t);return 1===e.length?e.minValue():j.INVALID_ALT_NUMBER},getAlts:function(t){const e=new G;return t.map((function(t){e.or(t)})),e},getConflictingAltSubsets:function(t){const e=new q;return e.hashFunction=function(t){a.hashStuff(t.state.stateNumber,t.context)},e.equalsFunction=function(t,e){return t.state.stateNumber===e.state.stateNumber&&t.context.equals(e.context)},t.items.map((function(t){let n=e.get(t);null===n&&(n=new G,e.set(t,n)),n.set(t.alt)})),e.getValues()},getStateToAltMap:function(t){const e=new Jt;return t.items.map((function(t){let n=e.get(t.state);null===n&&(n=new G,e.set(t.state,n)),n.set(t.alt)})),e},hasStateAssociatedWithOneAlt:function(t){const e=Zt.getStateToAltMap(t).values();for(let t=0;t<e.length;t++)if(1===e[t].length)return!0;return!1},getSingleViableAlt:function(t){let e=null;for(let n=0;n<t.length;n++){const s=t[n].minValue();if(null===e)e=s;else if(e!==s)return j.INVALID_ALT_NUMBER}return e}},Qt=Zt;class te extends Dt{constructor(t,e,n,s,i,r){r=r||t._ctx,s=s||t.getCurrentToken(),n=n||t.getCurrentToken(),e=e||t.getInputStream(),super({message:"",recognizer:t,input:e,ctx:r}),this.deadEndConfigs=i,this.startToken=n,this.offendingToken=s}}class ee{constructor(t){this.defaultMapCtor=t||q,this.cacheMap=new this.defaultMapCtor}get(t,e){const n=this.cacheMap.get(t)||null;return null===n?null:n.get(e)||null}set(t,e,n){let s=this.cacheMap.get(t)||null;null===s&&(s=new this.defaultMapCtor,this.cacheMap.set(t,s)),s.set(e,n)}}class ne extends qt{constructor(t,e,n,s){super(e,s),this.parser=t,this.decisionToDFA=n,this.predictionMode=Qt.LL,this._input=null,this._startIndex=0,this._outerContext=null,this._dfa=null,this.mergeCache=null,this.debug=!1,this.debug_closure=!1,this.debug_add=!1,this.trace_atn_sim=!1,this.dfa_debug=!1,this.retry_debug=!1}reset(){}adaptivePredict(t,e,n){(this.debug||this.trace_atn_sim)&&console.log("adaptivePredict decision "+e+" exec LA(1)=="+this.getLookaheadName(t)+" line "+t.LT(1).line+":"+t.LT(1).column),this._input=t,this._startIndex=t.index,this._outerContext=n;const s=this.decisionToDFA[e];this._dfa=s;const i=t.mark(),r=t.index;try{let e;if(e=s.precedenceDfa?s.getPrecedenceStartState(this.parser.getPrecedence()):s.s0,null===e){null===n&&(n=M.EMPTY),this.debug&&console.log("predictATN decision "+s.decision+" exec LA(1)=="+this.getLookaheadName(t)+", outerContext="+n.toString(this.parser.ruleNames));const i=!1;let r=this.computeStartState(s.atnStartState,M.EMPTY,i);s.precedenceDfa?(s.s0.configs=r,r=this.applyPrecedenceFilter(r),e=this.addDFAState(s,new Vt(null,r)),s.setPrecedenceStartState(this.parser.getPrecedence(),e)):(e=this.addDFAState(s,new Vt(null,r)),s.s0=e)}const i=this.execATN(s,e,t,r,n);return this.debug&&console.log("DFA after predictATN: "+s.toString(this.parser.literalNames,this.parser.symbolicNames)),i}finally{this._dfa=null,this.mergeCache=null,t.seek(r),t.release(i)}}execATN(t,e,n,i,r){let o;(this.debug||this.trace_atn_sim)&&console.log("execATN decision "+t.decision+", DFA state "+e+", LA(1)=="+this.getLookaheadName(n)+" line "+n.LT(1).line+":"+n.LT(1).column);let a=e;this.debug&&console.log("s0 = "+e);let l=n.LA(1);for(;;){let e=this.getExistingTargetState(a,l);if(null===e&&(e=this.computeTargetState(t,a,l)),e===qt.ERROR){const t=this.noViableAlt(n,r,a.configs,i);if(n.seek(i),o=this.getSynValidOrSemInvalidAltThatFinishedDecisionEntryRule(a.configs,r),o!==j.INVALID_ALT_NUMBER)return o;throw t}if(e.requiresFullContext&&this.predictionMode!==Qt.SLL){let s=null;if(null!==e.predicates){this.debug&&console.log("DFA state has preds in DFA sim LL failover");const t=n.index;if(t!==i&&n.seek(i),s=this.evalSemanticContext(e.predicates,r,!0),1===s.length)return this.debug&&console.log("Full LL avoided"),s.minValue();t!==i&&n.seek(t)}this.dfa_debug&&console.log("ctx sensitive state "+r+" in "+e);const a=!0,l=this.computeStartState(t.atnStartState,r,a);return this.reportAttemptingFullContext(t,s,e.configs,i,n.index),o=this.execATNWithFullContext(t,e,l,n,i,r),o}if(e.isAcceptState){if(null===e.predicates)return e.prediction;const s=n.index;n.seek(i);const o=this.evalSemanticContext(e.predicates,r,!0);if(0===o.length)throw this.noViableAlt(n,r,e.configs,i);return 1===o.length||this.reportAmbiguity(t,e,i,s,!1,o,e.configs),o.minValue()}a=e,l!==s.EOF&&(n.consume(),l=n.LA(1))}}getExistingTargetState(t,e){const n=t.edges;return null===n?null:n[e+1]||null}computeTargetState(t,e,n){const s=this.computeReachSet(e.configs,n,!1);if(null===s)return this.addDFAEdge(t,e,n,qt.ERROR),qt.ERROR;let i=new Vt(null,s);const r=this.getUniqueAlt(s);if(this.debug){const t=Qt.getConflictingAltSubsets(s);console.log("SLL altSubSets="+u(t)+", configs="+s+", predict="+r+", allSubsetsConflict="+Qt.allSubsetsConflict(t)+", conflictingAlts="+this.getConflictingAlts(s))}return r!==j.INVALID_ALT_NUMBER?(i.isAcceptState=!0,i.configs.uniqueAlt=r,i.prediction=r):Qt.hasSLLConflictTerminatingPrediction(this.predictionMode,s)&&(i.configs.conflictingAlts=this.getConflictingAlts(s),i.requiresFullContext=!0,i.isAcceptState=!0,i.prediction=i.configs.conflictingAlts.minValue()),i.isAcceptState&&i.configs.hasSemanticContext&&(this.predicateDFAState(i,this.atn.getDecisionState(t.decision)),null!==i.predicates&&(i.prediction=j.INVALID_ALT_NUMBER)),i=this.addDFAEdge(t,e,n,i),i}predicateDFAState(t,e){const n=e.transitions.length,s=this.getConflictingAltsOrUniqueAlt(t.configs),i=this.getPredsForAmbigAlts(s,t.configs,n);null!==i?(t.predicates=this.getPredicatePredictions(s,i),t.prediction=j.INVALID_ALT_NUMBER):t.prediction=s.minValue()}execATNWithFullContext(t,e,n,i,r,o){(this.debug||this.trace_atn_sim)&&console.log("execATNWithFullContext "+n);let a,l=!1,h=n;i.seek(r);let c=i.LA(1),u=-1;for(;;){if(a=this.computeReachSet(h,c,!0),null===a){const t=this.noViableAlt(i,o,h,r);i.seek(r);const e=this.getSynValidOrSemInvalidAltThatFinishedDecisionEntryRule(h,o);if(e!==j.INVALID_ALT_NUMBER)return e;throw t}const t=Qt.getConflictingAltSubsets(a);if(this.debug&&console.log("LL altSubSets="+t+", predict="+Qt.getUniqueAlt(t)+", resolvesToJustOneViableAlt="+Qt.resolvesToJustOneViableAlt(t)),a.uniqueAlt=this.getUniqueAlt(a),a.uniqueAlt!==j.INVALID_ALT_NUMBER){u=a.uniqueAlt;break}if(this.predictionMode!==Qt.LL_EXACT_AMBIG_DETECTION){if(u=Qt.resolvesToJustOneViableAlt(t),u!==j.INVALID_ALT_NUMBER)break}else if(Qt.allSubsetsConflict(t)&&Qt.allSubsetsEqual(t)){l=!0,u=Qt.getSingleViableAlt(t);break}h=a,c!==s.EOF&&(i.consume(),c=i.LA(1))}return a.uniqueAlt!==j.INVALID_ALT_NUMBER?(this.reportContextSensitivity(t,u,a,r,i.index),u):(this.reportAmbiguity(t,e,r,i.index,l,null,a),u)}computeReachSet(t,e,n){this.debug&&console.log("in computeReachSet, starting closure: "+t),null===this.mergeCache&&(this.mergeCache=new ee);const i=new zt(n);let r=null;for(let o=0;o<t.items.length;o++){const a=t.items[o];if(this.debug&&console.log("testing "+this.getTokenName(e)+" at "+a),a.state instanceof C)(n||e===s.EOF)&&(null===r&&(r=[]),r.push(a),this.debug_add&&console.log("added "+a+" to skippedStopStates"));else for(let t=0;t<a.state.transitions.length;t++){const n=a.state.transitions[t],s=this.getReachableTarget(n,e);if(null!==s){const t=new S({state:s},a);i.add(t,this.mergeCache),this.debug_add&&console.log("added "+t+" to intermediate")}}}let o=null;if(null===r&&e!==s.EOF&&(1===i.items.length||this.getUniqueAlt(i)!==j.INVALID_ALT_NUMBER)&&(o=i),null===o){o=new zt(n);const t=new d,r=e===s.EOF;for(let e=0;e<i.items.length;e++)this.closure(i.items[e],o,t,!1,n,r)}if(e===s.EOF&&(o=this.removeAllConfigsNotInRuleStopState(o,o===i)),!(null===r||n&&Qt.hasConfigInRuleStopState(o)))for(let t=0;t<r.length;t++)o.add(r[t],this.mergeCache);return this.trace_atn_sim&&console.log("computeReachSet "+t+" -> "+o),0===o.items.length?null:o}removeAllConfigsNotInRuleStopState(t,e){if(Qt.allConfigsInRuleStopStates(t))return t;const n=new zt(t.fullCtx);for(let i=0;i<t.items.length;i++){const r=t.items[i];if(r.state instanceof C)n.add(r,this.mergeCache);else if(e&&r.state.epsilonOnlyTransitions&&this.atn.nextTokens(r.state).contains(s.EPSILON)){const t=this.atn.ruleToStopState[r.state.ruleIndex];n.add(new S({state:t},r),this.mergeCache)}}return n}computeStartState(t,e,n){const s=H(this.atn,e),i=new zt(n);this.trace_atn_sim&&console.log("computeStartState from ATN state "+t+" initialContext="+s.toString(this.parser));for(let e=0;e<t.transitions.length;e++){const r=t.transitions[e].target,o=new S({state:r,alt:e+1,context:s},null),a=new d;this.closure(o,i,a,!0,n,!1)}return i}applyPrecedenceFilter(t){let e;const n=[],s=new zt(t.fullCtx);for(let i=0;i<t.items.length;i++){if(e=t.items[i],1!==e.alt)continue;const r=e.semanticContext.evalPrecedence(this.parser,this._outerContext);null!==r&&(n[e.state.stateNumber]=e.context,r!==e.semanticContext?s.add(new S({semanticContext:r},e),this.mergeCache):s.add(e,this.mergeCache))}for(let i=0;i<t.items.length;i++)if(e=t.items[i],1!==e.alt){if(!e.precedenceFilterSuppressed){const t=n[e.state.stateNumber]||null;if(null!==t&&t.equals(e.context))continue}s.add(e,this.mergeCache)}return s}getReachableTarget(t,e){return t.matches(e,0,this.atn.maxTokenType)?t.target:null}getPredsForAmbigAlts(t,e,n){let s=[];for(let n=0;n<e.items.length;n++){const i=e.items[n];t.get(i.alt)&&(s[i.alt]=g.orContext(s[i.alt]||null,i.semanticContext))}let i=0;for(let t=1;t<n+1;t++){const e=s[t]||null;null===e?s[t]=g.NONE:e!==g.NONE&&(i+=1)}return 0===i&&(s=null),this.debug&&console.log("getPredsForAmbigAlts result "+u(s)),s}getPredicatePredictions(t,e){const n=[];let s=!1;for(let i=1;i<e.length;i++){const r=e[i];null!==t&&t.get(i)&&n.push(new Xt(r,i)),r!==g.NONE&&(s=!0)}return s?n:null}getSynValidOrSemInvalidAltThatFinishedDecisionEntryRule(t,e){const n=this.splitAccordingToSemanticValidity(t,e),s=n[0],i=n[1];let r=this.getAltThatFinishedDecisionEntryRule(s);return r!==j.INVALID_ALT_NUMBER||i.items.length>0&&(r=this.getAltThatFinishedDecisionEntryRule(i),r!==j.INVALID_ALT_NUMBER)?r:j.INVALID_ALT_NUMBER}getAltThatFinishedDecisionEntryRule(t){const e=[];for(let n=0;n<t.items.length;n++){const s=t.items[n];(s.reachesIntoOuterContext>0||s.state instanceof C&&s.context.hasEmptyPath())&&e.indexOf(s.alt)<0&&e.push(s.alt)}return 0===e.length?j.INVALID_ALT_NUMBER:Math.min.apply(null,e)}splitAccordingToSemanticValidity(t,e){const n=new zt(t.fullCtx),s=new zt(t.fullCtx);for(let i=0;i<t.items.length;i++){const r=t.items[i];r.semanticContext!==g.NONE?r.semanticContext.evaluate(this.parser,e)?n.add(r):s.add(r):n.add(r)}return[n,s]}evalSemanticContext(t,e,n){const s=new G;for(let i=0;i<t.length;i++){const r=t[i];if(r.pred===g.NONE){if(s.set(r.alt),!n)break;continue}const o=r.pred.evaluate(this.parser,e);if((this.debug||this.dfa_debug)&&console.log("eval pred "+r+"="+o),o&&((this.debug||this.dfa_debug)&&console.log("PREDICT "+r.alt),s.set(r.alt),!n))break}return s}closure(t,e,n,s,i,r){this.closureCheckingStopState(t,e,n,s,i,0,r)}closureCheckingStopState(t,e,n,s,i,r,o){if((this.trace_atn_sim||this.debug_closure)&&console.log("closure("+t.toString(this.parser,!0)+")"),t.state instanceof C){if(!t.context.isEmpty()){for(let a=0;a<t.context.length;a++){if(t.context.getReturnState(a)===U.EMPTY_RETURN_STATE){if(i){e.add(new S({state:t.state,context:U.EMPTY},t),this.mergeCache);continue}this.debug&&console.log("FALLING off rule "+this.getRuleName(t.state.ruleIndex)),this.closure_(t,e,n,s,i,r,o);continue}const l=this.atn.states[t.context.getReturnState(a)],h=t.context.getParent(a),c={state:l,alt:t.alt,context:h,semanticContext:t.semanticContext},u=new S(c,null);u.reachesIntoOuterContext=t.reachesIntoOuterContext,this.closureCheckingStopState(u,e,n,s,i,r-1,o)}return}if(i)return void e.add(t,this.mergeCache);this.debug&&console.log("FALLING off rule "+this.getRuleName(t.state.ruleIndex))}this.closure_(t,e,n,s,i,r,o)}closure_(t,e,n,s,i,r,o){const a=t.state;a.epsilonOnlyTransitions||e.add(t,this.mergeCache);for(let l=0;l<a.transitions.length;l++){if(0===l&&this.canDropLoopEntryEdgeInLeftRecursiveRule(t))continue;const h=a.transitions[l],c=s&&!(h instanceof ct),u=this.getEpsilonTarget(t,h,c,0===r,i,o);if(null!==u){let s=r;if(t.state instanceof C){if(null!==this._dfa&&this._dfa.precedenceDfa&&h.outermostPrecedenceReturn===this._dfa.atnStartState.ruleIndex&&(u.precedenceFilterSuppressed=!0),u.reachesIntoOuterContext+=1,n.getOrAdd(u)!==u)continue;e.dipsIntoOuterContext=!0,s-=1,this.debug&&console.log("dips into outer ctx: "+u)}else{if(!h.isEpsilon&&n.getOrAdd(u)!==u)continue;h instanceof N&&s>=0&&(s+=1)}this.closureCheckingStopState(u,e,n,c,i,s,o)}}}canDropLoopEntryEdgeInLeftRecursiveRule(t){const e=t.state;if(e.stateType!==_.STAR_LOOP_ENTRY)return!1;if(e.stateType!==_.STAR_LOOP_ENTRY||!e.isPrecedenceDecision||t.context.isEmpty()||t.context.hasEmptyPath())return!1;const n=t.context.length;for(let s=0;s<n;s++)if(this.atn.states[t.context.getReturnState(s)].ruleIndex!==e.ruleIndex)return!1;const s=e.transitions[0].target.endState.stateNumber,i=this.atn.states[s];for(let s=0;s<n;s++){const n=t.context.getReturnState(s),r=this.atn.states[n];if(1!==r.transitions.length||!r.transitions[0].isEpsilon)return!1;const o=r.transitions[0].target;if(!(r.stateType===_.BLOCK_END&&o===e||r===i||o===i||o.stateType===_.BLOCK_END&&1===o.transitions.length&&o.transitions[0].isEpsilon&&o.transitions[0].target===e))return!1}return!0}getRuleName(t){return null!==this.parser&&t>=0?this.parser.ruleNames[t]:"<rule "+t+">"}getEpsilonTarget(t,e,n,i,r,o){switch(e.serializationType){case A.RULE:return this.ruleTransition(t,e);case A.PRECEDENCE:return this.precedenceTransition(t,e,n,i,r);case A.PREDICATE:return this.predTransition(t,e,n,i,r);case A.ACTION:return this.actionTransition(t,e);case A.EPSILON:return new S({state:e.target},t);case A.ATOM:case A.RANGE:case A.SET:return o&&e.matches(s.EOF,0,1)?new S({state:e.target},t):null;default:return null}}actionTransition(t,e){if(this.debug){const t=-1===e.actionIndex?65535:e.actionIndex;console.log("ACTION edge "+e.ruleIndex+":"+t)}return new S({state:e.target},t)}precedenceTransition(t,e,n,s,i){this.debug&&(console.log("PRED (collectPredicates="+n+") "+e.precedence+">=_p, ctx dependent=true"),null!==this.parser&&console.log("context surrounding pred is "+u(this.parser.getRuleInvocationStack())));let r=null;if(n&&s)if(i){const n=this._input.index;this._input.seek(this._startIndex);const s=e.getPredicate().evaluate(this.parser,this._outerContext);this._input.seek(n),s&&(r=new S({state:e.target},t))}else{const n=g.andContext(t.semanticContext,e.getPredicate());r=new S({state:e.target,semanticContext:n},t)}else r=new S({state:e.target},t);return this.debug&&console.log("config from pred transition="+r),r}predTransition(t,e,n,s,i){this.debug&&(console.log("PRED (collectPredicates="+n+") "+e.ruleIndex+":"+e.predIndex+", ctx dependent="+e.isCtxDependent),null!==this.parser&&console.log("context surrounding pred is "+u(this.parser.getRuleInvocationStack())));let r=null;if(n&&(e.isCtxDependent&&s||!e.isCtxDependent))if(i){const n=this._input.index;this._input.seek(this._startIndex);const s=e.getPredicate().evaluate(this.parser,this._outerContext);this._input.seek(n),s&&(r=new S({state:e.target},t))}else{const n=g.andContext(t.semanticContext,e.getPredicate());r=new S({state:e.target,semanticContext:n},t)}else r=new S({state:e.target},t);return this.debug&&console.log("config from pred transition="+r),r}ruleTransition(t,e){this.debug&&console.log("CALL rule "+this.getRuleName(e.target.ruleIndex)+", ctx="+t.context);const n=e.followState,s=z.create(t.context,n.stateNumber);return new S({state:e.target,context:s},t)}getConflictingAlts(t){const e=Qt.getConflictingAltSubsets(t);return Qt.getAlts(e)}getConflictingAltsOrUniqueAlt(t){let e=null;return t.uniqueAlt!==j.INVALID_ALT_NUMBER?(e=new G,e.set(t.uniqueAlt)):e=t.conflictingAlts,e}getTokenName(t){if(t===s.EOF)return"EOF";if(null!==this.parser&&null!==this.parser.literalNames){if(!(t>=this.parser.literalNames.length&&t>=this.parser.symbolicNames.length))return(this.parser.literalNames[t]||this.parser.symbolicNames[t])+"<"+t+">";console.log(t+" ttype out of range: "+this.parser.literalNames),console.log(""+this.parser.getInputStream().getTokens())}return""+t}getLookaheadName(t){return this.getTokenName(t.LA(1))}dumpDeadEndConfigs(t){console.log("dead end configs: ");const e=t.getDeadEndConfigs();for(let t=0;t<e.length;t++){const n=e[t];let s="no edges";if(n.state.transitions.length>0){const t=n.state.transitions[0];t instanceof lt?s="Atom "+this.getTokenName(t.label):t instanceof k&&(s=(t instanceof I?"~":"")+"Set "+t.set)}console.error(n.toString(this.parser,!0)+":"+s)}}noViableAlt(t,e,n,s){return new te(this.parser,t,t.get(s),t.LT(1),n,e)}getUniqueAlt(t){let e=j.INVALID_ALT_NUMBER;for(let n=0;n<t.items.length;n++){const s=t.items[n];if(e===j.INVALID_ALT_NUMBER)e=s.alt;else if(s.alt!==e)return j.INVALID_ALT_NUMBER}return e}addDFAEdge(t,e,n,s){if(this.debug&&console.log("EDGE "+e+" -> "+s+" upon "+this.getTokenName(n)),null===s)return null;if(s=this.addDFAState(t,s),null===e||n<-1||n>this.atn.maxTokenType)return s;if(null===e.edges&&(e.edges=[]),e.edges[n+1]=s,this.debug){const e=null===this.parser?null:this.parser.literalNames,n=null===this.parser?null:this.parser.symbolicNames;console.log("DFA=\n"+t.toString(e,n))}return s}addDFAState(t,e){if(e===qt.ERROR)return e;const n=t.states.get(e);return null!==n?(this.trace_atn_sim&&console.log("addDFAState "+e+" exists"),n):(e.stateNumber=t.states.length,e.configs.readOnly||(e.configs.optimizeConfigs(this),e.configs.setReadonly(!0)),this.trace_atn_sim&&console.log("addDFAState new "+e),t.states.add(e),this.debug&&console.log("adding new DFA state: "+e),e)}reportAttemptingFullContext(t,e,n,s,i){if(this.debug||this.retry_debug){const e=new m(s,i+1);console.log("reportAttemptingFullContext decision="+t.decision+":"+n+", input="+this.parser.getTokenStream().getText(e))}null!==this.parser&&this.parser.getErrorListener().reportAttemptingFullContext(this.parser,t,s,i,e,n)}reportContextSensitivity(t,e,n,s,i){if(this.debug||this.retry_debug){const e=new m(s,i+1);console.log("reportContextSensitivity decision="+t.decision+":"+n+", input="+this.parser.getTokenStream().getText(e))}null!==this.parser&&this.parser.getErrorListener().reportContextSensitivity(this.parser,t,s,i,e,n)}reportAmbiguity(t,e,n,s,i,r,o){if(this.debug||this.retry_debug){const t=new m(n,s+1);console.log("reportAmbiguity "+r+":"+o+", input="+this.parser.getTokenStream().getText(t))}null!==this.parser&&this.parser.getErrorListener().reportAmbiguity(this.parser,t,n,s,i,r,o)}}class se{constructor(){this.cache=new q}add(t){if(t===U.EMPTY)return U.EMPTY;const e=this.cache.get(t)||null;return null!==e?e:(this.cache.set(t,t),t)}get(t){return this.cache.get(t)||null}get length(){return this.cache.length}}const ie={ATN:j,ATNDeserializer:yt,LexerATNSimulator:$t,ParserATNSimulator:ne,PredictionMode:Qt,PredictionContextCache:se};class re{constructor(t,e,n){this.dfa=t,this.literalNames=e||[],this.symbolicNames=n||[]}toString(){if(null===this.dfa.s0)return null;let t="";const e=this.dfa.sortedStates();for(let n=0;n<e.length;n++){const s=e[n];if(null!==s.edges){const e=s.edges.length;for(let n=0;n<e;n++){const e=s.edges[n]||null;null!==e&&2147483647!==e.stateNumber&&(t=t.concat(this.getStateString(s)),t=t.concat("-"),t=t.concat(this.getEdgeLabel(n)),t=t.concat("->"),t=t.concat(this.getStateString(e)),t=t.concat("\n"))}}}return 0===t.length?null:t}getEdgeLabel(t){return 0===t?"EOF":null!==this.literalNames||null!==this.symbolicNames?this.literalNames[t-1]||this.symbolicNames[t-1]:String.fromCharCode(t-1)}getStateString(t){const e=(t.isAcceptState?":":"")+"s"+t.stateNumber+(t.requiresFullContext?"^":"");return t.isAcceptState?null!==t.predicates?e+"=>"+u(t.predicates):e+"=>"+t.prediction.toString():e}}class oe extends re{constructor(t){super(t,null)}getEdgeLabel(t){return"'"+String.fromCharCode(t)+"'"}}class ae{constructor(t,e){if(void 0===e&&(e=0),this.atnStartState=t,this.decision=e,this._states=new d,this.s0=null,this.precedenceDfa=!1,t instanceof it&&t.isPrecedenceDecision){this.precedenceDfa=!0;const t=new Vt(null,new zt);t.edges=[],t.isAcceptState=!1,t.requiresFullContext=!1,this.s0=t}}getPrecedenceStartState(t){if(!this.precedenceDfa)throw"Only precedence DFAs may contain a precedence start state.";return t<0||t>=this.s0.edges.length?null:this.s0.edges[t]||null}setPrecedenceStartState(t,e){if(!this.precedenceDfa)throw"Only precedence DFAs may contain a precedence start state.";t<0||(this.s0.edges[t]=e)}setPrecedenceDfa(t){if(this.precedenceDfa!==t){if(this._states=new d,t){const t=new Vt(null,new zt);t.edges=[],t.isAcceptState=!1,t.requiresFullContext=!1,this.s0=t}else this.s0=null;this.precedenceDfa=t}}sortedStates(){return this._states.values().sort((function(t,e){return t.stateNumber-e.stateNumber}))}toString(t,e){return t=t||null,e=e||null,null===this.s0?"":new re(this,t,e).toString()}toLexerString(){return null===this.s0?"":new oe(this).toString()}get states(){return this._states}}const le={DFA:ae,DFASerializer:re,LexerDFASerializer:oe,PredPrediction:Xt},he={PredictionContext:U},ce={Interval:m,IntervalSet:E};class ue{visitTerminal(t){}visitErrorNode(t){}enterEveryRule(t){}exitEveryRule(t){}}class de{visit(t){return Array.isArray(t)?t.map((function(t){return t.accept(this)}),this):t.accept(this)}visitChildren(t){return t.children?this.visit(t.children):null}visitTerminal(t){}visitErrorNode(t){}}class ge{walk(t,e){if(e instanceof b||void 0!==e.isErrorNode&&e.isErrorNode())t.visitErrorNode(e);else if(e instanceof P)t.visitTerminal(e);else{this.enterRule(t,e);for(let n=0;n<e.getChildCount();n++){const s=e.getChild(n);this.walk(t,s)}this.exitRule(t,e)}}enterRule(t,e){const n=e.ruleContext;t.enterEveryRule(n),n.enterRule(t)}exitRule(t,e){const n=e.ruleContext;n.exitRule(t),t.exitEveryRule(n)}}ge.DEFAULT=new ge;const pe={Trees:F,RuleNode:v,ErrorNode:b,TerminalNode:P,ParseTreeListener:ue,ParseTreeVisitor:de,ParseTreeWalker:ge};class fe extends Dt{constructor(t){super({message:"",recognizer:t,input:t.getInputStream(),ctx:t._ctx}),this.offendingToken=t.getCurrentToken()}}class xe extends Dt{constructor(t,e,n){super({message:Te(e,n||null),recognizer:t,input:t.getInputStream(),ctx:t._ctx});const s=t._interp.atn.states[t.state].transitions[0];s instanceof gt?(this.ruleIndex=s.ruleIndex,this.predicateIndex=s.predIndex):(this.ruleIndex=0,this.predicateIndex=0),this.predicate=e,this.offendingToken=t.getCurrentToken()}}function Te(t,e){return null!==e?e:"failed predicate: {"+t+"}?"}class Se extends Lt{constructor(t){super(),t=t||!0,this.exactOnly=t}reportAmbiguity(t,e,n,s,i,r,o){if(this.exactOnly&&!i)return;const a="reportAmbiguity d="+this.getDecisionDescription(t,e)+": ambigAlts="+this.getConflictingAlts(r,o)+", input='"+t.getTokenStream().getText(new m(n,s))+"'";t.notifyErrorListeners(a)}reportAttemptingFullContext(t,e,n,s,i,r){const o="reportAttemptingFullContext d="+this.getDecisionDescription(t,e)+", input='"+t.getTokenStream().getText(new m(n,s))+"'";t.notifyErrorListeners(o)}reportContextSensitivity(t,e,n,s,i,r){const o="reportContextSensitivity d="+this.getDecisionDescription(t,e)+", input='"+t.getTokenStream().getText(new m(n,s))+"'";t.notifyErrorListeners(o)}getDecisionDescription(t,e){const n=e.decision,s=e.atnStartState.ruleIndex,i=t.ruleNames;if(s<0||s>=i.length)return""+n;const r=i[s]||null;return null===r||0===r.length?""+n:`${n} (${r})`}getConflictingAlts(t,e){if(null!==t)return t;const n=new G;for(let t=0;t<e.items.length;t++)n.set(e.items[t].alt);return`{${n.values().join(", ")}}`}}class me extends Error{constructor(){super(),Error.captureStackTrace(this,me)}}class Ee{reset(t){}recoverInline(t){}recover(t,e){}sync(t){}inErrorRecoveryMode(t){}reportError(t){}}class _e extends Ee{constructor(){super(),this.errorRecoveryMode=!1,this.lastErrorIndex=-1,this.lastErrorStates=null,this.nextTokensContext=null,this.nextTokenState=0}reset(t){this.endErrorCondition(t)}beginErrorCondition(t){this.errorRecoveryMode=!0}inErrorRecoveryMode(t){return this.errorRecoveryMode}endErrorCondition(t){this.errorRecoveryMode=!1,this.lastErrorStates=null,this.lastErrorIndex=-1}reportMatch(t){this.endErrorCondition(t)}reportError(t,e){this.inErrorRecoveryMode(t)||(this.beginErrorCondition(t),e instanceof te?this.reportNoViableAlternative(t,e):e instanceof fe?this.reportInputMismatch(t,e):e instanceof xe?this.reportFailedPredicate(t,e):(console.log("unknown recognition error type: "+e.constructor.name),console.log(e.stack),t.notifyErrorListeners(e.getOffendingToken(),e.getMessage(),e)))}recover(t,e){this.lastErrorIndex===t.getInputStream().index&&null!==this.lastErrorStates&&this.lastErrorStates.indexOf(t.state)>=0&&t.consume(),this.lastErrorIndex=t._input.index,null===this.lastErrorStates&&(this.lastErrorStates=[]),this.lastErrorStates.push(t.state);const n=this.getErrorRecoverySet(t);this.consumeUntil(t,n)}sync(t){if(this.inErrorRecoveryMode(t))return;const e=t._interp.atn.states[t.state],n=t.getTokenStream().LA(1),i=t.atn.nextTokens(e);if(i.contains(n))return this.nextTokensContext=null,void(this.nextTokenState=_.INVALID_STATE_NUMBER);if(i.contains(s.EPSILON))null===this.nextTokensContext&&(this.nextTokensContext=t._ctx,this.nextTokensState=t._stateNumber);else switch(e.stateType){case _.BLOCK_START:case _.STAR_BLOCK_START:case _.PLUS_BLOCK_START:case _.STAR_LOOP_ENTRY:if(null!==this.singleTokenDeletion(t))return;throw new fe(t);case _.PLUS_LOOP_BACK:case _.STAR_LOOP_BACK:{this.reportUnwantedToken(t);const e=new E;e.addSet(t.getExpectedTokens());const n=e.addSet(this.getErrorRecoverySet(t));this.consumeUntil(t,n)}}}reportNoViableAlternative(t,e){const n=t.getTokenStream();let i;i=null!==n?e.startToken.type===s.EOF?"<EOF>":n.getText(new m(e.startToken.tokenIndex,e.offendingToken.tokenIndex)):"<unknown input>";const r="no viable alternative at input "+this.escapeWSAndQuote(i);t.notifyErrorListeners(r,e.offendingToken,e)}reportInputMismatch(t,e){const n="mismatched input "+this.getTokenErrorDisplay(e.offendingToken)+" expecting "+e.getExpectedTokens().toString(t.literalNames,t.symbolicNames);t.notifyErrorListeners(n,e.offendingToken,e)}reportFailedPredicate(t,e){const n="rule "+t.ruleNames[t._ctx.ruleIndex]+" "+e.message;t.notifyErrorListeners(n,e.offendingToken,e)}reportUnwantedToken(t){if(this.inErrorRecoveryMode(t))return;this.beginErrorCondition(t);const e=t.getCurrentToken(),n="extraneous input "+this.getTokenErrorDisplay(e)+" expecting "+this.getExpectedTokens(t).toString(t.literalNames,t.symbolicNames);t.notifyErrorListeners(n,e,null)}reportMissingToken(t){if(this.inErrorRecoveryMode(t))return;this.beginErrorCondition(t);const e=t.getCurrentToken(),n="missing "+this.getExpectedTokens(t).toString(t.literalNames,t.symbolicNames)+" at "+this.getTokenErrorDisplay(e);t.notifyErrorListeners(n,e,null)}recoverInline(t){const e=this.singleTokenDeletion(t);if(null!==e)return t.consume(),e;if(this.singleTokenInsertion(t))return this.getMissingSymbol(t);throw new fe(t)}singleTokenInsertion(t){const e=t.getTokenStream().LA(1),n=t._interp.atn,s=n.states[t.state].transitions[0].target;return!!n.nextTokens(s,t._ctx).contains(e)&&(this.reportMissingToken(t),!0)}singleTokenDeletion(t){const e=t.getTokenStream().LA(2);if(this.getExpectedTokens(t).contains(e)){this.reportUnwantedToken(t),t.consume();const e=t.getCurrentToken();return this.reportMatch(t),e}return null}getMissingSymbol(t){const e=t.getCurrentToken(),n=this.getExpectedTokens(t).first();let i;i=n===s.EOF?"<missing EOF>":"<missing "+t.literalNames[n]+">";let r=e;const o=t.getTokenStream().LT(-1);return r.type===s.EOF&&null!==o&&(r=o),t.getTokenFactory().create(r.source,n,i,s.DEFAULT_CHANNEL,-1,-1,r.line,r.column)}getExpectedTokens(t){return t.getExpectedTokens()}getTokenErrorDisplay(t){if(null===t)return"<no token>";let e=t.text;return null===e&&(e=t.type===s.EOF?"<EOF>":"<"+t.type+">"),this.escapeWSAndQuote(e)}escapeWSAndQuote(t){return"'"+(t=(t=(t=t.replace(/\n/g,"\\n")).replace(/\r/g,"\\r")).replace(/\t/g,"\\t"))+"'"}getErrorRecoverySet(t){const e=t._interp.atn;let n=t._ctx;const i=new E;for(;null!==n&&n.invokingState>=0;){const t=e.states[n.invokingState].transitions[0],s=e.nextTokens(t.followState);i.addSet(s),n=n.parentCtx}return i.removeOne(s.EPSILON),i}consumeUntil(t,e){let n=t.getTokenStream().LA(1);for(;n!==s.EOF&&!e.contains(n);)t.consume(),n=t.getTokenStream().LA(1)}}class Ce extends _e{constructor(){super()}recover(t,e){let n=t._ctx;for(;null!==n;)n.exception=e,n=n.parentCtx;throw new me(e)}recoverInline(t){this.recover(t,new fe(t))}sync(t){}}const Ae={RecognitionException:Dt,NoViableAltException:te,LexerNoViableAltException:Ft,InputMismatchException:fe,FailedPredicateException:xe,DiagnosticErrorListener:Se,BailErrorStrategy:Ce,DefaultErrorStrategy:_e,ErrorListener:Lt};class Ne{constructor(t,e){if(this.name="<empty>",this.strdata=t,this.decodeToUnicodeCodePoints=e||!1,this._index=0,this.data=[],this.decodeToUnicodeCodePoints)for(let t=0;t<this.strdata.length;){const e=this.strdata.codePointAt(t);this.data.push(e),t+=e<=65535?1:2}else{this.data=new Array(this.strdata.length);for(let t=0;t<this.strdata.length;t++)this.data[t]=this.strdata.charCodeAt(t)}this._size=this.data.length}reset(){this._index=0}consume(){if(this._index>=this._size)throw"cannot consume EOF";this._index+=1}LA(t){if(0===t)return 0;t<0&&(t+=1);const e=this._index+t-1;return e<0||e>=this._size?s.EOF:this.data[e]}LT(t){return this.LA(t)}mark(){return-1}release(t){}seek(t){t<=this._index?this._index=t:this._index=Math.min(t,this._size)}getText(t,e){if(e>=this._size&&(e=this._size-1),t>=this._size)return"";if(this.decodeToUnicodeCodePoints){let n="";for(let s=t;s<=e;s++)n+=String.fromCodePoint(this.data[s]);return n}return this.strdata.slice(t,e+1)}toString(){return this.strdata}get index(){return this._index}get size(){return this._size}}class ke extends Ne{constructor(t,e){super(t,e)}}const Ie=t(import.meta.url)("fs"),ye="undefined"!=typeof process&&null!=process.versions&&null!=process.versions.node;class Le extends ke{static fromPath(t,e,n){if(!ye)throw new Error("FileStream is only available when running in Node!");Ie.readFile(t,e,(function(t,e){let s=null;null!==e&&(s=new Ne(e,!0)),n(t,s)}))}constructor(t,e,n){if(!ye)throw new Error("FileStream is only available when running in Node!");super(Ie.readFileSync(t,e||"utf-8"),n),this.fileName=t}}const Oe={fromString:function(t){return new Ne(t,!0)},fromBlob:function(t,e,n,s){const i=new window.FileReader;i.onload=function(t){const e=new Ne(t.target.result,!0);n(e)},i.onerror=s,i.readAsText(t,e)},fromBuffer:function(t,e){return new Ne(t.toString(e),!0)},fromPath:function(t,e,n){Le.fromPath(t,e,n)},fromPathSync:function(t,e){return new Le(t,e)}},Re={arrayToString:u,stringToCharArray:function(t){let e=new Uint16Array(t.length);for(let n=0;n<t.length;n++)e[n]=t.charCodeAt(n);return e}};class we{}class ve extends we{constructor(t){super(),this.tokenSource=t,this.tokens=[],this.index=-1,this.fetchedEOF=!1}mark(){return 0}release(t){}reset(){this.seek(0)}seek(t){this.lazyInit(),this.index=this.adjustSeekIndex(t)}get size(){return this.tokens.length}get(t){return this.lazyInit(),this.tokens[t]}consume(){let t=!1;if(t=this.index>=0&&(this.fetchedEOF?this.index<this.tokens.length-1:this.index<this.tokens.length),!t&&this.LA(1)===s.EOF)throw"cannot consume EOF";this.sync(this.index+1)&&(this.index=this.adjustSeekIndex(this.index+1))}sync(t){const e=t-this.tokens.length+1;return!(e>0)||this.fetch(e)>=e}fetch(t){if(this.fetchedEOF)return 0;for(let e=0;e<t;e++){const t=this.tokenSource.nextToken();if(t.tokenIndex=this.tokens.length,this.tokens.push(t),t.type===s.EOF)return this.fetchedEOF=!0,e+1}return t}getTokens(t,e,n){if(void 0===n&&(n=null),t<0||e<0)return null;this.lazyInit();const i=[];e>=this.tokens.length&&(e=this.tokens.length-1);for(let r=t;r<e;r++){const t=this.tokens[r];if(t.type===s.EOF)break;(null===n||n.contains(t.type))&&i.push(t)}return i}LA(t){return this.LT(t).type}LB(t){return this.index-t<0?null:this.tokens[this.index-t]}LT(t){if(this.lazyInit(),0===t)return null;if(t<0)return this.LB(-t);const e=this.index+t-1;return this.sync(e),e>=this.tokens.length?this.tokens[this.tokens.length-1]:this.tokens[e]}adjustSeekIndex(t){return t}lazyInit(){-1===this.index&&this.setup()}setup(){this.sync(0),this.index=this.adjustSeekIndex(0)}setTokenSource(t){this.tokenSource=t,this.tokens=[],this.index=-1,this.fetchedEOF=!1}nextTokenOnChannel(t,e){if(this.sync(t),t>=this.tokens.length)return-1;let n=this.tokens[t];for(;n.channel!==e;){if(n.type===s.EOF)return-1;t+=1,this.sync(t),n=this.tokens[t]}return t}previousTokenOnChannel(t,e){for(;t>=0&&this.tokens[t].channel!==e;)t-=1;return t}getHiddenTokensToRight(t,e){if(void 0===e&&(e=-1),this.lazyInit(),t<0||t>=this.tokens.length)throw t+" not in 0.."+this.tokens.length-1;const n=this.nextTokenOnChannel(t+1,Mt.DEFAULT_TOKEN_CHANNEL),s=t+1,i=-1===n?this.tokens.length-1:n;return this.filterForChannel(s,i,e)}getHiddenTokensToLeft(t,e){if(void 0===e&&(e=-1),this.lazyInit(),t<0||t>=this.tokens.length)throw t+" not in 0.."+this.tokens.length-1;const n=this.previousTokenOnChannel(t-1,Mt.DEFAULT_TOKEN_CHANNEL);if(n===t-1)return null;const s=n+1,i=t-1;return this.filterForChannel(s,i,e)}filterForChannel(t,e,n){const s=[];for(let i=t;i<e+1;i++){const t=this.tokens[i];-1===n?t.channel!==Mt.DEFAULT_TOKEN_CHANNEL&&s.push(t):t.channel===n&&s.push(t)}return 0===s.length?null:s}getSourceName(){return this.tokenSource.getSourceName()}getText(t){this.lazyInit(),this.fill(),t||(t=new m(0,this.tokens.length-1));let e=t.start;e instanceof s&&(e=e.tokenIndex);let n=t.stop;if(n instanceof s&&(n=n.tokenIndex),null===e||null===n||e<0||n<0)return"";n>=this.tokens.length&&(n=this.tokens.length-1);let i="";for(let t=e;t<n+1;t++){const e=this.tokens[t];if(e.type===s.EOF)break;i+=e.text}return i}fill(){for(this.lazyInit();1e3===this.fetch(1e3););}}Object.defineProperty(ve,"size",{get:function(){return this.tokens.length}});class Pe extends ve{constructor(t,e){super(t),this.channel=void 0===e?s.DEFAULT_CHANNEL:e}adjustSeekIndex(t){return this.nextTokenOnChannel(t,this.channel)}LB(t){if(0===t||this.index-t<0)return null;let e=this.index,n=1;for(;n<=t;)e=this.previousTokenOnChannel(e-1,this.channel),n+=1;return e<0?null:this.tokens[e]}LT(t){if(this.lazyInit(),0===t)return null;if(t<0)return this.LB(-t);let e=this.index,n=1;for(;n<t;)this.sync(e+1)&&(e=this.nextTokenOnChannel(e+1,this.channel)),n+=1;return this.tokens[e]}getNumberOfOnChannelTokens(){let t=0;this.fill();for(let e=0;e<this.tokens.length;e++){const n=this.tokens[e];if(n.channel===this.channel&&(t+=1),n.type===s.EOF)break}return t}}class be extends ue{constructor(t){super(),this.parser=t}enterEveryRule(t){console.log("enter   "+this.parser.ruleNames[t.ruleIndex]+", LT(1)="+this.parser._input.LT(1).text)}visitTerminal(t){console.log("consume "+t.symbol+" rule "+this.parser.ruleNames[this.parser._ctx.ruleIndex])}exitEveryRule(t){console.log("exit    "+this.parser.ruleNames[t.ruleIndex]+", LT(1)="+this.parser._input.LT(1).text)}}class De extends wt{constructor(t){super(),this._input=null,this._errHandler=new _e,this._precedenceStack=[],this._precedenceStack.push(0),this._ctx=null,this.buildParseTrees=!0,this._tracer=null,this._parseListeners=null,this._syntaxErrors=0,this.setInputStream(t)}reset(){null!==this._input&&this._input.seek(0),this._errHandler.reset(this),this._ctx=null,this._syntaxErrors=0,this.setTrace(!1),this._precedenceStack=[],this._precedenceStack.push(0),null!==this._interp&&this._interp.reset()}match(t){let e=this.getCurrentToken();return e.type===t?(this._errHandler.reportMatch(this),this.consume()):(e=this._errHandler.recoverInline(this),this.buildParseTrees&&-1===e.tokenIndex&&this._ctx.addErrorNode(e)),e}matchWildcard(){let t=this.getCurrentToken();return t.type>0?(this._errHandler.reportMatch(this),this.consume()):(t=this._errHandler.recoverInline(this),this.buildParseTrees&&-1===t.tokenIndex&&this._ctx.addErrorNode(t)),t}getParseListeners(){return this._parseListeners||[]}addParseListener(t){if(null===t)throw"listener";null===this._parseListeners&&(this._parseListeners=[]),this._parseListeners.push(t)}removeParseListener(t){if(null!==this._parseListeners){const e=this._parseListeners.indexOf(t);e>=0&&this._parseListeners.splice(e,1),0===this._parseListeners.length&&(this._parseListeners=null)}}removeParseListeners(){this._parseListeners=null}triggerEnterRuleEvent(){if(null!==this._parseListeners){const t=this._ctx;this._parseListeners.forEach((function(e){e.enterEveryRule(t),t.enterRule(e)}))}}triggerExitRuleEvent(){if(null!==this._parseListeners){const t=this._ctx;this._parseListeners.slice(0).reverse().forEach((function(e){t.exitRule(e),e.exitEveryRule(t)}))}}getTokenFactory(){return this._input.tokenSource._factory}setTokenFactory(t){this._input.tokenSource._factory=t}getATNWithBypassAlts(){const t=this.getSerializedATN();if(null===t)throw"The current parser does not support an ATN with bypass alternatives.";let e=this.bypassAltsAtnCache[t];if(null===e){const n=new xt;n.generateRuleBypassTransitions=!0,e=new yt(n).deserialize(t),this.bypassAltsAtnCache[t]=e}return e}getInputStream(){return this.getTokenStream()}setInputStream(t){this.setTokenStream(t)}getTokenStream(){return this._input}setTokenStream(t){this._input=null,this.reset(),this._input=t}get syntaxErrorsCount(){return this._syntaxErrors}getCurrentToken(){return this._input.LT(1)}notifyErrorListeners(t,e,n){n=n||null,null===(e=e||null)&&(e=this.getCurrentToken()),this._syntaxErrors+=1;const s=e.line,i=e.column;this.getErrorListener().syntaxError(this,e,s,i,t,n)}consume(){const t=this.getCurrentToken();t.type!==s.EOF&&this.getInputStream().consume();const e=null!==this._parseListeners&&this._parseListeners.length>0;if(this.buildParseTrees||e){let n;n=this._errHandler.inErrorRecoveryMode(this)?this._ctx.addErrorNode(t):this._ctx.addTokenNode(t),n.invokingState=this.state,e&&this._parseListeners.forEach((function(t){n instanceof b||void 0!==n.isErrorNode&&n.isErrorNode()?t.visitErrorNode(n):n instanceof P&&t.visitTerminal(n)}))}return t}addContextToParseTree(){null!==this._ctx.parentCtx&&this._ctx.parentCtx.addChild(this._ctx)}enterRule(t,e,n){this.state=e,this._ctx=t,this._ctx.start=this._input.LT(1),this.buildParseTrees&&this.addContextToParseTree(),this.triggerEnterRuleEvent()}exitRule(){this._ctx.stop=this._input.LT(-1),this.triggerExitRuleEvent(),this.state=this._ctx.invokingState,this._ctx=this._ctx.parentCtx}enterOuterAlt(t,e){t.setAltNumber(e),this.buildParseTrees&&this._ctx!==t&&null!==this._ctx.parentCtx&&(this._ctx.parentCtx.removeLastChild(),this._ctx.parentCtx.addChild(t)),this._ctx=t}getPrecedence(){return 0===this._precedenceStack.length?-1:this._precedenceStack[this._precedenceStack.length-1]}enterRecursionRule(t,e,n,s){this.state=e,this._precedenceStack.push(s),this._ctx=t,this._ctx.start=this._input.LT(1),this.triggerEnterRuleEvent()}pushNewRecursionContext(t,e,n){const s=this._ctx;s.parentCtx=t,s.invokingState=e,s.stop=this._input.LT(-1),this._ctx=t,this._ctx.start=s.start,this.buildParseTrees&&this._ctx.addChild(s),this.triggerEnterRuleEvent()}unrollRecursionContexts(t){this._precedenceStack.pop(),this._ctx.stop=this._input.LT(-1);const e=this._ctx,n=this.getParseListeners();if(null!==n&&n.length>0)for(;this._ctx!==t;)this.triggerExitRuleEvent(),this._ctx=this._ctx.parentCtx;else this._ctx=t;e.parentCtx=t,this.buildParseTrees&&null!==t&&t.addChild(e)}getInvokingContext(t){let e=this._ctx;for(;null!==e;){if(e.ruleIndex===t)return e;e=e.parentCtx}return null}precpred(t,e){return e>=this._precedenceStack[this._precedenceStack.length-1]}inContext(t){return!1}isExpectedToken(t){const e=this._interp.atn;let n=this._ctx;const i=e.states[this.state];let r=e.nextTokens(i);if(r.contains(t))return!0;if(!r.contains(s.EPSILON))return!1;for(;null!==n&&n.invokingState>=0&&r.contains(s.EPSILON);){const s=e.states[n.invokingState].transitions[0];if(r=e.nextTokens(s.followState),r.contains(t))return!0;n=n.parentCtx}return!(!r.contains(s.EPSILON)||t!==s.EOF)}getExpectedTokens(){return this._interp.atn.getExpectedTokens(this.state,this._ctx)}getExpectedTokensWithinCurrentRule(){const t=this._interp.atn,e=t.states[this.state];return t.nextTokens(e)}getRuleIndex(t){const e=this.getRuleIndexMap()[t];return null!==e?e:-1}getRuleInvocationStack(t){null===(t=t||null)&&(t=this._ctx);const e=[];for(;null!==t;){const n=t.ruleIndex;n<0?e.push("n/a"):e.push(this.ruleNames[n]),t=t.parentCtx}return e}getDFAStrings(){return this._interp.decisionToDFA.toString()}dumpDFA(){let t=!1;for(let e=0;e<this._interp.decisionToDFA.length;e++){const n=this._interp.decisionToDFA[e];n.states.length>0&&(t&&console.log(),this.printer.println("Decision "+n.decision+":"),this.printer.print(n.toString(this.literalNames,this.symbolicNames)),t=!0)}}getSourceName(){return this._input.getSourceName()}setTrace(t){t?(null!==this._tracer&&this.removeParseListener(this._tracer),this._tracer=new be(this),this.addParseListener(this._tracer)):(this.removeParseListener(this._tracer),this._tracer=null)}}De.bypassAltsAtnCache={};class Fe extends P{constructor(t){super(),this.parentCtx=null,this.symbol=t}getChild(t){return null}getSymbol(){return this.symbol}getParent(){return this.parentCtx}getPayload(){return this.symbol}getSourceInterval(){if(null===this.symbol)return m.INVALID_INTERVAL;const t=this.symbol.tokenIndex;return new m(t,t)}getChildCount(){return 0}accept(t){return t.visitTerminal(this)}getText(){return this.symbol.text}toString(){return this.symbol.type===s.EOF?"<EOF>":this.symbol.text}}class Me extends Fe{constructor(t){super(t)}isErrorNode(){return!0}accept(t){return t.visitErrorNode(this)}}class Ue extends M{constructor(t,e){super(t,e),this.children=null,this.start=null,this.stop=null,this.exception=null}copyFrom(t){this.parentCtx=t.parentCtx,this.invokingState=t.invokingState,this.children=null,this.start=t.start,this.stop=t.stop,t.children&&(this.children=[],t.children.map((function(t){t instanceof Me&&(this.children.push(t),t.parentCtx=this)}),this))}enterRule(t){}exitRule(t){}addChild(t){return null===this.children&&(this.children=[]),this.children.push(t),t}removeLastChild(){null!==this.children&&this.children.pop()}addTokenNode(t){const e=new Fe(t);return this.addChild(e),e.parentCtx=this,e}addErrorNode(t){const e=new Me(t);return this.addChild(e),e.parentCtx=this,e}getChild(t,e){if(e=e||null,null===this.children||t<0||t>=this.children.length)return null;if(null===e)return this.children[t];for(let n=0;n<this.children.length;n++){const s=this.children[n];if(s instanceof e){if(0===t)return s;t-=1}}return null}getToken(t,e){if(null===this.children||e<0||e>=this.children.length)return null;for(let n=0;n<this.children.length;n++){const s=this.children[n];if(s instanceof P&&s.symbol.type===t){if(0===e)return s;e-=1}}return null}getTokens(t){if(null===this.children)return[];{const e=[];for(let n=0;n<this.children.length;n++){const s=this.children[n];s instanceof P&&s.symbol.type===t&&e.push(s)}return e}}getTypedRuleContext(t,e){return this.getChild(e,t)}getTypedRuleContexts(t){if(null===this.children)return[];{const e=[];for(let n=0;n<this.children.length;n++){const s=this.children[n];s instanceof t&&e.push(s)}return e}}getChildCount(){return null===this.children?0:this.children.length}getSourceInterval(){return null===this.start||null===this.stop?m.INVALID_INTERVAL:new m(this.start.tokenIndex,this.stop.tokenIndex)}}M.EMPTY=new Ue;class Be{static DEFAULT_PROGRAM_NAME="default";constructor(t){this.tokens=t,this.programs=new Map}getTokenStream(){return this.tokens}insertAfter(t,e){let n,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Be.DEFAULT_PROGRAM_NAME;n="number"==typeof t?t:t.tokenIndex;let i=this.getProgram(s),r=new qe(this.tokens,n,i.length,e);i.push(r)}insertBefore(t,e){let n,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Be.DEFAULT_PROGRAM_NAME;n="number"==typeof t?t:t.tokenIndex;const i=this.getProgram(s),r=new Ve(this.tokens,n,i.length,e);i.push(r)}replaceSingle(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Be.DEFAULT_PROGRAM_NAME;this.replace(t,t,e,n)}replace(t,e,n){let s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:Be.DEFAULT_PROGRAM_NAME;if("number"!=typeof t&&(t=t.tokenIndex),"number"!=typeof e&&(e=e.tokenIndex),t>e||t<0||e<0||e>=this.tokens.size)throw new RangeError(`replace: range invalid: ${t}..${e}(size=${this.tokens.size})`);let i=this.getProgram(s),r=new He(this.tokens,t,e,i.length,n);i.push(r)}delete(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Be.DEFAULT_PROGRAM_NAME;void 0===e&&(e=t),this.replace(t,e,null,n)}getProgram(t){let e=this.programs.get(t);return null==e&&(e=this.initializeProgram(t)),e}initializeProgram(t){const e=[];return this.programs.set(t,e),e}getText(t){let e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Be.DEFAULT_PROGRAM_NAME;e=t instanceof m?t:new m(0,this.tokens.size-1),"string"==typeof t&&(n=t);const i=this.programs.get(n);let r=e.start,o=e.stop;if(o>this.tokens.size-1&&(o=this.tokens.size-1),r<0&&(r=0),null==i||0===i.length)return this.tokens.getText(new m(r,o));let a=[],l=this.reduceToSingleOperationPerIndex(i),h=r;for(;h<=o&&h<this.tokens.size;){let t=l.get(h);l.delete(h);let e=this.tokens.get(h);null==t?(e.type!==s.EOF&&a.push(String(e.text)),h++):h=t.execute(a)}if(o===this.tokens.size-1)for(const t of l.values())t.index>=this.tokens.size-1&&a.push(t.text.toString());return a.join("")}reduceToSingleOperationPerIndex(t){for(let e=0;e<t.length;e++){let n=t[e];if(null==n)continue;if(!(n instanceof He))continue;let s=n,i=this.getKindOfOps(t,Ve,e);for(let e of i)e.index===s.index?(t[e.instructionIndex]=void 0,s.text=e.text.toString()+(null!=s.text?s.text.toString():"")):e.index>s.index&&e.index<=s.lastIndex&&(t[e.instructionIndex]=void 0);let r=this.getKindOfOps(t,He,e);for(let e of r){if(e.index>=s.index&&e.lastIndex<=s.lastIndex){t[e.instructionIndex]=void 0;continue}let n=e.lastIndex<s.index||e.index>s.lastIndex;if(null!=e.text||null!=s.text||n){if(!n)throw new Error(`replace op boundaries of ${s} overlap with previous ${e}`)}else t[e.instructionIndex]=void 0,s.index=Math.min(e.index,s.index),s.lastIndex=Math.max(e.lastIndex,s.lastIndex)}}for(let e=0;e<t.length;e++){let n=t[e];if(null==n)continue;if(!(n instanceof Ve))continue;let s=n,i=this.getKindOfOps(t,Ve,e);for(let e of i)e.index===s.index&&(e instanceof qe?(s.text=this.catOpText(e.text,s.text),t[e.instructionIndex]=void 0):e instanceof Ve&&(s.text=this.catOpText(s.text,e.text),t[e.instructionIndex]=void 0));let r=this.getKindOfOps(t,He,e);for(let n of r)if(s.index!==n.index){if(s.index>=n.index&&s.index<=n.lastIndex)throw new Error(`insert op ${s} within boundaries of previous ${n}`)}else n.text=this.catOpText(s.text,n.text),t[e]=void 0}let e=new Map;for(let n of t)if(null!=n){if(null!=e.get(n.index))throw new Error("should only be one op per index");e.set(n.index,n)}return e}catOpText(t,e){let n="",s="";return null!=t&&(n=t.toString()),null!=e&&(s=e.toString()),n+s}getKindOfOps(t,e,n){return t.slice(0,n).filter((t=>t&&t instanceof e))}}class ze{constructor(t,e,n,s){this.tokens=t,this.instructionIndex=n,this.index=e,this.text=void 0===s?"":s}toString(){let t=this.constructor.name;const e=t.indexOf("$");return t=t.substring(e+1,t.length),"<"+t+"@"+this.tokens.get(this.index)+':"'+this.text+'">'}}class Ve extends ze{constructor(t,e,n,s){super(t,e,n,s)}execute(t){return this.text&&t.push(this.text.toString()),this.tokens.get(this.index).type!==s.EOF&&t.push(String(this.tokens.get(this.index).text)),this.index+1}}class qe extends Ve{constructor(t,e,n,s){super(t,e+1,n,s)}}class He extends ze{constructor(t,e,n,s,i){super(t,e,s,i),this.lastIndex=n}execute(t){return this.text&&t.push(this.text.toString()),this.lastIndex+1}toString(){return null==this.text?"<DeleteOp@"+this.tokens.get(this.index)+".."+this.tokens.get(this.lastIndex)+">":"<ReplaceOp@"+this.tokens.get(this.index)+".."+this.tokens.get(this.lastIndex)+':"'+this.text+'">'}}const Ke={atn:ie,dfa:le,context:he,misc:ce,tree:pe,error:Ae,Token:s,CommonToken:vt,CharStreams:Oe,CharStream:Ne,InputStream:ke,FileStream:Le,CommonTokenStream:Pe,Lexer:Mt,Parser:De,ParserRuleContext:Ue,Interval:m,IntervalSet:E,LL1Analyzer:W,Utils:Re,TokenStreamRewriter:Be};var Ye=n.MG,Ge=n.fr,We=n.sR,je=n.Zo,$e=n.iH,Xe=n.rt,Je=n.jB,Ze=n.M8,Qe=n.$t,tn=n.aq,en=n.pG,nn=n.eP,sn=n.tf,rn=n.KU,on=n.zW,an=n.IX,ln=n.mY,hn=n.a7,cn=n.JG,un=n.ay,dn=n.X2,gn=n.WU,pn=n.Uw,fn=n.gw,xn=n.iX,Tn=n.re,Sn=n.Pg,mn=n.tD,En=n.R$,_n=n.Dj,Cn=n.m7,An=n.NZ,Nn=n.xo,kn=n.ou,In=n.qC,yn=n.mD,Ln=n.Ay;export{Ye as ATN,Ge as ATNDeserializer,We as BailErrorStrategy,je as CharStream,$e as CharStreams,Xe as CommonToken,Je as CommonTokenStream,Ze as DFA,Qe as DefaultErrorStrategy,tn as DiagnosticErrorListener,en as ErrorListener,nn as FailedPredicateException,sn as FileStream,rn as InputMismatchException,on as InputStream,an as Interval,ln as IntervalSet,hn as LL1Analyzer,cn as Lexer,un as LexerATNSimulator,dn as NoViableAltException,gn as ParseTreeListener,pn as ParseTreeVisitor,fn as ParseTreeWalker,xn as Parser,Tn as ParserATNSimulator,Sn as ParserRuleContext,mn as PredictionContextCache,En as PredictionMode,_n as RecognitionException,Cn as RuleContext,An as RuleNode,Nn as TerminalNode,kn as Token,In as TokenStreamRewriter,yn as arrayToString,Ln as default};
//# sourceMappingURL=antlr4.node.mjs.map