// Minimal Droplet Editor Implementation for VS Code Extension
// This is a simplified version that provides basic block-based editing functionality

(function() {
    'use strict';

    // Simple block-based editor implementation
    window.droplet = {
        Editor: function(container, options) {
            this.container = container;
            this.options = options || {};
            this.mode = options.mode || 'javascript';
            this.currentText = '';
            this.isBlockMode = true;
            this.eventHandlers = {};

            this.init();
        }
    };

    droplet.Editor.prototype = {
        init: function() {
            this.createUI();
            this.setupEventHandlers();
        },

        createUI: function() {
            this.container.innerHTML = `
                <div class="droplet-editor">
                    <div class="droplet-palette">
                        <div class="palette-header">Blocks</div>
                        <div class="palette-content" id="palette-content">
                            ${this.generatePaletteBlocks()}
                        </div>
                    </div>
                    <div class="droplet-workspace">
                        <div class="workspace-header">
                            <button id="toggle-mode">Switch to Text</button>
                        </div>
                        <div class="workspace-content">
                            <div class="block-area" id="block-area">
                                <div class="drop-zone">Drag blocks here to start programming</div>
                            </div>
                            <textarea class="text-area" id="text-area" style="display: none;"></textarea>
                        </div>
                    </div>
                </div>
            `;

            this.addStyles();
        },

        addStyles: function() {
            if (document.getElementById('droplet-styles')) return;

            const style = document.createElement('style');
            style.id = 'droplet-styles';
            style.textContent = `
                .droplet-editor {
                    display: flex;
                    height: 100%;
                    font-family: monospace;
                }
                .droplet-palette {
                    width: 200px;
                    background: var(--vscode-sideBar-background);
                    border-right: 1px solid var(--vscode-panel-border);
                    overflow-y: auto;
                }
                .palette-header {
                    padding: 10px;
                    background: var(--vscode-sideBarSectionHeader-background);
                    color: var(--vscode-sideBarSectionHeader-foreground);
                    font-weight: bold;
                }
                .palette-content {
                    padding: 10px;
                }
                .palette-block {
                    background: #4CAF50;
                    color: white;
                    padding: 8px;
                    margin: 5px 0;
                    border-radius: 5px;
                    cursor: grab;
                    user-select: none;
                    font-size: 12px;
                }
                .palette-block:hover {
                    background: #45a049;
                }
                .palette-block.function { background: #2196F3; }
                .palette-block.control { background: #FF9800; }
                .palette-block.output { background: #9C27B0; }
                .droplet-workspace {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                }
                .workspace-header {
                    padding: 10px;
                    background: var(--vscode-titleBar-activeBackground);
                    border-bottom: 1px solid var(--vscode-panel-border);
                }
                .workspace-content {
                    flex: 1;
                    position: relative;
                }
                .block-area {
                    height: 100%;
                    padding: 20px;
                    background: var(--vscode-editor-background);
                    overflow: auto;
                }
                .text-area {
                    width: 100%;
                    height: 100%;
                    border: none;
                    background: var(--vscode-editor-background);
                    color: var(--vscode-editor-foreground);
                    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                    font-size: 14px;
                    padding: 20px;
                    resize: none;
                    outline: none;
                }
                .drop-zone {
                    text-align: center;
                    color: var(--vscode-descriptionForeground);
                    padding: 50px;
                    border: 2px dashed var(--vscode-panel-border);
                    border-radius: 10px;
                }
                .code-block {
                    background: #4CAF50;
                    color: white;
                    padding: 10px;
                    margin: 10px 0;
                    border-radius: 5px;
                    cursor: move;
                    display: inline-block;
                    min-width: 100px;
                }
                .code-block.function { background: #2196F3; }
                .code-block.control { background: #FF9800; }
                .code-block.output { background: #9C27B0; }
            `;
            document.head.appendChild(style);
        },

        generatePaletteBlocks: function() {
            const blocks = this.getPaletteBlocks();
            return blocks.map(category => `
                <div class="palette-category">
                    <div class="category-header" style="color: ${category.color}; font-weight: bold; margin: 10px 0 5px 0;">
                        ${category.name}
                    </div>
                    ${category.blocks.map(block => `
                        <div class="palette-block ${block.type || 'variable'}" 
                             draggable="true" 
                             data-block="${block.code}">
                            ${block.title}
                        </div>
                    `).join('')}
                </div>
            `).join('');
        },

        getPaletteBlocks: function() {
            const commonBlocks = {
                javascript: [
                    {
                        name: 'Variables',
                        color: '#4CAF50',
                        blocks: [
                            { title: 'let variable = value', code: 'let variable = value', type: 'variable' },
                            { title: 'const constant = value', code: 'const constant = value', type: 'variable' }
                        ]
                    },
                    {
                        name: 'Functions',
                        color: '#2196F3',
                        blocks: [
                            { title: 'function name() { }', code: 'function name() {\n  // code here\n}', type: 'function' },
                            { title: 'call function()', code: 'functionName()', type: 'function' }
                        ]
                    },
                    {
                        name: 'Control',
                        color: '#FF9800',
                        blocks: [
                            { title: 'if (condition) { }', code: 'if (condition) {\n  // code here\n}', type: 'control' },
                            { title: 'for loop', code: 'for (let i = 0; i < 10; i++) {\n  // code here\n}', type: 'control' }
                        ]
                    },
                    {
                        name: 'Output',
                        color: '#9C27B0',
                        blocks: [
                            { title: 'console.log()', code: 'console.log("message")', type: 'output' },
                            { title: 'alert()', code: 'alert("message")', type: 'output' }
                        ]
                    }
                ],
                python: [
                    {
                        name: 'Variables',
                        color: '#4CAF50',
                        blocks: [
                            { title: 'variable = value', code: 'variable = value', type: 'variable' }
                        ]
                    },
                    {
                        name: 'Functions',
                        color: '#2196F3',
                        blocks: [
                            { title: 'def function():', code: 'def function_name():\n    # code here', type: 'function' },
                            { title: 'call function()', code: 'function_name()', type: 'function' }
                        ]
                    },
                    {
                        name: 'Control',
                        color: '#FF9800',
                        blocks: [
                            { title: 'if condition:', code: 'if condition:\n    # code here', type: 'control' },
                            { title: 'for loop', code: 'for i in range(10):\n    # code here', type: 'control' }
                        ]
                    },
                    {
                        name: 'Output',
                        color: '#9C27B0',
                        blocks: [
                            { title: 'print()', code: 'print("message")', type: 'output' }
                        ]
                    }
                ]
            };

            return commonBlocks[this.mode] || commonBlocks.javascript;
        },

        setupEventHandlers: function() {
            const self = this;
            
            // Toggle mode button
            const toggleBtn = this.container.querySelector('#toggle-mode');
            if (toggleBtn) {
                toggleBtn.addEventListener('click', function() {
                    self.toggleMode();
                });
            }

            // Drag and drop for blocks
            const paletteBlocks = this.container.querySelectorAll('.palette-block');
            paletteBlocks.forEach(block => {
                block.addEventListener('dragstart', function(e) {
                    e.dataTransfer.setData('text/plain', this.dataset.block);
                });
            });

            const blockArea = this.container.querySelector('#block-area');
            if (blockArea) {
                blockArea.addEventListener('dragover', function(e) {
                    e.preventDefault();
                });

                blockArea.addEventListener('drop', function(e) {
                    e.preventDefault();
                    const code = e.dataTransfer.getData('text/plain');
                    self.addBlock(code);
                });
            }

            // Text area changes
            const textArea = this.container.querySelector('#text-area');
            if (textArea) {
                textArea.addEventListener('input', function() {
                    self.currentText = this.value;
                    self.triggerChange();
                });
            }
        },

        addBlock: function(code) {
            const blockArea = this.container.querySelector('#block-area');
            const dropZone = blockArea.querySelector('.drop-zone');
            
            if (dropZone) {
                dropZone.style.display = 'none';
            }

            const blockElement = document.createElement('div');
            blockElement.className = 'code-block';
            blockElement.textContent = code;
            blockElement.draggable = true;
            
            blockArea.appendChild(blockElement);
            
            this.updateTextFromBlocks();
            this.triggerChange();
        },

        updateTextFromBlocks: function() {
            const blocks = this.container.querySelectorAll('.code-block');
            const codeLines = Array.from(blocks).map(block => block.textContent);
            this.currentText = codeLines.join('\n\n');
            
            const textArea = this.container.querySelector('#text-area');
            if (textArea) {
                textArea.value = this.currentText;
            }
        },

        toggleMode: function() {
            this.isBlockMode = !this.isBlockMode;
            const blockArea = this.container.querySelector('#block-area');
            const textArea = this.container.querySelector('#text-area');
            const toggleBtn = this.container.querySelector('#toggle-mode');

            if (this.isBlockMode) {
                blockArea.style.display = 'block';
                textArea.style.display = 'none';
                toggleBtn.textContent = 'Switch to Text';
                this.updateBlocksFromText();
            } else {
                blockArea.style.display = 'none';
                textArea.style.display = 'block';
                toggleBtn.textContent = 'Switch to Blocks';
                textArea.value = this.currentText;
            }

            this.triggerToggleDone();
        },

        updateBlocksFromText: function() {
            // Simple implementation - in a real editor this would parse the code
            const blockArea = this.container.querySelector('#block-area');
            const lines = this.currentText.split('\n').filter(line => line.trim());
            
            blockArea.innerHTML = '';
            
            if (lines.length === 0) {
                blockArea.innerHTML = '<div class="drop-zone">Drag blocks here to start programming</div>';
                return;
            }

            lines.forEach(line => {
                if (line.trim()) {
                    const blockElement = document.createElement('div');
                    blockElement.className = 'code-block';
                    blockElement.textContent = line.trim();
                    blockElement.draggable = true;
                    blockArea.appendChild(blockElement);
                }
            });
        },

        setValue: function(text) {
            this.currentText = text;
            const textArea = this.container.querySelector('#text-area');
            if (textArea) {
                textArea.value = text;
            }
            if (this.isBlockMode) {
                this.updateBlocksFromText();
            }
        },

        getValue: function() {
            if (!this.isBlockMode) {
                const textArea = this.container.querySelector('#text-area');
                if (textArea) {
                    this.currentText = textArea.value;
                }
            } else {
                this.updateTextFromBlocks();
            }
            return this.currentText;
        },

        setEditorState: function(useBlocks) {
            if (useBlocks !== this.isBlockMode) {
                this.toggleMode();
            }
        },

        get currentlyUsingBlocks() {
            return this.isBlockMode;
        },

        on: function(event, handler) {
            if (!this.eventHandlers[event]) {
                this.eventHandlers[event] = [];
            }
            this.eventHandlers[event].push(handler);
        },

        triggerChange: function() {
            if (this.eventHandlers.change) {
                this.eventHandlers.change.forEach(handler => handler());
            }
        },

        triggerToggleDone: function() {
            if (this.eventHandlers.toggledone) {
                this.eventHandlers.toggledone.forEach(handler => handler());
            }
        }
    };
})();
