{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AACjC,mEAAgE;AAEhE,SAAgB,QAAQ,CAAC,OAAgC;IACrD,OAAO,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAC;IAEnE,sCAAsC;IACtC,MAAM,QAAQ,GAAG,IAAI,6CAAqB,CAAC,OAAO,CAAC,CAAC;IACpD,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,MAAM,CAAC,4BAA4B,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CACzE,CAAC;IAEF,oBAAoB;IACpB,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,oBAAoB,EAAE,KAAK,IAAI,EAAE;QAC7D,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;QACpD,IAAI,CAAC,YAAY,EAAE;YACf,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,CAAC;YACzD,OAAO;SACV;QAED,MAAM,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC;QACvC,MAAM,mBAAmB,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;QACtD,MAAM,aAAa,GAAG,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAE1D,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE;YAC9C,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,0DAA0D,CAAC,CAAC;YAC3F,OAAO;SACV;QAED,2BAA2B;QAC3B,MAAM,cAAc,GAAG,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,aAAa,EAAE,UAAU,aAAa,EAAE,CAAC,CAAC;QAC3F,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;QAEnC,IAAI;YACA,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC5C,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;YACvE,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,iBAAiB,EAAE,GAAG,EAAE,gBAAgB,CAAC,CAAC;SAClF;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,iCAAiC,KAAK,EAAE,CAAC,CAAC;SAC5E;IACL,CAAC,CAAC,CACL,CAAC;IAEF,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,mBAAmB,EAAE,KAAK,IAAI,EAAE;QAC5D,MAAM,eAAe,GAAG;YACpB,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,IAAI,EAAE;YACpC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE;YAChC,EAAE,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,QAAQ,EAAE;SAC7C,CAAC;QAEF,MAAM,gBAAgB,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,eAAe,EAAE;YACxE,WAAW,EAAE,6BAA6B;SAC7C,CAAC,CAAC;QAEH,IAAI,CAAC,gBAAgB,EAAE;YACnB,OAAO;SACV;QAED,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;YAC9C,MAAM,EAAE,qCAAqC;YAC7C,WAAW,EAAE,mBAAmB;SACnC,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,EAAE;YACX,OAAO;SACV;QAED,MAAM,eAAe,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/D,IAAI,CAAC,eAAe,EAAE;YAClB,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,CAAC;YAC5D,OAAO;SACV;QAED,MAAM,YAAY,GAAG,GAAG,QAAQ,WAAW,gBAAgB,CAAC,KAAK,EAAE,CAAC;QACpE,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,eAAe,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC;QAEnE,IAAI;YACA,wCAAwC;YACxC,MAAM,QAAQ,GAAG,mBAAmB,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;YAC7D,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC;YACxE,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,iBAAiB,EAAE,GAAG,EAAE,gBAAgB,CAAC,CAAC;SAClF;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,qCAAqC,KAAK,EAAE,CAAC,CAAC;SAChF;IACL,CAAC,CAAC,CACL,CAAC;IAEF,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,oBAAoB,EAAE,GAAG,EAAE;QACvD,4DAA4D;QAC5D,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,8CAA8C,CAAC,CAAC;IACnF,CAAC,CAAC,CACL,CAAC;AACN,CAAC;AA5FD,4BA4FC;AAED,SAAgB,UAAU;IACtB,OAAO,CAAC,GAAG,CAAC,yDAAyD,CAAC,CAAC;AAC3E,CAAC;AAFD,gCAEC;AAED,SAAS,gBAAgB,CAAC,QAAgB;IACtC,MAAM,OAAO,GAAG,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;IAC1C,OAAO,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AAC7D,CAAC;AAED,SAAS,mBAAmB,CAAC,QAAgB;IACzC,QAAQ,QAAQ,EAAE;QACd,KAAK,IAAI;YACL,OAAO;;;;;;;;;kBASD,CAAC;QACX,KAAK,IAAI;YACL,OAAO;;;;;;;;iBAQF,CAAC;QACV,KAAK,QAAQ;YACT,OAAO;;;;;;;;gBAQH,CAAC;QACT;YACI,OAAO,2CAA2C,CAAC;KAC1D;AACL,CAAC"}