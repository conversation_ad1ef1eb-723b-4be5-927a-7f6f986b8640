// Generated from C.g4 by ANTLR 4.5
// jshint ignore: start
var antlr4 = require('antlr4/index');

// This class defines a complete listener for a parse tree produced by CParser.
function CListener() {
	antlr4.tree.ParseTreeListener.call(this);
	return this;
}

CListener.prototype = Object.create(antlr4.tree.ParseTreeListener.prototype);
CListener.prototype.constructor = CListener;

// Enter a parse tree produced by CParser#primaryExpression.
CListener.prototype.enterPrimaryExpression = function(ctx) {
};

// Exit a parse tree produced by CParser#primaryExpression.
CListener.prototype.exitPrimaryExpression = function(ctx) {
};


// Enter a parse tree produced by CParser#genericSelection.
CListener.prototype.enterGenericSelection = function(ctx) {
};

// Exit a parse tree produced by CParser#genericSelection.
CListener.prototype.exitGenericSelection = function(ctx) {
};


// Enter a parse tree produced by CParser#genericAssocList.
CListener.prototype.enterGenericAssocList = function(ctx) {
};

// Exit a parse tree produced by CParser#genericAssocList.
CListener.prototype.exitGenericAssocList = function(ctx) {
};


// Enter a parse tree produced by CParser#genericAssociation.
CListener.prototype.enterGenericAssociation = function(ctx) {
};

// Exit a parse tree produced by CParser#genericAssociation.
CListener.prototype.exitGenericAssociation = function(ctx) {
};


// Enter a parse tree produced by CParser#postfixExpression.
CListener.prototype.enterPostfixExpression = function(ctx) {
};

// Exit a parse tree produced by CParser#postfixExpression.
CListener.prototype.exitPostfixExpression = function(ctx) {
};


// Enter a parse tree produced by CParser#argumentExpressionList.
CListener.prototype.enterArgumentExpressionList = function(ctx) {
};

// Exit a parse tree produced by CParser#argumentExpressionList.
CListener.prototype.exitArgumentExpressionList = function(ctx) {
};


// Enter a parse tree produced by CParser#unaryExpression.
CListener.prototype.enterUnaryExpression = function(ctx) {
};

// Exit a parse tree produced by CParser#unaryExpression.
CListener.prototype.exitUnaryExpression = function(ctx) {
};


// Enter a parse tree produced by CParser#unaryOperator.
CListener.prototype.enterUnaryOperator = function(ctx) {
};

// Exit a parse tree produced by CParser#unaryOperator.
CListener.prototype.exitUnaryOperator = function(ctx) {
};


// Enter a parse tree produced by CParser#castExpression.
CListener.prototype.enterCastExpression = function(ctx) {
};

// Exit a parse tree produced by CParser#castExpression.
CListener.prototype.exitCastExpression = function(ctx) {
};


// Enter a parse tree produced by CParser#multiplicativeExpression.
CListener.prototype.enterMultiplicativeExpression = function(ctx) {
};

// Exit a parse tree produced by CParser#multiplicativeExpression.
CListener.prototype.exitMultiplicativeExpression = function(ctx) {
};


// Enter a parse tree produced by CParser#additiveExpression.
CListener.prototype.enterAdditiveExpression = function(ctx) {
};

// Exit a parse tree produced by CParser#additiveExpression.
CListener.prototype.exitAdditiveExpression = function(ctx) {
};


// Enter a parse tree produced by CParser#shiftExpression.
CListener.prototype.enterShiftExpression = function(ctx) {
};

// Exit a parse tree produced by CParser#shiftExpression.
CListener.prototype.exitShiftExpression = function(ctx) {
};


// Enter a parse tree produced by CParser#relationalExpression.
CListener.prototype.enterRelationalExpression = function(ctx) {
};

// Exit a parse tree produced by CParser#relationalExpression.
CListener.prototype.exitRelationalExpression = function(ctx) {
};


// Enter a parse tree produced by CParser#equalityExpression.
CListener.prototype.enterEqualityExpression = function(ctx) {
};

// Exit a parse tree produced by CParser#equalityExpression.
CListener.prototype.exitEqualityExpression = function(ctx) {
};


// Enter a parse tree produced by CParser#andExpression.
CListener.prototype.enterAndExpression = function(ctx) {
};

// Exit a parse tree produced by CParser#andExpression.
CListener.prototype.exitAndExpression = function(ctx) {
};


// Enter a parse tree produced by CParser#exclusiveOrExpression.
CListener.prototype.enterExclusiveOrExpression = function(ctx) {
};

// Exit a parse tree produced by CParser#exclusiveOrExpression.
CListener.prototype.exitExclusiveOrExpression = function(ctx) {
};


// Enter a parse tree produced by CParser#inclusiveOrExpression.
CListener.prototype.enterInclusiveOrExpression = function(ctx) {
};

// Exit a parse tree produced by CParser#inclusiveOrExpression.
CListener.prototype.exitInclusiveOrExpression = function(ctx) {
};


// Enter a parse tree produced by CParser#logicalAndExpression.
CListener.prototype.enterLogicalAndExpression = function(ctx) {
};

// Exit a parse tree produced by CParser#logicalAndExpression.
CListener.prototype.exitLogicalAndExpression = function(ctx) {
};


// Enter a parse tree produced by CParser#logicalOrExpression.
CListener.prototype.enterLogicalOrExpression = function(ctx) {
};

// Exit a parse tree produced by CParser#logicalOrExpression.
CListener.prototype.exitLogicalOrExpression = function(ctx) {
};


// Enter a parse tree produced by CParser#conditionalExpression.
CListener.prototype.enterConditionalExpression = function(ctx) {
};

// Exit a parse tree produced by CParser#conditionalExpression.
CListener.prototype.exitConditionalExpression = function(ctx) {
};


// Enter a parse tree produced by CParser#assignmentExpression.
CListener.prototype.enterAssignmentExpression = function(ctx) {
};

// Exit a parse tree produced by CParser#assignmentExpression.
CListener.prototype.exitAssignmentExpression = function(ctx) {
};


// Enter a parse tree produced by CParser#assignmentOperator.
CListener.prototype.enterAssignmentOperator = function(ctx) {
};

// Exit a parse tree produced by CParser#assignmentOperator.
CListener.prototype.exitAssignmentOperator = function(ctx) {
};


// Enter a parse tree produced by CParser#expression.
CListener.prototype.enterExpression = function(ctx) {
};

// Exit a parse tree produced by CParser#expression.
CListener.prototype.exitExpression = function(ctx) {
};


// Enter a parse tree produced by CParser#constantExpression.
CListener.prototype.enterConstantExpression = function(ctx) {
};

// Exit a parse tree produced by CParser#constantExpression.
CListener.prototype.exitConstantExpression = function(ctx) {
};


// Enter a parse tree produced by CParser#declaration.
CListener.prototype.enterDeclaration = function(ctx) {
};

// Exit a parse tree produced by CParser#declaration.
CListener.prototype.exitDeclaration = function(ctx) {
};


// Enter a parse tree produced by CParser#declarationSpecifiers.
CListener.prototype.enterDeclarationSpecifiers = function(ctx) {
};

// Exit a parse tree produced by CParser#declarationSpecifiers.
CListener.prototype.exitDeclarationSpecifiers = function(ctx) {
};


// Enter a parse tree produced by CParser#declarationSpecifiers2.
CListener.prototype.enterDeclarationSpecifiers2 = function(ctx) {
};

// Exit a parse tree produced by CParser#declarationSpecifiers2.
CListener.prototype.exitDeclarationSpecifiers2 = function(ctx) {
};


// Enter a parse tree produced by CParser#declarationSpecifier.
CListener.prototype.enterDeclarationSpecifier = function(ctx) {
};

// Exit a parse tree produced by CParser#declarationSpecifier.
CListener.prototype.exitDeclarationSpecifier = function(ctx) {
};


// Enter a parse tree produced by CParser#initDeclaratorList.
CListener.prototype.enterInitDeclaratorList = function(ctx) {
};

// Exit a parse tree produced by CParser#initDeclaratorList.
CListener.prototype.exitInitDeclaratorList = function(ctx) {
};


// Enter a parse tree produced by CParser#initDeclarator.
CListener.prototype.enterInitDeclarator = function(ctx) {
};

// Exit a parse tree produced by CParser#initDeclarator.
CListener.prototype.exitInitDeclarator = function(ctx) {
};


// Enter a parse tree produced by CParser#storageClassSpecifier.
CListener.prototype.enterStorageClassSpecifier = function(ctx) {
};

// Exit a parse tree produced by CParser#storageClassSpecifier.
CListener.prototype.exitStorageClassSpecifier = function(ctx) {
};


// Enter a parse tree produced by CParser#typeSpecifier.
CListener.prototype.enterTypeSpecifier = function(ctx) {
};

// Exit a parse tree produced by CParser#typeSpecifier.
CListener.prototype.exitTypeSpecifier = function(ctx) {
};


// Enter a parse tree produced by CParser#structOrUnionSpecifier.
CListener.prototype.enterStructOrUnionSpecifier = function(ctx) {
};

// Exit a parse tree produced by CParser#structOrUnionSpecifier.
CListener.prototype.exitStructOrUnionSpecifier = function(ctx) {
};


// Enter a parse tree produced by CParser#structOrUnion.
CListener.prototype.enterStructOrUnion = function(ctx) {
};

// Exit a parse tree produced by CParser#structOrUnion.
CListener.prototype.exitStructOrUnion = function(ctx) {
};


// Enter a parse tree produced by CParser#structDeclarationList.
CListener.prototype.enterStructDeclarationList = function(ctx) {
};

// Exit a parse tree produced by CParser#structDeclarationList.
CListener.prototype.exitStructDeclarationList = function(ctx) {
};


// Enter a parse tree produced by CParser#structDeclaration.
CListener.prototype.enterStructDeclaration = function(ctx) {
};

// Exit a parse tree produced by CParser#structDeclaration.
CListener.prototype.exitStructDeclaration = function(ctx) {
};


// Enter a parse tree produced by CParser#specifierQualifierList.
CListener.prototype.enterSpecifierQualifierList = function(ctx) {
};

// Exit a parse tree produced by CParser#specifierQualifierList.
CListener.prototype.exitSpecifierQualifierList = function(ctx) {
};


// Enter a parse tree produced by CParser#structDeclaratorList.
CListener.prototype.enterStructDeclaratorList = function(ctx) {
};

// Exit a parse tree produced by CParser#structDeclaratorList.
CListener.prototype.exitStructDeclaratorList = function(ctx) {
};


// Enter a parse tree produced by CParser#structDeclarator.
CListener.prototype.enterStructDeclarator = function(ctx) {
};

// Exit a parse tree produced by CParser#structDeclarator.
CListener.prototype.exitStructDeclarator = function(ctx) {
};


// Enter a parse tree produced by CParser#enumSpecifier.
CListener.prototype.enterEnumSpecifier = function(ctx) {
};

// Exit a parse tree produced by CParser#enumSpecifier.
CListener.prototype.exitEnumSpecifier = function(ctx) {
};


// Enter a parse tree produced by CParser#enumeratorList.
CListener.prototype.enterEnumeratorList = function(ctx) {
};

// Exit a parse tree produced by CParser#enumeratorList.
CListener.prototype.exitEnumeratorList = function(ctx) {
};


// Enter a parse tree produced by CParser#enumerator.
CListener.prototype.enterEnumerator = function(ctx) {
};

// Exit a parse tree produced by CParser#enumerator.
CListener.prototype.exitEnumerator = function(ctx) {
};


// Enter a parse tree produced by CParser#enumerationConstant.
CListener.prototype.enterEnumerationConstant = function(ctx) {
};

// Exit a parse tree produced by CParser#enumerationConstant.
CListener.prototype.exitEnumerationConstant = function(ctx) {
};


// Enter a parse tree produced by CParser#atomicTypeSpecifier.
CListener.prototype.enterAtomicTypeSpecifier = function(ctx) {
};

// Exit a parse tree produced by CParser#atomicTypeSpecifier.
CListener.prototype.exitAtomicTypeSpecifier = function(ctx) {
};


// Enter a parse tree produced by CParser#typeQualifier.
CListener.prototype.enterTypeQualifier = function(ctx) {
};

// Exit a parse tree produced by CParser#typeQualifier.
CListener.prototype.exitTypeQualifier = function(ctx) {
};


// Enter a parse tree produced by CParser#functionSpecifier.
CListener.prototype.enterFunctionSpecifier = function(ctx) {
};

// Exit a parse tree produced by CParser#functionSpecifier.
CListener.prototype.exitFunctionSpecifier = function(ctx) {
};


// Enter a parse tree produced by CParser#alignmentSpecifier.
CListener.prototype.enterAlignmentSpecifier = function(ctx) {
};

// Exit a parse tree produced by CParser#alignmentSpecifier.
CListener.prototype.exitAlignmentSpecifier = function(ctx) {
};


// Enter a parse tree produced by CParser#declarator.
CListener.prototype.enterDeclarator = function(ctx) {
};

// Exit a parse tree produced by CParser#declarator.
CListener.prototype.exitDeclarator = function(ctx) {
};


// Enter a parse tree produced by CParser#directDeclarator.
CListener.prototype.enterDirectDeclarator = function(ctx) {
};

// Exit a parse tree produced by CParser#directDeclarator.
CListener.prototype.exitDirectDeclarator = function(ctx) {
};


// Enter a parse tree produced by CParser#gccDeclaratorExtension.
CListener.prototype.enterGccDeclaratorExtension = function(ctx) {
};

// Exit a parse tree produced by CParser#gccDeclaratorExtension.
CListener.prototype.exitGccDeclaratorExtension = function(ctx) {
};


// Enter a parse tree produced by CParser#gccAttributeSpecifier.
CListener.prototype.enterGccAttributeSpecifier = function(ctx) {
};

// Exit a parse tree produced by CParser#gccAttributeSpecifier.
CListener.prototype.exitGccAttributeSpecifier = function(ctx) {
};


// Enter a parse tree produced by CParser#gccAttributeList.
CListener.prototype.enterGccAttributeList = function(ctx) {
};

// Exit a parse tree produced by CParser#gccAttributeList.
CListener.prototype.exitGccAttributeList = function(ctx) {
};


// Enter a parse tree produced by CParser#gccAttribute.
CListener.prototype.enterGccAttribute = function(ctx) {
};

// Exit a parse tree produced by CParser#gccAttribute.
CListener.prototype.exitGccAttribute = function(ctx) {
};


// Enter a parse tree produced by CParser#nestedParenthesesBlock.
CListener.prototype.enterNestedParenthesesBlock = function(ctx) {
};

// Exit a parse tree produced by CParser#nestedParenthesesBlock.
CListener.prototype.exitNestedParenthesesBlock = function(ctx) {
};


// Enter a parse tree produced by CParser#pointer.
CListener.prototype.enterPointer = function(ctx) {
};

// Exit a parse tree produced by CParser#pointer.
CListener.prototype.exitPointer = function(ctx) {
};


// Enter a parse tree produced by CParser#typeQualifierList.
CListener.prototype.enterTypeQualifierList = function(ctx) {
};

// Exit a parse tree produced by CParser#typeQualifierList.
CListener.prototype.exitTypeQualifierList = function(ctx) {
};


// Enter a parse tree produced by CParser#parameterTypeList.
CListener.prototype.enterParameterTypeList = function(ctx) {
};

// Exit a parse tree produced by CParser#parameterTypeList.
CListener.prototype.exitParameterTypeList = function(ctx) {
};


// Enter a parse tree produced by CParser#parameterList.
CListener.prototype.enterParameterList = function(ctx) {
};

// Exit a parse tree produced by CParser#parameterList.
CListener.prototype.exitParameterList = function(ctx) {
};


// Enter a parse tree produced by CParser#parameterDeclaration.
CListener.prototype.enterParameterDeclaration = function(ctx) {
};

// Exit a parse tree produced by CParser#parameterDeclaration.
CListener.prototype.exitParameterDeclaration = function(ctx) {
};


// Enter a parse tree produced by CParser#identifierList.
CListener.prototype.enterIdentifierList = function(ctx) {
};

// Exit a parse tree produced by CParser#identifierList.
CListener.prototype.exitIdentifierList = function(ctx) {
};


// Enter a parse tree produced by CParser#typeName.
CListener.prototype.enterTypeName = function(ctx) {
};

// Exit a parse tree produced by CParser#typeName.
CListener.prototype.exitTypeName = function(ctx) {
};


// Enter a parse tree produced by CParser#abstractDeclarator.
CListener.prototype.enterAbstractDeclarator = function(ctx) {
};

// Exit a parse tree produced by CParser#abstractDeclarator.
CListener.prototype.exitAbstractDeclarator = function(ctx) {
};


// Enter a parse tree produced by CParser#directAbstractDeclarator.
CListener.prototype.enterDirectAbstractDeclarator = function(ctx) {
};

// Exit a parse tree produced by CParser#directAbstractDeclarator.
CListener.prototype.exitDirectAbstractDeclarator = function(ctx) {
};


// Enter a parse tree produced by CParser#typedefName.
CListener.prototype.enterTypedefName = function(ctx) {
};

// Exit a parse tree produced by CParser#typedefName.
CListener.prototype.exitTypedefName = function(ctx) {
};


// Enter a parse tree produced by CParser#initializer.
CListener.prototype.enterInitializer = function(ctx) {
};

// Exit a parse tree produced by CParser#initializer.
CListener.prototype.exitInitializer = function(ctx) {
};


// Enter a parse tree produced by CParser#initializerList.
CListener.prototype.enterInitializerList = function(ctx) {
};

// Exit a parse tree produced by CParser#initializerList.
CListener.prototype.exitInitializerList = function(ctx) {
};


// Enter a parse tree produced by CParser#designation.
CListener.prototype.enterDesignation = function(ctx) {
};

// Exit a parse tree produced by CParser#designation.
CListener.prototype.exitDesignation = function(ctx) {
};


// Enter a parse tree produced by CParser#designatorList.
CListener.prototype.enterDesignatorList = function(ctx) {
};

// Exit a parse tree produced by CParser#designatorList.
CListener.prototype.exitDesignatorList = function(ctx) {
};


// Enter a parse tree produced by CParser#designator.
CListener.prototype.enterDesignator = function(ctx) {
};

// Exit a parse tree produced by CParser#designator.
CListener.prototype.exitDesignator = function(ctx) {
};


// Enter a parse tree produced by CParser#staticAssertDeclaration.
CListener.prototype.enterStaticAssertDeclaration = function(ctx) {
};

// Exit a parse tree produced by CParser#staticAssertDeclaration.
CListener.prototype.exitStaticAssertDeclaration = function(ctx) {
};


// Enter a parse tree produced by CParser#statement.
CListener.prototype.enterStatement = function(ctx) {
};

// Exit a parse tree produced by CParser#statement.
CListener.prototype.exitStatement = function(ctx) {
};


// Enter a parse tree produced by CParser#labeledStatement.
CListener.prototype.enterLabeledStatement = function(ctx) {
};

// Exit a parse tree produced by CParser#labeledStatement.
CListener.prototype.exitLabeledStatement = function(ctx) {
};


// Enter a parse tree produced by CParser#compoundStatement.
CListener.prototype.enterCompoundStatement = function(ctx) {
};

// Exit a parse tree produced by CParser#compoundStatement.
CListener.prototype.exitCompoundStatement = function(ctx) {
};


// Enter a parse tree produced by CParser#blockItemList.
CListener.prototype.enterBlockItemList = function(ctx) {
};

// Exit a parse tree produced by CParser#blockItemList.
CListener.prototype.exitBlockItemList = function(ctx) {
};


// Enter a parse tree produced by CParser#blockItem.
CListener.prototype.enterBlockItem = function(ctx) {
};

// Exit a parse tree produced by CParser#blockItem.
CListener.prototype.exitBlockItem = function(ctx) {
};


// Enter a parse tree produced by CParser#expressionStatement.
CListener.prototype.enterExpressionStatement = function(ctx) {
};

// Exit a parse tree produced by CParser#expressionStatement.
CListener.prototype.exitExpressionStatement = function(ctx) {
};


// Enter a parse tree produced by CParser#selectionStatement.
CListener.prototype.enterSelectionStatement = function(ctx) {
};

// Exit a parse tree produced by CParser#selectionStatement.
CListener.prototype.exitSelectionStatement = function(ctx) {
};


// Enter a parse tree produced by CParser#iterationStatement.
CListener.prototype.enterIterationStatement = function(ctx) {
};

// Exit a parse tree produced by CParser#iterationStatement.
CListener.prototype.exitIterationStatement = function(ctx) {
};


// Enter a parse tree produced by CParser#jumpStatement.
CListener.prototype.enterJumpStatement = function(ctx) {
};

// Exit a parse tree produced by CParser#jumpStatement.
CListener.prototype.exitJumpStatement = function(ctx) {
};


// Enter a parse tree produced by CParser#compilationUnit.
CListener.prototype.enterCompilationUnit = function(ctx) {
};

// Exit a parse tree produced by CParser#compilationUnit.
CListener.prototype.exitCompilationUnit = function(ctx) {
};


// Enter a parse tree produced by CParser#translationUnit.
CListener.prototype.enterTranslationUnit = function(ctx) {
};

// Exit a parse tree produced by CParser#translationUnit.
CListener.prototype.exitTranslationUnit = function(ctx) {
};


// Enter a parse tree produced by CParser#externalDeclaration.
CListener.prototype.enterExternalDeclaration = function(ctx) {
};

// Exit a parse tree produced by CParser#externalDeclaration.
CListener.prototype.exitExternalDeclaration = function(ctx) {
};


// Enter a parse tree produced by CParser#functionDefinition.
CListener.prototype.enterFunctionDefinition = function(ctx) {
};

// Exit a parse tree produced by CParser#functionDefinition.
CListener.prototype.exitFunctionDefinition = function(ctx) {
};


// Enter a parse tree produced by CParser#declarationList.
CListener.prototype.enterDeclarationList = function(ctx) {
};

// Exit a parse tree produced by CParser#declarationList.
CListener.prototype.exitDeclarationList = function(ctx) {
};



exports.CListener = CListener;