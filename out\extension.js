"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deactivate = exports.activate = void 0;
const vscode = require("vscode");
const dropletEditorProvider_1 = require("./dropletEditorProvider");
function activate(context) {
    console.log('Droplet Visual Programming extension is now active!');
    // Register the custom editor provider
    const provider = new dropletEditorProvider_1.DropletEditorProvider(context);
    context.subscriptions.push(vscode.window.registerCustomEditorProvider('droplet.editor', provider));
    // Register commands
    context.subscriptions.push(vscode.commands.registerCommand('droplet.openEditor', async () => {
        const activeEditor = vscode.window.activeTextEditor;
        if (!activeEditor) {
            vscode.window.showErrorMessage('No active editor found');
            return;
        }
        const document = activeEditor.document;
        const supportedExtensions = ['.js', '.py', '.coffee'];
        const fileExtension = getFileExtension(document.fileName);
        if (!supportedExtensions.includes(fileExtension)) {
            vscode.window.showErrorMessage('Droplet editor only supports .js, .py, and .coffee files');
            return;
        }
        // Create a new visual file
        const visualFileName = document.fileName.replace(fileExtension, `.visual${fileExtension}`);
        const content = document.getText();
        try {
            const uri = vscode.Uri.file(visualFileName);
            await vscode.workspace.fs.writeFile(uri, Buffer.from(content, 'utf8'));
            await vscode.commands.executeCommand('vscode.openWith', uri, 'droplet.editor');
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to create visual file: ${error}`);
        }
    }));
    context.subscriptions.push(vscode.commands.registerCommand('droplet.createNew', async () => {
        const languageOptions = [
            { label: 'JavaScript', value: 'js' },
            { label: 'Python', value: 'py' },
            { label: 'CoffeeScript', value: 'coffee' }
        ];
        const selectedLanguage = await vscode.window.showQuickPick(languageOptions, {
            placeHolder: 'Select programming language'
        });
        if (!selectedLanguage) {
            return;
        }
        const fileName = await vscode.window.showInputBox({
            prompt: 'Enter file name (without extension)',
            placeHolder: 'my-visual-program'
        });
        if (!fileName) {
            return;
        }
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            vscode.window.showErrorMessage('No workspace folder found');
            return;
        }
        const fullFileName = `${fileName}.visual.${selectedLanguage.value}`;
        const uri = vscode.Uri.joinPath(workspaceFolder.uri, fullFileName);
        try {
            // Create empty file with basic template
            const template = getLanguageTemplate(selectedLanguage.value);
            await vscode.workspace.fs.writeFile(uri, Buffer.from(template, 'utf8'));
            await vscode.commands.executeCommand('vscode.openWith', uri, 'droplet.editor');
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to create new visual file: ${error}`);
        }
    }));
    context.subscriptions.push(vscode.commands.registerCommand('droplet.toggleMode', () => {
        // This command will be handled by the DropletEditorProvider
        vscode.commands.executeCommand('workbench.action.webview.reloadWebviewAction');
    }));
}
exports.activate = activate;
function deactivate() {
    console.log('Droplet Visual Programming extension is now deactivated');
}
exports.deactivate = deactivate;
function getFileExtension(fileName) {
    const lastDot = fileName.lastIndexOf('.');
    return lastDot !== -1 ? fileName.substring(lastDot) : '';
}
function getLanguageTemplate(language) {
    switch (language) {
        case 'js':
            return `// Welcome to Droplet Visual Programming!
// This is a JavaScript file that can be edited visually

console.log("Hello, World!");

function greet(name) {
    return "Hello, " + name + "!";
}

greet("Droplet");`;
        case 'py':
            return `# Welcome to Droplet Visual Programming!
# This is a Python file that can be edited visually

print("Hello, World!")

def greet(name):
    return f"Hello, {name}!"

greet("Droplet")`;
        case 'coffee':
            return `# Welcome to Droplet Visual Programming!
# This is a CoffeeScript file that can be edited visually

console.log "Hello, World!"

greet = (name) ->
  "Hello, #{name}!"

greet "Droplet"`;
        default:
            return '// Welcome to Droplet Visual Programming!';
    }
}
//# sourceMappingURL=extension.js.map