(function(f){if(typeof exports==="object"&&typeof module!=="undefined"){module.exports=f()}else if(typeof define==="function"&&define.amd){define([],f)}else{var g;if(typeof window!=="undefined"){g=window}else if(typeof global!=="undefined"){g=global}else if(typeof self!=="undefined"){g=self}else{g=this}g.skulptparser = f()}})(function(){var define,module,exports;return (function e(t,n,r){function s(o,u){if(!n[o]){if(!t[o]){var a=typeof require=="function"&&require;if(!u&&a)return a(o,!0);if(i)return i(o,!0);var f=new Error("Cannot find module '"+o+"'");throw f.code="MODULE_NOT_FOUND",f}var l=n[o]={exports:{}};t[o][0].call(l.exports,function(e){var n=t[o][1][e];return s(n?n:e)},l,l.exports,e,t,n,r)}return n[o].exports}var i=typeof require=="function"&&require;for(var o=0;o<r.length;o++)s(r[o]);return s})({1:[function(require,module,exports){
// generated by pgen/main.py
var Tokenizer = require('../src/tokenize');

exports.OpMap = {
"(": Tokenizer.Tokens.T_LPAR,
")": Tokenizer.Tokens.T_RPAR,
"[": Tokenizer.Tokens.T_LSQB,
"]": Tokenizer.Tokens.T_RSQB,
":": Tokenizer.Tokens.T_COLON,
",": Tokenizer.Tokens.T_COMMA,
";": Tokenizer.Tokens.T_SEMI,
"+": Tokenizer.Tokens.T_PLUS,
"-": Tokenizer.Tokens.T_MINUS,
"*": Tokenizer.Tokens.T_STAR,
"/": Tokenizer.Tokens.T_SLASH,
"|": Tokenizer.Tokens.T_VBAR,
"&": Tokenizer.Tokens.T_AMPER,
"<": Tokenizer.Tokens.T_LESS,
">": Tokenizer.Tokens.T_GREATER,
"=": Tokenizer.Tokens.T_EQUAL,
".": Tokenizer.Tokens.T_DOT,
"%": Tokenizer.Tokens.T_PERCENT,
"`": Tokenizer.Tokens.T_BACKQUOTE,
"{": Tokenizer.Tokens.T_LBRACE,
"}": Tokenizer.Tokens.T_RBRACE,
"@": Tokenizer.Tokens.T_AT,
"==": Tokenizer.Tokens.T_EQEQUAL,
"!=": Tokenizer.Tokens.T_NOTEQUAL,
"<>": Tokenizer.Tokens.T_NOTEQUAL,
"<=": Tokenizer.Tokens.T_LESSEQUAL,
">=": Tokenizer.Tokens.T_GREATEREQUAL,
"~": Tokenizer.Tokens.T_TILDE,
"^": Tokenizer.Tokens.T_CIRCUMFLEX,
"<<": Tokenizer.Tokens.T_LEFTSHIFT,
">>": Tokenizer.Tokens.T_RIGHTSHIFT,
"**": Tokenizer.Tokens.T_DOUBLESTAR,
"+=": Tokenizer.Tokens.T_PLUSEQUAL,
"-=": Tokenizer.Tokens.T_MINEQUAL,
"*=": Tokenizer.Tokens.T_STAREQUAL,
"/=": Tokenizer.Tokens.T_SLASHEQUAL,
"%=": Tokenizer.Tokens.T_PERCENTEQUAL,
"&=": Tokenizer.Tokens.T_AMPEREQUAL,
"|=": Tokenizer.Tokens.T_VBAREQUAL,
"^=": Tokenizer.Tokens.T_CIRCUMFLEXEQUAL,
"<<=": Tokenizer.Tokens.T_LEFTSHIFTEQUAL,
">>=": Tokenizer.Tokens.T_RIGHTSHIFTEQUAL,
"**=": Tokenizer.Tokens.T_DOUBLESTAREQUAL,
"//": Tokenizer.Tokens.T_DOUBLESLASH,
"//=": Tokenizer.Tokens.T_DOUBLESLASHEQUAL,
"->": Tokenizer.Tokens.T_RARROW
};
exports.ParseTables = {
sym:
{and_expr: 257,
 and_test: 258,
 arglist: 259,
 argument: 260,
 arith_expr: 261,
 assert_stmt: 262,
 atom: 263,
 augassign: 264,
 break_stmt: 265,
 classdef: 266,
 comp_for: 267,
 comp_if: 268,
 comp_iter: 269,
 comp_op: 270,
 comparison: 271,
 compound_stmt: 272,
 continue_stmt: 273,
 debugger_stmt: 274,
 decorated: 275,
 decorator: 276,
 decorators: 277,
 del_stmt: 278,
 dictorsetmaker: 279,
 dotted_as_name: 280,
 dotted_as_names: 281,
 dotted_name: 282,
 encoding_decl: 283,
 eval_input: 284,
 except_clause: 285,
 exec_stmt: 286,
 expr: 287,
 expr_stmt: 288,
 exprlist: 289,
 factor: 290,
 file_input: 291,
 flow_stmt: 292,
 for_stmt: 293,
 fpdef: 294,
 fplist: 295,
 funcdef: 296,
 global_stmt: 297,
 if_stmt: 298,
 import_as_name: 299,
 import_as_names: 300,
 import_from: 301,
 import_name: 302,
 import_stmt: 303,
 lambdef: 304,
 list_for: 305,
 list_if: 306,
 list_iter: 307,
 listmaker: 308,
 not_test: 309,
 old_lambdef: 310,
 old_test: 311,
 or_test: 312,
 parameters: 313,
 pass_stmt: 314,
 power: 315,
 print_stmt: 316,
 raise_stmt: 317,
 return_stmt: 318,
 shift_expr: 319,
 simple_stmt: 320,
 single_input: 256,
 sliceop: 321,
 small_stmt: 322,
 stmt: 323,
 subscript: 324,
 subscriptlist: 325,
 suite: 326,
 term: 327,
 test: 328,
 testlist: 329,
 testlist1: 330,
 testlist_comp: 331,
 testlist_safe: 332,
 trailer: 333,
 try_stmt: 334,
 varargslist: 335,
 while_stmt: 336,
 with_item: 337,
 with_stmt: 338,
 xor_expr: 339,
 yield_expr: 340,
 yield_stmt: 341},
number2symbol:
{256: 'single_input',
 257: 'and_expr',
 258: 'and_test',
 259: 'arglist',
 260: 'argument',
 261: 'arith_expr',
 262: 'assert_stmt',
 263: 'atom',
 264: 'augassign',
 265: 'break_stmt',
 266: 'classdef',
 267: 'comp_for',
 268: 'comp_if',
 269: 'comp_iter',
 270: 'comp_op',
 271: 'comparison',
 272: 'compound_stmt',
 273: 'continue_stmt',
 274: 'debugger_stmt',
 275: 'decorated',
 276: 'decorator',
 277: 'decorators',
 278: 'del_stmt',
 279: 'dictorsetmaker',
 280: 'dotted_as_name',
 281: 'dotted_as_names',
 282: 'dotted_name',
 283: 'encoding_decl',
 284: 'eval_input',
 285: 'except_clause',
 286: 'exec_stmt',
 287: 'expr',
 288: 'expr_stmt',
 289: 'exprlist',
 290: 'factor',
 291: 'file_input',
 292: 'flow_stmt',
 293: 'for_stmt',
 294: 'fpdef',
 295: 'fplist',
 296: 'funcdef',
 297: 'global_stmt',
 298: 'if_stmt',
 299: 'import_as_name',
 300: 'import_as_names',
 301: 'import_from',
 302: 'import_name',
 303: 'import_stmt',
 304: 'lambdef',
 305: 'list_for',
 306: 'list_if',
 307: 'list_iter',
 308: 'listmaker',
 309: 'not_test',
 310: 'old_lambdef',
 311: 'old_test',
 312: 'or_test',
 313: 'parameters',
 314: 'pass_stmt',
 315: 'power',
 316: 'print_stmt',
 317: 'raise_stmt',
 318: 'return_stmt',
 319: 'shift_expr',
 320: 'simple_stmt',
 321: 'sliceop',
 322: 'small_stmt',
 323: 'stmt',
 324: 'subscript',
 325: 'subscriptlist',
 326: 'suite',
 327: 'term',
 328: 'test',
 329: 'testlist',
 330: 'testlist1',
 331: 'testlist_comp',
 332: 'testlist_safe',
 333: 'trailer',
 334: 'try_stmt',
 335: 'varargslist',
 336: 'while_stmt',
 337: 'with_item',
 338: 'with_stmt',
 339: 'xor_expr',
 340: 'yield_expr',
 341: 'yield_stmt'},
dfas:
{256: [[[[1, 1], [2, 1], [3, 2]], [[0, 1]], [[2, 1]]],
       {2: 1,
        4: 1,
        5: 1,
        6: 1,
        7: 1,
        8: 1,
        9: 1,
        10: 1,
        11: 1,
        12: 1,
        13: 1,
        14: 1,
        15: 1,
        16: 1,
        17: 1,
        18: 1,
        19: 1,
        20: 1,
        21: 1,
        22: 1,
        23: 1,
        24: 1,
        25: 1,
        26: 1,
        27: 1,
        28: 1,
        29: 1,
        30: 1,
        31: 1,
        32: 1,
        33: 1,
        34: 1,
        35: 1,
        36: 1,
        37: 1}],
 257: [[[[38, 1]], [[39, 0], [0, 1]]],
       {6: 1, 8: 1, 9: 1, 14: 1, 15: 1, 19: 1, 22: 1, 26: 1, 30: 1, 37: 1}],
 258: [[[[40, 1]], [[41, 0], [0, 1]]],
       {6: 1,
        7: 1,
        8: 1,
        9: 1,
        14: 1,
        15: 1,
        19: 1,
        22: 1,
        26: 1,
        30: 1,
        37: 1}],
 259: [[[[42, 1], [43, 2], [44, 3]],
        [[45, 4]],
        [[46, 5], [0, 2]],
        [[45, 6]],
        [[46, 7], [0, 4]],
        [[42, 1], [43, 2], [44, 3], [0, 5]],
        [[0, 6]],
        [[43, 4], [44, 3]]],
       {6: 1,
        7: 1,
        8: 1,
        9: 1,
        11: 1,
        14: 1,
        15: 1,
        19: 1,
        22: 1,
        26: 1,
        30: 1,
        37: 1,
        42: 1,
        44: 1}],
 260: [[[[45, 1]], [[47, 2], [48, 3], [0, 1]], [[45, 3]], [[0, 3]]],
       {6: 1,
        7: 1,
        8: 1,
        9: 1,
        11: 1,
        14: 1,
        15: 1,
        19: 1,
        22: 1,
        26: 1,
        30: 1,
        37: 1}],
 261: [[[[49, 1]], [[26, 0], [37, 0], [0, 1]]],
       {6: 1, 8: 1, 9: 1, 14: 1, 15: 1, 19: 1, 22: 1, 26: 1, 30: 1, 37: 1}],
 262: [[[[21, 1]], [[45, 2]], [[46, 3], [0, 2]], [[45, 4]], [[0, 4]]],
       {21: 1}],
 263: [[[[19, 1], [8, 2], [9, 5], [30, 4], [14, 3], [15, 6], [22, 2]],
        [[19, 1], [0, 1]],
        [[0, 2]],
        [[50, 7], [51, 2]],
        [[52, 2], [53, 8], [54, 8]],
        [[55, 2], [56, 9]],
        [[57, 10]],
        [[51, 2]],
        [[52, 2]],
        [[55, 2]],
        [[15, 2]]],
       {8: 1, 9: 1, 14: 1, 15: 1, 19: 1, 22: 1, 30: 1}],
 264: [[[[58, 1],
         [59, 1],
         [60, 1],
         [61, 1],
         [62, 1],
         [63, 1],
         [64, 1],
         [65, 1],
         [66, 1],
         [67, 1],
         [68, 1],
         [69, 1]],
        [[0, 1]]],
       {58: 1,
        59: 1,
        60: 1,
        61: 1,
        62: 1,
        63: 1,
        64: 1,
        65: 1,
        66: 1,
        67: 1,
        68: 1,
        69: 1}],
 265: [[[[33, 1]], [[0, 1]]], {33: 1}],
 266: [[[[10, 1]],
        [[22, 2]],
        [[70, 3], [30, 4]],
        [[71, 5]],
        [[52, 6], [72, 7]],
        [[0, 5]],
        [[70, 3]],
        [[52, 6]]],
       {10: 1}],
 267: [[[[29, 1]],
        [[73, 2]],
        [[74, 3]],
        [[75, 4]],
        [[76, 5], [0, 4]],
        [[0, 5]]],
       {29: 1}],
 268: [[[[32, 1]], [[77, 2]], [[76, 3], [0, 2]], [[0, 3]]], {32: 1}],
 269: [[[[78, 1], [48, 1]], [[0, 1]]], {29: 1, 32: 1}],
 270: [[[[79, 1],
         [80, 1],
         [7, 2],
         [81, 1],
         [79, 1],
         [74, 1],
         [82, 1],
         [83, 3],
         [84, 1],
         [85, 1]],
        [[0, 1]],
        [[74, 1]],
        [[7, 1], [0, 3]]],
       {7: 1, 74: 1, 79: 1, 80: 1, 81: 1, 82: 1, 83: 1, 84: 1, 85: 1}],
 271: [[[[86, 1]], [[87, 0], [0, 1]]],
       {6: 1, 8: 1, 9: 1, 14: 1, 15: 1, 19: 1, 22: 1, 26: 1, 30: 1, 37: 1}],
 272: [[[[88, 1],
         [89, 1],
         [90, 1],
         [91, 1],
         [92, 1],
         [93, 1],
         [94, 1],
         [95, 1]],
        [[0, 1]]],
       {4: 1, 10: 1, 16: 1, 18: 1, 29: 1, 32: 1, 35: 1, 36: 1}],
 273: [[[[34, 1]], [[0, 1]]], {34: 1}],
 274: [[[[13, 1]], [[0, 1]]], {13: 1}],
 275: [[[[96, 1]], [[94, 2], [91, 2]], [[0, 2]]], {35: 1}],
 276: [[[[35, 1]],
        [[97, 2]],
        [[2, 4], [30, 3]],
        [[52, 5], [98, 6]],
        [[0, 4]],
        [[2, 4]],
        [[52, 5]]],
       {35: 1}],
 277: [[[[99, 1]], [[99, 1], [0, 1]]], {35: 1}],
 278: [[[[23, 1]], [[73, 2]], [[0, 2]]], {23: 1}],
 279: [[[[45, 1]],
        [[70, 2], [48, 3], [46, 4], [0, 1]],
        [[45, 5]],
        [[0, 3]],
        [[45, 6], [0, 4]],
        [[48, 3], [46, 7], [0, 5]],
        [[46, 4], [0, 6]],
        [[45, 8], [0, 7]],
        [[70, 9]],
        [[45, 10]],
        [[46, 7], [0, 10]]],
       {6: 1,
        7: 1,
        8: 1,
        9: 1,
        11: 1,
        14: 1,
        15: 1,
        19: 1,
        22: 1,
        26: 1,
        30: 1,
        37: 1}],
 280: [[[[97, 1]], [[100, 2], [0, 1]], [[22, 3]], [[0, 3]]], {22: 1}],
 281: [[[[101, 1]], [[46, 0], [0, 1]]], {22: 1}],
 282: [[[[22, 1]], [[102, 0], [0, 1]]], {22: 1}],
 283: [[[[22, 1]], [[0, 1]]], {22: 1}],
 284: [[[[72, 1]], [[2, 1], [103, 2]], [[0, 2]]],
       {6: 1,
        7: 1,
        8: 1,
        9: 1,
        11: 1,
        14: 1,
        15: 1,
        19: 1,
        22: 1,
        26: 1,
        30: 1,
        37: 1}],
 285: [[[[104, 1]],
        [[45, 2], [0, 1]],
        [[100, 3], [46, 3], [0, 2]],
        [[45, 4]],
        [[0, 4]]],
       {104: 1}],
 286: [[[[17, 1]],
        [[86, 2]],
        [[74, 3], [0, 2]],
        [[45, 4]],
        [[46, 5], [0, 4]],
        [[45, 6]],
        [[0, 6]]],
       {17: 1}],
 287: [[[[105, 1]], [[106, 0], [0, 1]]],
       {6: 1, 8: 1, 9: 1, 14: 1, 15: 1, 19: 1, 22: 1, 26: 1, 30: 1, 37: 1}],
 288: [[[[72, 1]],
        [[107, 2], [47, 3], [0, 1]],
        [[72, 4], [53, 4]],
        [[72, 5], [53, 5]],
        [[0, 4]],
        [[47, 3], [0, 5]]],
       {6: 1,
        7: 1,
        8: 1,
        9: 1,
        11: 1,
        14: 1,
        15: 1,
        19: 1,
        22: 1,
        26: 1,
        30: 1,
        37: 1}],
 289: [[[[86, 1]], [[46, 2], [0, 1]], [[86, 1], [0, 2]]],
       {6: 1, 8: 1, 9: 1, 14: 1, 15: 1, 19: 1, 22: 1, 26: 1, 30: 1, 37: 1}],
 290: [[[[37, 2], [26, 2], [6, 2], [108, 1]], [[0, 1]], [[109, 1]]],
       {6: 1, 8: 1, 9: 1, 14: 1, 15: 1, 19: 1, 22: 1, 26: 1, 30: 1, 37: 1}],
 291: [[[[2, 0], [103, 1], [110, 0]], [[0, 1]]],
       {2: 1,
        4: 1,
        5: 1,
        6: 1,
        7: 1,
        8: 1,
        9: 1,
        10: 1,
        11: 1,
        12: 1,
        13: 1,
        14: 1,
        15: 1,
        16: 1,
        17: 1,
        18: 1,
        19: 1,
        20: 1,
        21: 1,
        22: 1,
        23: 1,
        24: 1,
        25: 1,
        26: 1,
        27: 1,
        28: 1,
        29: 1,
        30: 1,
        31: 1,
        32: 1,
        33: 1,
        34: 1,
        35: 1,
        36: 1,
        37: 1,
        103: 1}],
 292: [[[[111, 1], [112, 1], [113, 1], [114, 1], [115, 1]], [[0, 1]]],
       {5: 1, 20: 1, 27: 1, 33: 1, 34: 1}],
 293: [[[[29, 1]],
        [[73, 2]],
        [[74, 3]],
        [[72, 4]],
        [[70, 5]],
        [[71, 6]],
        [[116, 7], [0, 6]],
        [[70, 8]],
        [[71, 9]],
        [[0, 9]]],
       {29: 1}],
 294: [[[[30, 1], [22, 2]], [[117, 3]], [[0, 2]], [[52, 2]]], {22: 1, 30: 1}],
 295: [[[[118, 1]], [[46, 2], [0, 1]], [[118, 1], [0, 2]]], {22: 1, 30: 1}],
 296: [[[[4, 1]], [[22, 2]], [[119, 3]], [[70, 4]], [[71, 5]], [[0, 5]]],
       {4: 1}],
 297: [[[[28, 1]], [[22, 2]], [[46, 1], [0, 2]]], {28: 1}],
 298: [[[[32, 1]],
        [[45, 2]],
        [[70, 3]],
        [[71, 4]],
        [[116, 5], [120, 1], [0, 4]],
        [[70, 6]],
        [[71, 7]],
        [[0, 7]]],
       {32: 1}],
 299: [[[[22, 1]], [[100, 2], [0, 1]], [[22, 3]], [[0, 3]]], {22: 1}],
 300: [[[[121, 1]], [[46, 2], [0, 1]], [[121, 1], [0, 2]]], {22: 1}],
 301: [[[[31, 1]],
        [[97, 2], [102, 3]],
        [[25, 4]],
        [[97, 2], [25, 4], [102, 3]],
        [[122, 5], [42, 5], [30, 6]],
        [[0, 5]],
        [[122, 7]],
        [[52, 5]]],
       {31: 1}],
 302: [[[[25, 1]], [[123, 2]], [[0, 2]]], {25: 1}],
 303: [[[[124, 1], [125, 1]], [[0, 1]]], {25: 1, 31: 1}],
 304: [[[[11, 1]], [[70, 2], [126, 3]], [[45, 4]], [[70, 2]], [[0, 4]]],
       {11: 1}],
 305: [[[[29, 1]],
        [[73, 2]],
        [[74, 3]],
        [[127, 4]],
        [[128, 5], [0, 4]],
        [[0, 5]]],
       {29: 1}],
 306: [[[[32, 1]], [[77, 2]], [[128, 3], [0, 2]], [[0, 3]]], {32: 1}],
 307: [[[[129, 1], [130, 1]], [[0, 1]]], {29: 1, 32: 1}],
 308: [[[[45, 1]],
        [[129, 2], [46, 3], [0, 1]],
        [[0, 2]],
        [[45, 4], [0, 3]],
        [[46, 3], [0, 4]]],
       {6: 1,
        7: 1,
        8: 1,
        9: 1,
        11: 1,
        14: 1,
        15: 1,
        19: 1,
        22: 1,
        26: 1,
        30: 1,
        37: 1}],
 309: [[[[7, 1], [131, 2]], [[40, 2]], [[0, 2]]],
       {6: 1,
        7: 1,
        8: 1,
        9: 1,
        14: 1,
        15: 1,
        19: 1,
        22: 1,
        26: 1,
        30: 1,
        37: 1}],
 310: [[[[11, 1]], [[70, 2], [126, 3]], [[77, 4]], [[70, 2]], [[0, 4]]],
       {11: 1}],
 311: [[[[132, 1], [75, 1]], [[0, 1]]],
       {6: 1,
        7: 1,
        8: 1,
        9: 1,
        11: 1,
        14: 1,
        15: 1,
        19: 1,
        22: 1,
        26: 1,
        30: 1,
        37: 1}],
 312: [[[[133, 1]], [[134, 0], [0, 1]]],
       {6: 1,
        7: 1,
        8: 1,
        9: 1,
        14: 1,
        15: 1,
        19: 1,
        22: 1,
        26: 1,
        30: 1,
        37: 1}],
 313: [[[[30, 1]], [[52, 2], [126, 3]], [[0, 2]], [[52, 2]]], {30: 1}],
 314: [[[[24, 1]], [[0, 1]]], {24: 1}],
 315: [[[[135, 1]], [[44, 2], [136, 1], [0, 1]], [[109, 3]], [[0, 3]]],
       {8: 1, 9: 1, 14: 1, 15: 1, 19: 1, 22: 1, 30: 1}],
 316: [[[[12, 1]],
        [[45, 2], [137, 3], [0, 1]],
        [[46, 4], [0, 2]],
        [[45, 5]],
        [[45, 2], [0, 4]],
        [[46, 6], [0, 5]],
        [[45, 7]],
        [[46, 8], [0, 7]],
        [[45, 7], [0, 8]]],
       {12: 1}],
 317: [[[[5, 1]],
        [[45, 2], [0, 1]],
        [[46, 3], [0, 2]],
        [[45, 4]],
        [[46, 5], [0, 4]],
        [[45, 6]],
        [[0, 6]]],
       {5: 1}],
 318: [[[[20, 1]], [[72, 2], [0, 1]], [[0, 2]]], {20: 1}],
 319: [[[[138, 1]], [[139, 0], [137, 0], [0, 1]]],
       {6: 1, 8: 1, 9: 1, 14: 1, 15: 1, 19: 1, 22: 1, 26: 1, 30: 1, 37: 1}],
 320: [[[[140, 1]], [[2, 2], [141, 3]], [[0, 2]], [[140, 1], [2, 2]]],
       {5: 1,
        6: 1,
        7: 1,
        8: 1,
        9: 1,
        11: 1,
        12: 1,
        13: 1,
        14: 1,
        15: 1,
        17: 1,
        19: 1,
        20: 1,
        21: 1,
        22: 1,
        23: 1,
        24: 1,
        25: 1,
        26: 1,
        27: 1,
        28: 1,
        30: 1,
        31: 1,
        33: 1,
        34: 1,
        37: 1}],
 321: [[[[70, 1]], [[45, 2], [0, 1]], [[0, 2]]], {70: 1}],
 322: [[[[142, 1],
         [143, 1],
         [144, 1],
         [145, 1],
         [146, 1],
         [147, 1],
         [148, 1],
         [149, 1],
         [150, 1],
         [151, 1]],
        [[0, 1]]],
       {5: 1,
        6: 1,
        7: 1,
        8: 1,
        9: 1,
        11: 1,
        12: 1,
        13: 1,
        14: 1,
        15: 1,
        17: 1,
        19: 1,
        20: 1,
        21: 1,
        22: 1,
        23: 1,
        24: 1,
        25: 1,
        26: 1,
        27: 1,
        28: 1,
        30: 1,
        31: 1,
        33: 1,
        34: 1,
        37: 1}],
 323: [[[[1, 1], [3, 1]], [[0, 1]]],
       {4: 1,
        5: 1,
        6: 1,
        7: 1,
        8: 1,
        9: 1,
        10: 1,
        11: 1,
        12: 1,
        13: 1,
        14: 1,
        15: 1,
        16: 1,
        17: 1,
        18: 1,
        19: 1,
        20: 1,
        21: 1,
        22: 1,
        23: 1,
        24: 1,
        25: 1,
        26: 1,
        27: 1,
        28: 1,
        29: 1,
        30: 1,
        31: 1,
        32: 1,
        33: 1,
        34: 1,
        35: 1,
        36: 1,
        37: 1}],
 324: [[[[45, 1], [70, 2], [102, 3]],
        [[70, 2], [0, 1]],
        [[45, 4], [152, 5], [0, 2]],
        [[102, 6]],
        [[152, 5], [0, 4]],
        [[0, 5]],
        [[102, 5]]],
       {6: 1,
        7: 1,
        8: 1,
        9: 1,
        11: 1,
        14: 1,
        15: 1,
        19: 1,
        22: 1,
        26: 1,
        30: 1,
        37: 1,
        70: 1,
        102: 1}],
 325: [[[[153, 1]], [[46, 2], [0, 1]], [[153, 1], [0, 2]]],
       {6: 1,
        7: 1,
        8: 1,
        9: 1,
        11: 1,
        14: 1,
        15: 1,
        19: 1,
        22: 1,
        26: 1,
        30: 1,
        37: 1,
        70: 1,
        102: 1}],
 326: [[[[1, 1], [2, 2]],
        [[0, 1]],
        [[154, 3]],
        [[110, 4]],
        [[155, 1], [110, 4]]],
       {2: 1,
        5: 1,
        6: 1,
        7: 1,
        8: 1,
        9: 1,
        11: 1,
        12: 1,
        13: 1,
        14: 1,
        15: 1,
        17: 1,
        19: 1,
        20: 1,
        21: 1,
        22: 1,
        23: 1,
        24: 1,
        25: 1,
        26: 1,
        27: 1,
        28: 1,
        30: 1,
        31: 1,
        33: 1,
        34: 1,
        37: 1}],
 327: [[[[109, 1]], [[156, 0], [42, 0], [157, 0], [158, 0], [0, 1]]],
       {6: 1, 8: 1, 9: 1, 14: 1, 15: 1, 19: 1, 22: 1, 26: 1, 30: 1, 37: 1}],
 328: [[[[75, 1], [159, 2]],
        [[32, 3], [0, 1]],
        [[0, 2]],
        [[75, 4]],
        [[116, 5]],
        [[45, 2]]],
       {6: 1,
        7: 1,
        8: 1,
        9: 1,
        11: 1,
        14: 1,
        15: 1,
        19: 1,
        22: 1,
        26: 1,
        30: 1,
        37: 1}],
 329: [[[[45, 1]], [[46, 2], [0, 1]], [[45, 1], [0, 2]]],
       {6: 1,
        7: 1,
        8: 1,
        9: 1,
        11: 1,
        14: 1,
        15: 1,
        19: 1,
        22: 1,
        26: 1,
        30: 1,
        37: 1}],
 330: [[[[45, 1]], [[46, 0], [0, 1]]],
       {6: 1,
        7: 1,
        8: 1,
        9: 1,
        11: 1,
        14: 1,
        15: 1,
        19: 1,
        22: 1,
        26: 1,
        30: 1,
        37: 1}],
 331: [[[[45, 1]],
        [[48, 2], [46, 3], [0, 1]],
        [[0, 2]],
        [[45, 4], [0, 3]],
        [[46, 3], [0, 4]]],
       {6: 1,
        7: 1,
        8: 1,
        9: 1,
        11: 1,
        14: 1,
        15: 1,
        19: 1,
        22: 1,
        26: 1,
        30: 1,
        37: 1}],
 332: [[[[77, 1]],
        [[46, 2], [0, 1]],
        [[77, 3]],
        [[46, 4], [0, 3]],
        [[77, 3], [0, 4]]],
       {6: 1,
        7: 1,
        8: 1,
        9: 1,
        11: 1,
        14: 1,
        15: 1,
        19: 1,
        22: 1,
        26: 1,
        30: 1,
        37: 1}],
 333: [[[[30, 1], [102, 2], [14, 3]],
        [[52, 4], [98, 5]],
        [[22, 4]],
        [[160, 6]],
        [[0, 4]],
        [[52, 4]],
        [[51, 4]]],
       {14: 1, 30: 1, 102: 1}],
 334: [[[[16, 1]],
        [[70, 2]],
        [[71, 3]],
        [[161, 4], [162, 5]],
        [[70, 6]],
        [[70, 7]],
        [[71, 8]],
        [[71, 9]],
        [[161, 4], [116, 10], [162, 5], [0, 8]],
        [[0, 9]],
        [[70, 11]],
        [[71, 12]],
        [[162, 5], [0, 12]]],
       {16: 1}],
 335: [[[[42, 1], [118, 2], [44, 3]],
        [[22, 4]],
        [[47, 5], [46, 6], [0, 2]],
        [[22, 7]],
        [[46, 8], [0, 4]],
        [[45, 9]],
        [[42, 1], [118, 2], [44, 3], [0, 6]],
        [[0, 7]],
        [[44, 3]],
        [[46, 6], [0, 9]]],
       {22: 1, 30: 1, 42: 1, 44: 1}],
 336: [[[[18, 1]],
        [[45, 2]],
        [[70, 3]],
        [[71, 4]],
        [[116, 5], [0, 4]],
        [[70, 6]],
        [[71, 7]],
        [[0, 7]]],
       {18: 1}],
 337: [[[[45, 1]], [[100, 2], [0, 1]], [[86, 3]], [[0, 3]]],
       {6: 1,
        7: 1,
        8: 1,
        9: 1,
        11: 1,
        14: 1,
        15: 1,
        19: 1,
        22: 1,
        26: 1,
        30: 1,
        37: 1}],
 338: [[[[36, 1]], [[163, 2]], [[70, 3], [46, 1]], [[71, 4]], [[0, 4]]],
       {36: 1}],
 339: [[[[164, 1]], [[165, 0], [0, 1]]],
       {6: 1, 8: 1, 9: 1, 14: 1, 15: 1, 19: 1, 22: 1, 26: 1, 30: 1, 37: 1}],
 340: [[[[27, 1]], [[72, 2], [0, 1]], [[0, 2]]], {27: 1}],
 341: [[[[53, 1]], [[0, 1]]], {27: 1}]},
states:
[[[[1, 1], [2, 1], [3, 2]], [[0, 1]], [[2, 1]]],
 [[[38, 1]], [[39, 0], [0, 1]]],
 [[[40, 1]], [[41, 0], [0, 1]]],
 [[[42, 1], [43, 2], [44, 3]],
  [[45, 4]],
  [[46, 5], [0, 2]],
  [[45, 6]],
  [[46, 7], [0, 4]],
  [[42, 1], [43, 2], [44, 3], [0, 5]],
  [[0, 6]],
  [[43, 4], [44, 3]]],
 [[[45, 1]], [[47, 2], [48, 3], [0, 1]], [[45, 3]], [[0, 3]]],
 [[[49, 1]], [[26, 0], [37, 0], [0, 1]]],
 [[[21, 1]], [[45, 2]], [[46, 3], [0, 2]], [[45, 4]], [[0, 4]]],
 [[[19, 1], [8, 2], [9, 5], [30, 4], [14, 3], [15, 6], [22, 2]],
  [[19, 1], [0, 1]],
  [[0, 2]],
  [[50, 7], [51, 2]],
  [[52, 2], [53, 8], [54, 8]],
  [[55, 2], [56, 9]],
  [[57, 10]],
  [[51, 2]],
  [[52, 2]],
  [[55, 2]],
  [[15, 2]]],
 [[[58, 1],
   [59, 1],
   [60, 1],
   [61, 1],
   [62, 1],
   [63, 1],
   [64, 1],
   [65, 1],
   [66, 1],
   [67, 1],
   [68, 1],
   [69, 1]],
  [[0, 1]]],
 [[[33, 1]], [[0, 1]]],
 [[[10, 1]],
  [[22, 2]],
  [[70, 3], [30, 4]],
  [[71, 5]],
  [[52, 6], [72, 7]],
  [[0, 5]],
  [[70, 3]],
  [[52, 6]]],
 [[[29, 1]], [[73, 2]], [[74, 3]], [[75, 4]], [[76, 5], [0, 4]], [[0, 5]]],
 [[[32, 1]], [[77, 2]], [[76, 3], [0, 2]], [[0, 3]]],
 [[[78, 1], [48, 1]], [[0, 1]]],
 [[[79, 1],
   [80, 1],
   [7, 2],
   [81, 1],
   [79, 1],
   [74, 1],
   [82, 1],
   [83, 3],
   [84, 1],
   [85, 1]],
  [[0, 1]],
  [[74, 1]],
  [[7, 1], [0, 3]]],
 [[[86, 1]], [[87, 0], [0, 1]]],
 [[[88, 1], [89, 1], [90, 1], [91, 1], [92, 1], [93, 1], [94, 1], [95, 1]],
  [[0, 1]]],
 [[[34, 1]], [[0, 1]]],
 [[[13, 1]], [[0, 1]]],
 [[[96, 1]], [[94, 2], [91, 2]], [[0, 2]]],
 [[[35, 1]],
  [[97, 2]],
  [[2, 4], [30, 3]],
  [[52, 5], [98, 6]],
  [[0, 4]],
  [[2, 4]],
  [[52, 5]]],
 [[[99, 1]], [[99, 1], [0, 1]]],
 [[[23, 1]], [[73, 2]], [[0, 2]]],
 [[[45, 1]],
  [[70, 2], [48, 3], [46, 4], [0, 1]],
  [[45, 5]],
  [[0, 3]],
  [[45, 6], [0, 4]],
  [[48, 3], [46, 7], [0, 5]],
  [[46, 4], [0, 6]],
  [[45, 8], [0, 7]],
  [[70, 9]],
  [[45, 10]],
  [[46, 7], [0, 10]]],
 [[[97, 1]], [[100, 2], [0, 1]], [[22, 3]], [[0, 3]]],
 [[[101, 1]], [[46, 0], [0, 1]]],
 [[[22, 1]], [[102, 0], [0, 1]]],
 [[[22, 1]], [[0, 1]]],
 [[[72, 1]], [[2, 1], [103, 2]], [[0, 2]]],
 [[[104, 1]],
  [[45, 2], [0, 1]],
  [[100, 3], [46, 3], [0, 2]],
  [[45, 4]],
  [[0, 4]]],
 [[[17, 1]],
  [[86, 2]],
  [[74, 3], [0, 2]],
  [[45, 4]],
  [[46, 5], [0, 4]],
  [[45, 6]],
  [[0, 6]]],
 [[[105, 1]], [[106, 0], [0, 1]]],
 [[[72, 1]],
  [[107, 2], [47, 3], [0, 1]],
  [[72, 4], [53, 4]],
  [[72, 5], [53, 5]],
  [[0, 4]],
  [[47, 3], [0, 5]]],
 [[[86, 1]], [[46, 2], [0, 1]], [[86, 1], [0, 2]]],
 [[[37, 2], [26, 2], [6, 2], [108, 1]], [[0, 1]], [[109, 1]]],
 [[[2, 0], [103, 1], [110, 0]], [[0, 1]]],
 [[[111, 1], [112, 1], [113, 1], [114, 1], [115, 1]], [[0, 1]]],
 [[[29, 1]],
  [[73, 2]],
  [[74, 3]],
  [[72, 4]],
  [[70, 5]],
  [[71, 6]],
  [[116, 7], [0, 6]],
  [[70, 8]],
  [[71, 9]],
  [[0, 9]]],
 [[[30, 1], [22, 2]], [[117, 3]], [[0, 2]], [[52, 2]]],
 [[[118, 1]], [[46, 2], [0, 1]], [[118, 1], [0, 2]]],
 [[[4, 1]], [[22, 2]], [[119, 3]], [[70, 4]], [[71, 5]], [[0, 5]]],
 [[[28, 1]], [[22, 2]], [[46, 1], [0, 2]]],
 [[[32, 1]],
  [[45, 2]],
  [[70, 3]],
  [[71, 4]],
  [[116, 5], [120, 1], [0, 4]],
  [[70, 6]],
  [[71, 7]],
  [[0, 7]]],
 [[[22, 1]], [[100, 2], [0, 1]], [[22, 3]], [[0, 3]]],
 [[[121, 1]], [[46, 2], [0, 1]], [[121, 1], [0, 2]]],
 [[[31, 1]],
  [[97, 2], [102, 3]],
  [[25, 4]],
  [[97, 2], [25, 4], [102, 3]],
  [[122, 5], [42, 5], [30, 6]],
  [[0, 5]],
  [[122, 7]],
  [[52, 5]]],
 [[[25, 1]], [[123, 2]], [[0, 2]]],
 [[[124, 1], [125, 1]], [[0, 1]]],
 [[[11, 1]], [[70, 2], [126, 3]], [[45, 4]], [[70, 2]], [[0, 4]]],
 [[[29, 1]], [[73, 2]], [[74, 3]], [[127, 4]], [[128, 5], [0, 4]], [[0, 5]]],
 [[[32, 1]], [[77, 2]], [[128, 3], [0, 2]], [[0, 3]]],
 [[[129, 1], [130, 1]], [[0, 1]]],
 [[[45, 1]],
  [[129, 2], [46, 3], [0, 1]],
  [[0, 2]],
  [[45, 4], [0, 3]],
  [[46, 3], [0, 4]]],
 [[[7, 1], [131, 2]], [[40, 2]], [[0, 2]]],
 [[[11, 1]], [[70, 2], [126, 3]], [[77, 4]], [[70, 2]], [[0, 4]]],
 [[[132, 1], [75, 1]], [[0, 1]]],
 [[[133, 1]], [[134, 0], [0, 1]]],
 [[[30, 1]], [[52, 2], [126, 3]], [[0, 2]], [[52, 2]]],
 [[[24, 1]], [[0, 1]]],
 [[[135, 1]], [[44, 2], [136, 1], [0, 1]], [[109, 3]], [[0, 3]]],
 [[[12, 1]],
  [[45, 2], [137, 3], [0, 1]],
  [[46, 4], [0, 2]],
  [[45, 5]],
  [[45, 2], [0, 4]],
  [[46, 6], [0, 5]],
  [[45, 7]],
  [[46, 8], [0, 7]],
  [[45, 7], [0, 8]]],
 [[[5, 1]],
  [[45, 2], [0, 1]],
  [[46, 3], [0, 2]],
  [[45, 4]],
  [[46, 5], [0, 4]],
  [[45, 6]],
  [[0, 6]]],
 [[[20, 1]], [[72, 2], [0, 1]], [[0, 2]]],
 [[[138, 1]], [[139, 0], [137, 0], [0, 1]]],
 [[[140, 1]], [[2, 2], [141, 3]], [[0, 2]], [[140, 1], [2, 2]]],
 [[[70, 1]], [[45, 2], [0, 1]], [[0, 2]]],
 [[[142, 1],
   [143, 1],
   [144, 1],
   [145, 1],
   [146, 1],
   [147, 1],
   [148, 1],
   [149, 1],
   [150, 1],
   [151, 1]],
  [[0, 1]]],
 [[[1, 1], [3, 1]], [[0, 1]]],
 [[[45, 1], [70, 2], [102, 3]],
  [[70, 2], [0, 1]],
  [[45, 4], [152, 5], [0, 2]],
  [[102, 6]],
  [[152, 5], [0, 4]],
  [[0, 5]],
  [[102, 5]]],
 [[[153, 1]], [[46, 2], [0, 1]], [[153, 1], [0, 2]]],
 [[[1, 1], [2, 2]], [[0, 1]], [[154, 3]], [[110, 4]], [[155, 1], [110, 4]]],
 [[[109, 1]], [[156, 0], [42, 0], [157, 0], [158, 0], [0, 1]]],
 [[[75, 1], [159, 2]],
  [[32, 3], [0, 1]],
  [[0, 2]],
  [[75, 4]],
  [[116, 5]],
  [[45, 2]]],
 [[[45, 1]], [[46, 2], [0, 1]], [[45, 1], [0, 2]]],
 [[[45, 1]], [[46, 0], [0, 1]]],
 [[[45, 1]],
  [[48, 2], [46, 3], [0, 1]],
  [[0, 2]],
  [[45, 4], [0, 3]],
  [[46, 3], [0, 4]]],
 [[[77, 1]],
  [[46, 2], [0, 1]],
  [[77, 3]],
  [[46, 4], [0, 3]],
  [[77, 3], [0, 4]]],
 [[[30, 1], [102, 2], [14, 3]],
  [[52, 4], [98, 5]],
  [[22, 4]],
  [[160, 6]],
  [[0, 4]],
  [[52, 4]],
  [[51, 4]]],
 [[[16, 1]],
  [[70, 2]],
  [[71, 3]],
  [[161, 4], [162, 5]],
  [[70, 6]],
  [[70, 7]],
  [[71, 8]],
  [[71, 9]],
  [[161, 4], [116, 10], [162, 5], [0, 8]],
  [[0, 9]],
  [[70, 11]],
  [[71, 12]],
  [[162, 5], [0, 12]]],
 [[[42, 1], [118, 2], [44, 3]],
  [[22, 4]],
  [[47, 5], [46, 6], [0, 2]],
  [[22, 7]],
  [[46, 8], [0, 4]],
  [[45, 9]],
  [[42, 1], [118, 2], [44, 3], [0, 6]],
  [[0, 7]],
  [[44, 3]],
  [[46, 6], [0, 9]]],
 [[[18, 1]],
  [[45, 2]],
  [[70, 3]],
  [[71, 4]],
  [[116, 5], [0, 4]],
  [[70, 6]],
  [[71, 7]],
  [[0, 7]]],
 [[[45, 1]], [[100, 2], [0, 1]], [[86, 3]], [[0, 3]]],
 [[[36, 1]], [[163, 2]], [[70, 3], [46, 1]], [[71, 4]], [[0, 4]]],
 [[[164, 1]], [[165, 0], [0, 1]]],
 [[[27, 1]], [[72, 2], [0, 1]], [[0, 2]]],
 [[[53, 1]], [[0, 1]]]],
labels:
[[0, 'EMPTY'],
 [320, null],
 [4, null],
 [272, null],
 [1, 'def'],
 [1, 'raise'],
 [32, null],
 [1, 'not'],
 [2, null],
 [26, null],
 [1, 'class'],
 [1, 'lambda'],
 [1, 'print'],
 [1, 'debugger'],
 [9, null],
 [25, null],
 [1, 'try'],
 [1, 'exec'],
 [1, 'while'],
 [3, null],
 [1, 'return'],
 [1, 'assert'],
 [1, null],
 [1, 'del'],
 [1, 'pass'],
 [1, 'import'],
 [15, null],
 [1, 'yield'],
 [1, 'global'],
 [1, 'for'],
 [7, null],
 [1, 'from'],
 [1, 'if'],
 [1, 'break'],
 [1, 'continue'],
 [50, null],
 [1, 'with'],
 [14, null],
 [319, null],
 [19, null],
 [309, null],
 [1, 'and'],
 [16, null],
 [260, null],
 [36, null],
 [328, null],
 [12, null],
 [22, null],
 [267, null],
 [327, null],
 [308, null],
 [10, null],
 [8, null],
 [340, null],
 [331, null],
 [27, null],
 [279, null],
 [330, null],
 [46, null],
 [39, null],
 [41, null],
 [47, null],
 [42, null],
 [43, null],
 [37, null],
 [44, null],
 [49, null],
 [45, null],
 [38, null],
 [40, null],
 [11, null],
 [326, null],
 [329, null],
 [289, null],
 [1, 'in'],
 [312, null],
 [269, null],
 [311, null],
 [268, null],
 [29, null],
 [21, null],
 [28, null],
 [30, null],
 [1, 'is'],
 [31, null],
 [20, null],
 [287, null],
 [270, null],
 [334, null],
 [298, null],
 [293, null],
 [266, null],
 [338, null],
 [336, null],
 [296, null],
 [275, null],
 [277, null],
 [282, null],
 [259, null],
 [276, null],
 [1, 'as'],
 [280, null],
 [23, null],
 [0, null],
 [1, 'except'],
 [339, null],
 [18, null],
 [264, null],
 [315, null],
 [290, null],
 [323, null],
 [265, null],
 [273, null],
 [317, null],
 [318, null],
 [341, null],
 [1, 'else'],
 [295, null],
 [294, null],
 [313, null],
 [1, 'elif'],
 [299, null],
 [300, null],
 [281, null],
 [302, null],
 [301, null],
 [335, null],
 [332, null],
 [307, null],
 [305, null],
 [306, null],
 [271, null],
 [310, null],
 [258, null],
 [1, 'or'],
 [263, null],
 [333, null],
 [35, null],
 [261, null],
 [34, null],
 [322, null],
 [13, null],
 [292, null],
 [278, null],
 [288, null],
 [314, null],
 [316, null],
 [262, null],
 [286, null],
 [297, null],
 [303, null],
 [274, null],
 [321, null],
 [324, null],
 [5, null],
 [6, null],
 [48, null],
 [17, null],
 [24, null],
 [304, null],
 [325, null],
 [285, null],
 [1, 'finally'],
 [337, null],
 [257, null],
 [33, null]],
keywords:
{'and': 41,
 'as': 100,
 'assert': 21,
 'break': 33,
 'class': 10,
 'continue': 34,
 'debugger': 13,
 'def': 4,
 'del': 23,
 'elif': 120,
 'else': 116,
 'except': 104,
 'exec': 17,
 'finally': 162,
 'for': 29,
 'from': 31,
 'global': 28,
 'if': 32,
 'import': 25,
 'in': 74,
 'is': 83,
 'lambda': 11,
 'not': 7,
 'or': 134,
 'pass': 24,
 'print': 12,
 'raise': 5,
 'return': 20,
 'try': 16,
 'while': 18,
 'with': 36,
 'yield': 27},
tokens:
{0: 103,
 1: 22,
 2: 8,
 3: 19,
 4: 2,
 5: 154,
 6: 155,
 7: 30,
 8: 52,
 9: 14,
 10: 51,
 11: 70,
 12: 46,
 13: 141,
 14: 37,
 15: 26,
 16: 42,
 17: 157,
 18: 106,
 19: 39,
 20: 85,
 21: 80,
 22: 47,
 23: 102,
 24: 158,
 25: 15,
 26: 9,
 27: 55,
 28: 81,
 29: 79,
 30: 82,
 31: 84,
 32: 6,
 33: 165,
 34: 139,
 35: 137,
 36: 44,
 37: 64,
 38: 68,
 39: 59,
 40: 69,
 41: 60,
 42: 62,
 43: 63,
 44: 65,
 45: 67,
 46: 58,
 47: 61,
 48: 156,
 49: 66,
 50: 35},
start: 256
};

},{"../src/tokenize":5}],2:[function(require,module,exports){
// todo; these should all be func objects too, otherwise str() of them won't
// work, etc.

exports.range = function range (start, stop, step) {
    var ret = [];
    var i;

    exports.pyCheckArgs("range", arguments, 1, 3);
    exports.pyCheckType("start", "integer", exports.checkInt(start));
    if (stop !== undefined) {
        exports.pyCheckType("stop", "integer", exports.checkInt(stop));
    }
    if (step !== undefined) {
        exports.pyCheckType("step", "integer", exports.checkInt(step));
    }

    start = exports.asnum$(start);
    stop = exports.asnum$(stop);
    step = exports.asnum$(step);

    if ((stop === undefined) && (step === undefined)) {
        stop = start;
        start = 0;
        step = 1;
    } else if (step === undefined) {
        step = 1;
    }

    if (step === 0) {
        throw new exports.ValueError("range() step argument must not be zero");
    }

    if (step > 0) {
        for (i = start; i < stop; i += step) {
            ret.push(new exports.nmber(i, exports.nmber.int$));
        }
    } else {
        for (i = start; i > stop; i += step) {
            ret.push(new exports.nmber(i, exports.nmber.int$));
        }
    }

    return new exports.list(ret);
};

exports.asnum$ = function (a) {
    if (a === undefined) {
        return a;
    }
    if (a === null) {
        return a;
    }
    if (a.constructor === exports.none) {
        return null;
    }
    if (a.constructor === exports.bool) {
        if (a.v) {
            return 1;
        }
        return 0;
    }
    if (typeof a === "number") {
        return a;
    }
    if (typeof a === "string") {
        return a;
    }
    if (a.constructor === exports.nmber) {
        return a.v;
    }
    if (a.constructor === exports.lng) {
        if (a.cantBeInt()) {
            return a.str$(10, true);
        }
        return a.toInt$();
    }
    if (a.constructor === exports.biginteger) {
        if ((a.trueCompare(new exports.biginteger(exports.nmber.threshold$)) > 0) ||
            (a.trueCompare(new exports.biginteger(-exports.nmber.threshold$)) < 0)) {
            return a.toString();
        }
        return a.intValue();
    }

    return a;
};

exports.assk$ = function (a, b) {
    return new exports.nmber(a, b);
};
exports.asnum$nofloat = function (a) {
    var decimal;
    var mantissa;
    var expon;
    if (a === undefined) {
        return a;
    }
    if (a === null) {
        return a;
    }
    if (a.constructor === exports.none) {
        return null;
    }
    if (a.constructor === exports.bool) {
        if (a.v) {
            return 1;
        }
        return 0;
    }
    if (typeof a === "number") {
        a = a.toString();
    }
    if (a.constructor === exports.nmber) {
        a = a.v.toString();
    }
    if (a.constructor === exports.lng) {
        a = a.str$(10, true);
    }
    if (a.constructor === exports.biginteger) {
        a = a.toString();
    }

//	Sk.debugout("INITIAL: " + a);

    //	If not a float, great, just return this
    if (a.indexOf(".") < 0 && a.indexOf("e") < 0 && a.indexOf("E") < 0) {
        return a;
    }

    expon = 0;

    if (a.indexOf("e") >= 0) {
        mantissa = a.substr(0, a.indexOf("e"));
        expon = a.substr(a.indexOf("e") + 1);
    } else if (a.indexOf("E") >= 0) {
        mantissa = a.substr(0, a.indexOf("e"));
        expon = a.substr(a.indexOf("E") + 1);
    } else {
        mantissa = a;
    }

//	Sk.debugout("e:" + expon);

    expon = parseInt(expon, 10);

//	Sk.debugout("MANTISSA:" + mantissa);
//	Sk.debugout("EXPONENT:" + expon);

    decimal = mantissa.indexOf(".");

//	Sk.debugout("DECIMAL: " + decimal);

    //	Simplest case, no decimal
    if (decimal < 0) {
        if (expon >= 0) {
            // Just add more zeroes and we're done
            while (expon-- > 0) {
                mantissa += "0";
            }
            return mantissa;
        } else {
            if (mantissa.length > -expon) {
                return mantissa.substr(0, mantissa.length + expon);
            }
            else {
                return 0;
            }
        }
    }

    //	Negative exponent OR decimal (neg or pos exp)
    if (decimal === 0) {
        mantissa = mantissa.substr(1);
    }
    else if (decimal < mantissa.length) {
        mantissa = mantissa.substr(0, decimal) + mantissa.substr(decimal + 1);
    }
    else {
        mantissa = mantissa.substr(0, decimal);
    }

//	Sk.debugout("NO DECIMAL: " + mantissa);

    decimal = decimal + expon;

//	Sk.debugout("MOVE DECIM: " + decimal);

    while (decimal > mantissa.length) {
        mantissa += "0";
    }

//	Sk.debugout("PADDED    : " + mantissa);

    if (decimal <= 0) {
        mantissa = 0;
    } else {
        mantissa = mantissa.substr(0, decimal);
    }

//	Sk.debugout("LENGTH: " + mantissa.length);
//	Sk.debugout("RETURN: " + mantissa);

    return mantissa;
};

exports.round = function round (number, ndigits) {
    var result, multiplier;
    exports.pyCheckArgs("round", arguments, 1, 2);

    if (!exports.checkNumber(number)) {
        throw new exports.TypeError("a float is required");
    }

    if ((ndigits !== undefined) && !Sk.misceval.isIndex(ndigits)) {
        throw new exports.TypeError("'" + Sk.abstr.typeName(ndigits) + "' object cannot be interpreted as an index");
    }

    if (ndigits === undefined) {
        ndigits = 0;
    }

    number = exports.asnum$(number);
    ndigits = Sk.misceval.asIndex(ndigits);

    multiplier = Math.pow(10, ndigits);
    result = Math.round(number * multiplier) / multiplier;

    return new exports.nmber(result, exports.nmber.float$);
};

exports.len = function len (item) {
    exports.pyCheckArgs("len", arguments, 1, 1);

    if (item.sq$length) {
        return new exports.nmber(item.sq$length(), exports.nmber.int$);
    }

    if (item.mp$length) {
        return new exports.nmber(item.mp$length(), exports.nmber.int$);
    }

    if (item.tp$length) {
        return new exports.nmber(item.tp$length(), exports.nmber.int$);
    }

    throw new exports.TypeError("object of type '" + Sk.abstr.typeName(item) + "' has no len()");
};

exports.min = function min () {
    var i;
    var lowest;
    var args;
    exports.pyCheckArgs("min", arguments, 1);

    args = Sk.misceval.arrayFromArguments(arguments);
    lowest = args[0];

    if (lowest === undefined) {
        throw new exports.ValueError("min() arg is an empty sequence");
    }

    for (i = 1; i < args.length; ++i) {
        if (Sk.misceval.richCompareBool(args[i], lowest, "Lt")) {
            lowest = args[i];
        }
    }
    return lowest;
};

exports.max = function max () {
    var i;
    var highest;
    var args;
    exports.pyCheckArgs("max", arguments, 1);

    args = Sk.misceval.arrayFromArguments(arguments);
    highest = args[0];

    if (highest === undefined) {
        throw new exports.ValueError("max() arg is an empty sequence");
    }

    for (i = 1; i < args.length; ++i) {
        if (Sk.misceval.richCompareBool(args[i], highest, "Gt")) {
            highest = args[i];
        }
    }
    return highest;
};

exports.any = function any (iter) {
    var it, i;

    exports.pyCheckArgs("any", arguments, 1);
    if (!exports.checkIterable(iter)) {
        throw new exports.TypeError("'" + Sk.abstr.typeName(iter) +
            "' object is not iterable");
    }

    it = iter.tp$iter();
    for (i = it.tp$iternext(); i !== undefined; i = it.tp$iternext()) {
        if (Sk.misceval.isTrue(i)) {
            return exports.bool.true$;
        }
    }

    return exports.bool.false$;
};

exports.all = function all (iter) {
    var it, i;

    exports.pyCheckArgs("all", arguments, 1);
    if (!exports.checkIterable(iter)) {
        throw new exports.TypeError("'" + Sk.abstr.typeName(iter) +
            "' object is not iterable");
    }

    it = iter.tp$iter();
    for (i = it.tp$iternext(); i !== undefined; i = it.tp$iternext()) {
        if (!Sk.misceval.isTrue(i)) {
            return exports.bool.false$;
        }
    }

    return exports.bool.true$;
};

exports.sum = function sum (iter, start) {
    var tot;
    var it, i;
    var has_float;

    exports.pyCheckArgs("sum", arguments, 1, 2);
    exports.pyCheckType("iter", "iterable", exports.checkIterable(iter));
    if (start !== undefined && exports.checkString(start)) {
        throw new exports.TypeError("sum() can't sum strings [use ''.join(seq) instead]");
    }
    if (start === undefined) {
        tot = new exports.nmber(0, exports.nmber.int$);
    }
    else {
        tot = start;
    }

    it = iter.tp$iter();
    for (i = it.tp$iternext(); i !== undefined; i = it.tp$iternext()) {
        if (i.skType === exports.nmber.float$) {
            has_float = true;
            if (tot.skType !== exports.nmber.float$) {
                tot = new exports.nmber(exports.asnum$(tot),
                    exports.nmber.float$);
            }
        } else if (i instanceof exports.lng) {
            if (!has_float) {
                if (!(tot instanceof exports.lng)) {
                    tot = new exports.lng(tot);
                }
            }
        }

        if (tot.nb$add(i) !== undefined) {
            tot = tot.nb$add(i);
        } else {
            throw new exports.TypeError("unsupported operand type(s) for +: '" +
                Sk.abstr.typeName(tot) + "' and '" +
                Sk.abstr.typeName(i) + "'");
        }
    }

    return tot;
};

exports.zip = function zip () {
    var el;
    var tup;
    var done;
    var res;
    var i;
    var iters;
    if (arguments.length === 0) {
        return new exports.list([]);
    }

    iters = [];
    for (i = 0; i < arguments.length; i++) {
        if (arguments[i].tp$iter) {
            iters.push(arguments[i].tp$iter());
        }
        else {
            throw new exports.TypeError("argument " + i + " must support iteration");
        }
    }
    res = [];
    done = false;
    while (!done) {
        tup = [];
        for (i = 0; i < arguments.length; i++) {
            el = iters[i].tp$iternext();
            if (el === undefined) {
                done = true;
                break;
            }
            tup.push(el);
        }
        if (!done) {
            res.push(new exports.tuple(tup));
        }
    }
    return new exports.list(res);
};

exports.abs = function abs (x) {
    exports.pyCheckArgs("abs", arguments, 1, 1);
    exports.pyCheckType("x", "number", exports.checkNumber(x));

    return new exports.nmber(Math.abs(exports.asnum$(x)), x.skType);
};

exports.ord = function ord (x) {
    exports.pyCheckArgs("ord", arguments, 1, 1);

    if (!exports.checkString(x)) {
        throw new exports.TypeError("ord() expected a string of length 1, but " + Sk.abstr.typeName(x) + " found");
    }
    else if (x.v.length !== 1) {
        throw new exports.TypeError("ord() expected a character, but string of length " + x.v.length + " found");
    }
    return new exports.nmber((x.v).charCodeAt(0), exports.nmber.int$);
};

exports.chr = function chr (x) {
    exports.pyCheckArgs("chr", arguments, 1, 1);
    if (!exports.checkInt(x)) {
        throw new exports.TypeError("an integer is required");
    }
    x = exports.asnum$(x);


    if ((x < 0) || (x > 255)) {
        throw new exports.ValueError("chr() arg not in range(256)");
    }

    return new exports.str(String.fromCharCode(x));
};

exports.int2str_ = function helper_ (x, radix, prefix) {
    var suffix;
    var str = "";
    if (x instanceof exports.lng) {
        suffix = "";
        if (radix !== 2) {
            suffix = "L";
        }

        str = x.str$(radix, false);
        if (x.nb$isnegative()) {
            return new exports.str("-" + prefix + str + suffix);
        }
        return new exports.str(prefix + str + suffix);
    } else {
        x = Sk.misceval.asIndex(x);
        str = x.toString(radix);
        if (x < 0) {
            return new exports.str("-" + prefix + str.slice(1));
        }
        return new exports.str(prefix + str);
    }
};

exports.hex = function hex (x) {
    exports.pyCheckArgs("hex", arguments, 1, 1);
    if (!Sk.misceval.isIndex(x)) {
        throw new exports.TypeError("hex() argument can't be converted to hex");
    }
    return exports.int2str_(x, 16, "0x");
};

exports.oct = function oct (x) {
    exports.pyCheckArgs("oct", arguments, 1, 1);
    if (!Sk.misceval.isIndex(x)) {
        throw new exports.TypeError("oct() argument can't be converted to hex");
    }
    return exports.int2str_(x, 8, "0");
};

exports.bin = function bin (x) {
    exports.pyCheckArgs("bin", arguments, 1, 1);
    if (!Sk.misceval.isIndex(x)) {
        throw new exports.TypeError("'" + Sk.abstr.typeName(x) + "' object can't be interpreted as an index");
    }
    return exports.int2str_(x, 2, "0b");
};

exports.dir = function dir (x) {
    var last;
    var it;
    var prop;
    var base;
    var mro;
    var i;
    var s;
    var k;
    var names;
    var getName;
    exports.pyCheckArgs("dir", arguments, 1, 1);

    getName = function (k) {
        var s = null;
        var internal = ["__bases__", "__mro__", "__class__"];
        if (internal.indexOf(k) !== -1) {
            return null;
        }
        if (k.indexOf("$") !== -1) {
            s = exports.dir.slotNameToRichName(k);
        }
        else if (k.charAt(k.length - 1) !== "_") {
            s = k;
        }
        else if (k.charAt(0) === "_") {
            s = k;
        }
        return s;
    };

    names = [];

    // Add all object properties
    for (k in x.constructor.prototype) {
        s = getName(k);
        if (s) {
            names.push(new exports.str(s));
        }
    }

    // Add all attributes
    if (x["$d"]) {
        if (x["$d"].tp$iter) {
            // Dictionary
            it = x["$d"].tp$iter();
            for (i = it.tp$iternext(); i !== undefined; i = it.tp$iternext()) {
                s = new exports.str(i);
                s = getName(s.v);
                if (s) {
                    names.push(new exports.str(s));
                }
            }
        }
        else {
            // Object
            for (s in x["$d"]) {
                names.push(new exports.str(s));
            }
        }
    }

    // Add all class attributes
    mro = x.tp$mro;
    if (mro) {
        mro = x.tp$mro;
        for (i = 0; i < mro.v.length; ++i) {
            base = mro.v[i];
            for (prop in base) {
                if (base.hasOwnProperty(prop)) {
                    s = getName(prop);
                    if (s) {
                        names.push(new exports.str(s));
                    }
                }
            }
        }
    }

    // Sort results
    names.sort(function (a, b) {
        return (a.v > b.v) - (a.v < b.v);
    });

    // Get rid of duplicates before returning, as duplicates should
    //  only occur when they are shadowed
    last = function (value, index, self) {
        // Returns true iff the value is not the same as the next value
        return value !== self[index + 1];
    };
    return new exports.list(names.filter(last));
};

exports.dir.slotNameToRichName = function (k) {
    // todo; map tp$xyz to __xyz__ properly
    return undefined;
};

exports.repr = function repr (x) {
    exports.pyCheckArgs("repr", arguments, 1, 1);

    return Sk.misceval.objectRepr(x);
};

exports.open = function open (filename, mode, bufsize) {
    exports.pyCheckArgs("open", arguments, 1, 3);
    if (mode === undefined) {
        mode = new exports.str("r");
    }
    if (mode.v !== "r" && mode.v !== "rb") {
        throw "todo; haven't implemented non-read opens";
    }
    return new exports.file(filename, mode, bufsize);
};

exports.isinstance = function isinstance (obj, type) {
    var issubclass;
    var i;
    exports.pyCheckArgs("isinstance", arguments, 2, 2);
    if (!exports.checkClass(type) && !(type instanceof exports.tuple)) {
        throw new exports.TypeError("isinstance() arg 2 must be a class, type, or tuple of classes and types");
    }

    if (type === exports.int_.prototype.ob$type) {
        if ((obj.tp$name === "number") && (obj.skType === exports.nmber.int$)) {
            return exports.bool.true$;
        }
        else {
            return exports.bool.false$;
        }
    }

    if (type === exports.float_.prototype.ob$type) {
        if ((obj.tp$name === "number") && (obj.skType === exports.nmber.float$)) {
            return exports.bool.true$;
        }
        else {
            return exports.bool.false$;
        }
    }

    if (type === exports.none.prototype.ob$type) {
        if (obj instanceof exports.none) {
            return exports.bool.true$;
        }
        else {
            return exports.bool.false$;
        }
    }

    // Normal case
    if (obj.ob$type === type) {
        return exports.bool.true$;
    }

    // Handle tuple type argument
    if (type instanceof exports.tuple) {
        for (i = 0; i < type.v.length; ++i) {
            if (Sk.misceval.isTrue(exports.isinstance(obj, type.v[i]))) {
                return exports.bool.true$;
            }
        }
        return exports.bool.false$;
    }

    issubclass = function (klass, base) {
        var i;
        var bases;
        if (klass === base) {
            return exports.bool.true$;
        }
        if (klass["$d"] === undefined) {
            return exports.bool.false$;
        }
        bases = klass["$d"].mp$subscript(exports.type.basesStr_);
        for (i = 0; i < bases.v.length; ++i) {
            if (Sk.misceval.isTrue(issubclass(bases.v[i], base))) {
                return exports.bool.true$;
            }
        }
        return exports.bool.false$;
    };

    return issubclass(obj.ob$type, type);
};
exports.hashCount = 0;
exports.hash = function hash (value) {
    var junk;
    exports.pyCheckArgs("hash", arguments, 1, 1);

    // Useless object to get compiler to allow check for __hash__ property
    junk = {__hash__: function () {
        return 0;
    }};

    if ((value instanceof Object) && (value.tp$hash !== undefined)) {
        if (value.$savedHash_) {
            return value.$savedHash_;
        }
        value.$savedHash_ = value.tp$hash();
        return value.$savedHash_;
    }
    else if ((value instanceof Object) && (value.__hash__ !== undefined)) {
        return Sk.misceval.callsim(value.__hash__, value);
    }
    else if (value instanceof exports.bool) {
        if (value.v) {
            return new exports.nmber(1, exports.nmber.int$);
        }
        return new exports.nmber(0, exports.nmber.int$);
    }
    else if (value instanceof exports.none) {
        return new exports.nmber(0, exports.nmber.int$);
    }
    else if (value instanceof Object) {
        if (value.__id === undefined) {
            exports.hashCount += 1;
            value.__id = exports.hashCount;
        }
        return new exports.nmber(value.__id, exports.nmber.int$);
    }
    else if (typeof value === "number" || value === null ||
        value === true || value === false) {
        throw new exports.TypeError("unsupported Javascript type");
    }

    return new exports.str((typeof value) + " " + String(value));
    // todo; throw properly for unhashable types
};

exports.getattr = function getattr (obj, name, default_) {
    var ret;
    exports.pyCheckArgs("getattr", arguments, 2, 3);
    if (!exports.checkString(name)) {
        throw new exports.TypeError("attribute name must be string");
    }

    ret = obj.tp$getattr(name.v);
    if (ret === undefined) {
        if (default_ !== undefined) {
            return default_;
        }
        else {
            throw new exports.AttributeError("'" + Sk.abstr.typeName(obj) + "' object has no attribute '" + name.v + "'");
        }
    }
    return ret;
};

exports.raw_input = function (prompt) {
    var x, resolution, susp;

    prompt = prompt ? prompt.v : "";
    x = Sk.inputfun(prompt);

    if (x instanceof Promise) {
        susp = new Sk.misceval.Suspension();

        susp.resume = function() {
            return new exports.str(resolution);
        };

        susp.data = {
            type: "Sk.promise",
            promise: x.then(function(value) {
                resolution = value;
                return value;
            }, function(err) {
                resolution = "";
                return err;
            })
        };

        return susp;
    }
    else {
        return new exports.str(x);
    }
};

exports.input = exports.raw_input;

exports.jseval = function jseval (evalcode) {
    eval(evalcode)
};

exports.jsmillis = function jsmillis () {
    var now = new Date();
    return now.valueOf();
};

exports.superbi = function superbi () {
    throw new exports.NotImplementedError("super is not yet implemented, please report your use case as a github issue.");
};

exports.eval_ = function eval_ () {
    throw new exports.NotImplementedError("eval is not yet implemented");
};

exports.map = function map (fun, seq) {
    var iter, item;
    var retval;
    var next;
    var nones;
    var args;
    var argnum;
    var i;
    var iterables;
    var combined;
    exports.pyCheckArgs("map", arguments, 2);

    if (arguments.length > 2) {
        // Pack sequences into one list of Javascript Arrays

        combined = [];
        iterables = Array.prototype.slice.apply(arguments).slice(1);
        for (i in iterables) {
            if (!exports.checkIterable(iterables[i])) {
                argnum = parseInt(i, 10) + 2;
                throw new exports.TypeError("argument " + argnum + " to map() must support iteration");
            }
            iterables[i] = iterables[i].tp$iter();
        }

        while (true) {
            args = [];
            nones = 0;
            for (i in iterables) {
                next = iterables[i].tp$iternext();
                if (next === undefined) {
                    args.push(exports.none.none$);
                    nones++;
                }
                else {
                    args.push(next);
                }
            }
            if (nones !== iterables.length) {
                combined.push(args);
            }
            else {
                // All iterables are done
                break;
            }
        }
        seq = new exports.list(combined);
    }
    if (!exports.checkIterable(seq)) {
        throw new exports.TypeError("'" + Sk.abstr.typeName(seq) + "' object is not iterable");
    }

    retval = [];

    for (iter = seq.tp$iter(), item = iter.tp$iternext();
         item !== undefined;
         item = iter.tp$iternext()) {
        if (fun === exports.none.none$) {
            if (item instanceof Array) {
                // With None function and multiple sequences,
                // map should return a list of tuples
                item = new exports.tuple(item);
            }
            retval.push(item);
        }
        else {
            if (!(item instanceof Array)) {
                // If there was only one iterable, convert to Javascript
                // Array for call to apply.
                item = [item];
            }
            retval.push(Sk.misceval.apply(fun, undefined, undefined, undefined, item));
        }
    }

    return new exports.list(retval);
};

exports.reduce = function reduce (fun, seq, initializer) {
    var item;
    var accum_value;
    var iter;
    exports.pyCheckArgs("reduce", arguments, 2, 3);
    if (!exports.checkIterable(seq)) {
        throw new exports.TypeError("'" + Sk.abstr.typeName(seq) + "' object is not iterable");
    }

    iter = seq.tp$iter();
    if (initializer === undefined) {
        initializer = iter.tp$iternext();
        if (initializer === undefined) {
            throw new exports.TypeError("reduce() of empty sequence with no initial value");
        }
    }
    accum_value = initializer;
    for (item = iter.tp$iternext();
         item !== undefined;
         item = iter.tp$iternext()) {
        accum_value = Sk.misceval.callsim(fun, accum_value, item);
    }

    return accum_value;
};

exports.filter = function filter (fun, iterable) {
    var result;
    var iter, item;
    var retval;
    var ret;
    var add;
    var ctor;
    exports.pyCheckArgs("filter", arguments, 2, 2);

    if (!exports.checkIterable(iterable)) {
        throw new exports.TypeError("'" + Sk.abstr.typeName(iterable) + "' object is not iterable");
    }

    ctor = function () {
        return [];
    };
    add = function (iter, item) {
        iter.push(item);
        return iter;
    };
    ret = function (iter) {
        return new exports.list(iter);
    };

    if (iterable.__class__ === exports.str) {
        ctor = function () {
            return new exports.str("");
        };
        add = function (iter, item) {
            return iter.sq$concat(item);
        };
        ret = function (iter) {
            return iter;
        };
    }
    else if (iterable.__class__ === exports.tuple) {
        ret = function (iter) {
            return new exports.tuple(iter);
        };
    }

    retval = ctor();

    for (iter = iterable.tp$iter(), item = iter.tp$iternext();
         item !== undefined;
         item = iter.tp$iternext()) {
        if (fun === exports.none.none$) {
            result = exports.bool(item);
        }
        else {
            result = Sk.misceval.callsim(fun, item);
        }

        if (Sk.misceval.isTrue(result)) {
            retval = add(retval, item);
        }
    }

    return ret(retval);
};

exports.hasattr = function hasattr (obj, attr) {
    exports.pyCheckArgs("hasattr", arguments, 2, 2);
    if (!exports.checkString(attr)) {
        throw new exports.TypeError("hasattr(): attribute name must be string");
    }

    if (obj.tp$getattr) {
        if (obj.tp$getattr(attr.v)) {
            return exports.bool.true$;
        } else {
            return exports.bool.false$;
        }
    } else {
        throw new exports.AttributeError("Object has no tp$getattr method");
    }
};


exports.pow = function pow (a, b, c) {
    var ret;
    var res;
    var right;
    var left;
    var c_num;
    var b_num;
    var a_num;
    exports.pyCheckArgs("pow", arguments, 2, 3);

    if (c instanceof exports.none) {
        c = undefined;
    }

    a_num = exports.asnum$(a);
    b_num = exports.asnum$(b);
    c_num = exports.asnum$(c);

    if (!exports.checkNumber(a) || !exports.checkNumber(b)) {
        if (c === undefined) {
            throw new exports.TypeError("unsupported operand type(s) for pow(): '" + Sk.abstr.typeName(a) + "' and '" + Sk.abstr.typeName(b) + "'");
        }
        throw new exports.TypeError("unsupported operand type(s) for pow(): '" + Sk.abstr.typeName(a) + "', '" + Sk.abstr.typeName(b) + "', '" + Sk.abstr.typeName(c) + "'");
    }
    if (a_num < 0 && b.skType === exports.nmber.float$) {
        throw new exports.ValueError("negative number cannot be raised to a fractional power");
    }

    if (c === undefined) {
        if ((a.skType === exports.nmber.float$ || b.skType === exports.nmber.float$) || (b_num < 0)) {
            return new exports.nmber(Math.pow(a_num, b_num), exports.nmber.float$);
        }

        left = new exports.nmber(a_num, exports.nmber.int$);
        right = new exports.nmber(b_num, exports.nmber.int$);
        res = left.nb$power(right);

        if (a instanceof exports.lng || b instanceof exports.lng) {
            return new exports.lng(res);
        }

        return res;
    }
    else {
        if (!exports.checkInt(a) || !exports.checkInt(b) || !exports.checkInt(c)) {
            throw new exports.TypeError("pow() 3rd argument not allowed unless all arguments are integers");
        }
        if (b_num < 0) {
            throw new exports.TypeError("pow() 2nd argument cannot be negative when 3rd argument specified");
        }

        if ((a instanceof exports.lng || b instanceof exports.lng || c instanceof exports.lng) ||
            (Math.pow(a_num, b_num) === Infinity)) {
            // convert a to a long so that we can use biginteger's modPowInt method
            a = new exports.lng(a);
            return a.nb$power(b, c);
        }
        else {
            ret = new exports.nmber(Math.pow(a_num, b_num), exports.nmber.int$);
            return ret.nb$remainder(c);
        }
    }
};

exports.quit = function quit (msg) {
    var s = new exports.str(msg).v;
    throw new exports.SystemExit(s);
};


exports.issubclass = function issubclass (c1, c2) {
    var i;
    var issubclass_internal;
    exports.pyCheckArgs("issubclass", arguments, 2, 2);
    if (!exports.checkClass(c2) && !(c2 instanceof exports.tuple)) {
        throw new exports.TypeError("issubclass() arg 2 must be a classinfo, type, or tuple of classes and types");
    }

    //print("c1 name: " + c1.tp$name);

    if (c2 === exports.int_.prototype.ob$type) {
        return true;
    }

    if (c2 === exports.float_.prototype.ob$type) {
        return true;
    }

    if (c2 === exports.none.prototype.ob$type) {
        return true;
    }

    // Normal case
    if (c1.ob$type === c2) {
        return true;
    }

    issubclass_internal = function (klass, base) {
        var i;
        var bases;
        if (klass === base) {
            return true;
        }
        if (klass["$d"] === undefined) {
            return false;
        }
        if (klass["$d"].mp$subscript) {
            bases = klass["$d"].mp$subscript(exports.type.basesStr_);
        } else {
            return false;
        }
        for (i = 0; i < bases.v.length; ++i) {
            if (issubclass_internal(bases.v[i], base)) {
                return true;
            }
        }
        return false;
    };

    // Handle tuple type argument
    if (c2 instanceof exports.tuple) {
        for (i = 0; i < c2.v.length; ++i) {
            if (exports.issubclass(c1, c2.v[i])) {
                return true;
            }
        }
        return false;
    }

    return issubclass_internal(c1, c2);

};

exports.globals = function globals () {
    var i;
    var ret = new exports.dict([]);
    for (i in Sk["globals"]) {
        ret.mp$ass_subscript(new exports.str(i), Sk["globals"][i]);
    }

    return ret;

};

exports.divmod = function divmod (a, b) {
    return Sk.abstr.numberBinOp(a, b, "DivMod");
};


exports.bytearray = function bytearray () {
    throw new exports.NotImplementedError("bytearray is not yet implemented");
};
exports.callable = function callable () {
    throw new exports.NotImplementedError("callable is not yet implemented");
};
exports.complex = function complex () {
    throw new exports.NotImplementedError("complex is not yet implemented");
};
exports.delattr = function delattr () {
    throw new exports.NotImplementedError("delattr is not yet implemented");
};
exports.execfile = function execfile () {
    throw new exports.NotImplementedError("execfile is not yet implemented");
};
exports.format = function format () {
    throw new exports.NotImplementedError("format is not yet implemented");
};
exports.frozenset = function frozenset () {
    throw new exports.NotImplementedError("frozenset is not yet implemented");
};

exports.help = function help () {
    throw new exports.NotImplementedError("help is not yet implemented");
};
exports.iter = function iter () {
    throw new exports.NotImplementedError("iter is not yet implemented");
};
exports.locals = function locals () {
    throw new exports.NotImplementedError("locals is not yet implemented");
};
exports.memoryview = function memoryview () {
    throw new exports.NotImplementedError("memoryview is not yet implemented");
};
exports.next_ = function next_ () {
    throw new exports.NotImplementedError("next is not yet implemented");
};
exports.property = function property () {
    throw new exports.NotImplementedError("property is not yet implemented");
};
exports.reload = function reload () {
    throw new exports.NotImplementedError("reload is not yet implemented");
};
exports.reversed = function reversed () {
    throw new exports.NotImplementedError("reversed is not yet implemented");
};
exports.unichr = function unichr () {
    throw new exports.NotImplementedError("unichr is not yet implemented");
};
exports.vars = function vars () {
    throw new exports.NotImplementedError("vars is not yet implemented");
};
exports.xrange = exports.range;
exports.apply_ = function apply_ () {
    throw new exports.NotImplementedError("apply is not yet implemented");
};
exports.buffer = function buffer () {
    throw new exports.NotImplementedError("buffer is not yet implemented");
};
exports.coerce = function coerce () {
    throw new exports.NotImplementedError("coerce is not yet implemented");
};
exports.intern = function intern () {
    throw new exports.NotImplementedError("intern is not yet implemented");
};


/*
 exportsFiles = {};
 exports.read = function read(x) {
 if (exportsFiles === undefined || exportsFiles["files"][x] === undefined)
 throw "File not found: '" + x + "'";
 return exportsFiles["files"][x];
 };
 exportsFiles = undefined;
 */

},{}],3:[function(require,module,exports){
module.exports = {
  parser: require('./parser'),
  Tokenizer: require('./tokenize'),
  tables: require('../gen/parse_tables')
}

},{"../gen/parse_tables":1,"./parser":4,"./tokenize":5}],4:[function(require,module,exports){
// low level parser to a concrete syntax tree, derived from cpython's lib2to3

/**
 *
 * @constructor
 * @param {Object} grammar
 *
 * p = new Parser(grammar);
 * p.setup([start]);
 * foreach input token:
 *     if p.addtoken(...):
 *         break
 * root = p.rootnode
 *
 * can throw ParseError
 */

var builtin = require('./builtin'),
    Tokenizer = require('./tokenize'),
    tables = require ('../gen/parse_tables');

function Parser (filename, grammar) {
    this.filename = filename;
    this.grammar = grammar;
    return this;
}


Parser.prototype.setup = function (start) {
    var stackentry;
    var newnode;
    start = start || this.grammar.start;
    //print("START:"+start);

    newnode =
    {
        type    : start,
        value   : null,
        context : null,
        children: []
    };
    stackentry =
    {
        dfa  : this.grammar.dfas[start],
        state: 0,
        node : newnode
    };
    this.stack = [stackentry];
    this.used_names = {};
};

function findInDfa (a, obj) {
    var i = a.length;
    while (i--) {
        if (a[i][0] === obj[0] && a[i][1] === obj[1]) {
            return true;
        }
    }
    return false;
}


// Add a token; return true if we're done
Parser.prototype.addtoken = function (type, value, context) {
    var errline;
    var itsfirst;
    var itsdfa;
    var state;
    var v;
    var t;
    var newstate;
    var i;
    var a;
    var arcs;
    var first;
    var states;
    var tp;
    var ilabel = this.classify(type, value, context);
    //print("ilabel:"+ilabel);

    OUTERWHILE:
    while (true) {
        tp = this.stack[this.stack.length - 1];
        states = tp.dfa[0];
        first = tp.dfa[1];
        arcs = states[tp.state];

        // look for a state with this label
        for (a = 0; a < arcs.length; ++a) {
            i = arcs[a][0];
            newstate = arcs[a][1];
            t = this.grammar.labels[i][0];
            v = this.grammar.labels[i][1];
            if (ilabel === i) {
                // look it up in the list of labels
                // goog.asserts.assert(t < 256);
                // shift a token; we're done with it
                this.shift(type, value, newstate, context);
                // pop while we are in an accept-only state
                state = newstate;
                //print("before:"+JSON.stringify(states[state]) + ":state:"+state+":"+JSON.stringify(states[state]));
                /* jshint ignore:start */
                while (states[state].length === 1
                    && states[state][0][0] === 0
                    && states[state][0][1] === state) // states[state] == [(0, state)])
                {
                    this.pop();
                    //print("in after pop:"+JSON.stringify(states[state]) + ":state:"+state+":"+JSON.stringify(states[state]));
                    if (this.stack.length === 0) {
                        // done!
                        return true;
                    }
                    tp = this.stack[this.stack.length - 1];
                    state = tp.state;
                    states = tp.dfa[0];
                    first = tp.dfa[1];
                    //print(JSON.stringify(states), JSON.stringify(first));
                    //print("bottom:"+JSON.stringify(states[state]) + ":state:"+state+":"+JSON.stringify(states[state]));
                }
                /* jshint ignore:end */
                // done with this token
                //print("DONE, return false");
                return false;
            }
            else if (t >= 256) {
                itsdfa = this.grammar.dfas[t];
                itsfirst = itsdfa[1];
                if (itsfirst.hasOwnProperty(ilabel)) {
                    // push a symbol
                    this.push(t, this.grammar.dfas[t], newstate, context);
                    continue OUTERWHILE;
                }
            }
        }

        //print("findInDfa: " + JSON.stringify(arcs)+" vs. " + tp.state);
        if (findInDfa(arcs, [0, tp.state])) {
            // an accepting state, pop it and try somethign else
            //print("WAA");
            this.pop();
            if (this.stack.length === 0) {
                throw new builtin.ParseError("too much input", this.filename);
            }
        }
        else {
            // no transition
            errline = context[0][0];
            throw new builtin.ParseError("bad input", this.filename, errline, context);
        }
    }
};

// turn a token into a label
Parser.prototype.classify = function (type, value, context) {
    var ilabel;
    if (type === Tokenizer.Tokens.T_NAME) {
        this.used_names[value] = true;
        ilabel = this.grammar.keywords.hasOwnProperty(value) && this.grammar.keywords[value];
        if (ilabel) {
            //print("is keyword");
            return ilabel;
        }
    }
    ilabel = this.grammar.tokens.hasOwnProperty(type) && this.grammar.tokens[type];
    if (!ilabel) {
        // throw new builtin.ParseError("bad token", type, value, context);
        // Questionable modification to put line number in position 2
        // like everywhere else and filename in position 1.
        throw new builtin.ParseError("bad token", this.filename, context[0][0], context);
    }
    return ilabel;
};

// shift a token
Parser.prototype.shift = function (type, value, newstate, context) {
    var dfa = this.stack[this.stack.length - 1].dfa;
    var state = this.stack[this.stack.length - 1].state;
    var node = this.stack[this.stack.length - 1].node;
    //print("context", context);
    var newnode = {
        type      : type,
        value     : value,
        lineno    : context[0][0],         // throwing away end here to match cpython
        col_offset: context[0][1],
        line_end  : context[1][0],
        col_end   : context[1][1],
        children  : null
    };
    if (newnode) {
        node.children.push(newnode);
    }
    this.stack[this.stack.length - 1] = {
        dfa  : dfa,
        state: newstate,
        node : node
    };
};

// push a nonterminal
Parser.prototype.push = function (type, newdfa, newstate, context) {
    var dfa = this.stack[this.stack.length - 1].dfa;
    var node = this.stack[this.stack.length - 1].node;
    var newnode = {
        type      : type,
        value     : null,
        lineno    : context[0][0],      // throwing away end here to match cpython
        col_offset: context[0][1],
        children  : []
    };
    this.stack[this.stack.length - 1] = {
        dfa  : dfa,
        state: newstate,
        node : node
    };
    this.stack.push({
        dfa  : newdfa,
        state: 0,
        node : newnode
    });
};

//var ac = 0;
//var bc = 0;

// pop a nonterminal
Parser.prototype.pop = function () {
    var node;
    var pop = this.stack.pop();
    var newnode = pop.node;
    //print("POP");
    if (newnode) {
        //print("A", ac++, newnode.type);
        //print("stacklen:"+this.stack.length);
        if (this.stack.length !== 0) {
            //print("B", bc++);
            node = this.stack[this.stack.length - 1].node;
            node.children.push(newnode);
        }
        else {
            //print("C");
            this.rootnode = newnode;
            this.rootnode.used_names = this.used_names;
        }
    }
};

/**
 * parser for interactive input. returns a function that should be called with
 * lines of input as they are entered. the function will return false
 * until the input is complete, when it will return the rootnode of the parse.
 *
 * @param {string} filename
 * @param {string=} style root of parse tree (optional)
 */
function makeParser (filename, style) {
    var tokenizer;
    var T_OP;
    var T_NL;
    var T_COMMENT;
    var prefix;
    var column;
    var lineno;
    var p;
    if (style === undefined) {
        style = "file_input";
    }
    p = new Parser(filename, tables.ParseTables);
    // for closure's benefit
    if (style === "file_input") {
        p.setup(tables.ParseTables.sym.file_input);
    }
    else {
        goog.asserts.fail("todo;");
    }
    lineno = 1;
    column = 0;
    prefix = "";
    T_COMMENT = Tokenizer.Tokens.T_COMMENT;
    T_NL = Tokenizer.Tokens.T_NL;
    T_OP = Tokenizer.Tokens.T_OP;
    tokenizer = new Tokenizer(filename, style === "single_input", function (type, value, start, end, line) {
        var s_lineno = start[0];
        var s_column = start[1];
        /*
         if (s_lineno !== lineno && s_column !== column)
         {
         // todo; update prefix and line/col
         }
         */
        if (type === T_COMMENT || type === T_NL) {
            prefix += value;
            lineno = end[0];
            column = end[1];
            if (value[value.length - 1] === "\n") {
                lineno += 1;
                column = 0;
            }
            //print("  not calling addtoken");
            return undefined;
        }
        if (type === T_OP) {
            type = tables.OpMap[value];
        }
        if (p.addtoken(type, value, [start, end, line])) {
            return true;
        }
    });
    return function (line) {
        var ret = tokenizer.generateTokens(line);
        //print("tok:"+ret);
        if (ret) {
            if (ret !== "done") {
                throw new builtin.ParseError("incomplete input", this.filename);
            }
            return p.rootnode;
        }
        return false;
    };
}

exports.parse = function parse (filename, input) {
    var i;
    var ret;
    var lines;
    var parseFunc = makeParser(filename);
    if (input.substr(input.length - 1, 1) !== "\n") {
        input += "\n";
    }
    //print("input:"+input);
    lines = input.split("\n");
    for (i = 0; i < lines.length; ++i) {
        ret = parseFunc(lines[i] + ((i === lines.length - 1) ? "" : "\n"));
    }
    return ret;
};

exports.parseTreeDump = function parseTreeDump (n, indent) {
    //return JSON.stringify(n, null, 2);
    var i;
    var ret;
    indent = indent || "";
    ret = "";
    ret += indent;
    if (n.type >= 256) // non-term
    {
        ret += tables.ParseTables.number2symbol[n.type] + "\n";
        for (i = 0; i < n.children.length; ++i) {
            ret += exports.parseTreeDump(n.children[i], indent + "  ");
        }
    }
    else {
        ret += Tokenizer.tokenNames[n.type] + ": " + new builtin.str(n.value)["$r"]().v + "\n";
    }
    return ret;
};

},{"../gen/parse_tables":1,"./builtin":2,"./tokenize":5}],5:[function(require,module,exports){
/*
 * This is a port of tokenize.py by Ka-Ping Yee.
 *
 * each call to readline should return one line of input as a string, or
 * undefined if it's finished.
 *
 * callback is called for each token with 5 args:
 * 1. the token type
 * 2. the token string
 * 3. [ start_row, start_col ]
 * 4. [ end_row, end_col ]
 * 5. logical line where the token was found, including continuation lines
 *
 * callback can return true to abort.
 *
 */

/**
 * @constructor
 */
module.exports = function Tokenizer(filename, interactive, callback) {
    this.filename = filename;
    this.callback = callback;
    this.lnum = 0;
    this.parenlev = 0;
    this.continued = false;
    this.namechars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ_";
    this.numchars = "0123456789";
    this.contstr = "";
    this.needcont = false;
    this.contline = undefined;
    this.indents = [0];
    this.endprog = /.*/;
    this.strstart = [-1, -1];
    this.interactive = interactive;
    this.doneFunc = function () {
        var i;
        for (i = 1; i < this.indents.length; ++i) // pop remaining indent levels
        {
            if (this.callback(module.exports.Tokens.T_DEDENT, "", [this.lnum, 0], [this.lnum, 0], "")) {
                return "done";
            }
        }
        if (this.callback(module.exports.Tokens.T_ENDMARKER, "", [this.lnum, 0], [this.lnum, 0], "")) {
            return "done";
        }

        return "failed";
    };

};

/**
 * @enum {number}
 */
module.exports.Tokens = {
    T_ENDMARKER       : 0,
    T_NAME            : 1,
    T_NUMBER          : 2,
    T_STRING          : 3,
    T_NEWLINE         : 4,
    T_INDENT          : 5,
    T_DEDENT          : 6,
    T_LPAR            : 7,
    T_RPAR            : 8,
    T_LSQB            : 9,
    T_RSQB            : 10,
    T_COLON           : 11,
    T_COMMA           : 12,
    T_SEMI            : 13,
    T_PLUS            : 14,
    T_MINUS           : 15,
    T_STAR            : 16,
    T_SLASH           : 17,
    T_VBAR            : 18,
    T_AMPER           : 19,
    T_LESS            : 20,
    T_GREATER         : 21,
    T_EQUAL           : 22,
    T_DOT             : 23,
    T_PERCENT         : 24,
    T_BACKQUOTE       : 25,
    T_LBRACE          : 26,
    T_RBRACE          : 27,
    T_EQEQUAL         : 28,
    T_NOTEQUAL        : 29,
    T_LESSEQUAL       : 30,
    T_GREATEREQUAL    : 31,
    T_TILDE           : 32,
    T_CIRCUMFLEX      : 33,
    T_LEFTSHIFT       : 34,
    T_RIGHTSHIFT      : 35,
    T_DOUBLESTAR      : 36,
    T_PLUSEQUAL       : 37,
    T_MINEQUAL        : 38,
    T_STAREQUAL       : 39,
    T_SLASHEQUAL      : 40,
    T_PERCENTEQUAL    : 41,
    T_AMPEREQUAL      : 42,
    T_VBAREQUAL       : 43,
    T_CIRCUMFLEXEQUAL : 44,
    T_LEFTSHIFTEQUAL  : 45,
    T_RIGHTSHIFTEQUAL : 46,
    T_DOUBLESTAREQUAL : 47,
    T_DOUBLESLASH     : 48,
    T_DOUBLESLASHEQUAL: 49,
    T_AT              : 50,
    T_OP              : 51,
    T_COMMENT         : 52,
    T_NL              : 53,
    T_RARROW          : 54,
    T_ERRORTOKEN      : 55,
    T_N_TOKENS        : 56,
    T_NT_OFFSET       : 256
};

/** @param {...*} x */
function group (x) {
    var args = Array.prototype.slice.call(arguments);
    return "(" + args.join("|") + ")";
}

/** @param {...*} x */
function any (x) {
    return group.apply(null, arguments) + "*";
}

/** @param {...*} x */
function maybe (x) {
    return group.apply(null, arguments) + "?";
}

/* we have to use string and ctor to be able to build patterns up. + on /.../
 * does something strange. */
var Whitespace = "[ \\f\\t]*";
var Comment_ = "#[^\\r\\n]*";
var Ident = "[a-zA-Z_]\\w*";

var Binnumber = "0[bB][01]*";
var Hexnumber = "0[xX][\\da-fA-F]*[lL]?";
var Octnumber = "0[oO]?[0-7]*[lL]?";
var Decnumber = "[1-9]\\d*[lL]?";
var Intnumber = group(Binnumber, Hexnumber, Octnumber, Decnumber);

var Exponent = "[eE][-+]?\\d+";
var Pointfloat = group("\\d+\\.\\d*", "\\.\\d+") + maybe(Exponent);
var Expfloat = "\\d+" + Exponent;
var Floatnumber = group(Pointfloat, Expfloat);
var Imagnumber = group("\\d+[jJ]", Floatnumber + "[jJ]");
var Number_ = group(Imagnumber, Floatnumber, Intnumber);

// tail end of ' string
var Single = "^[^'\\\\]*(?:\\\\.[^'\\\\]*)*'";
// tail end of " string
var Double_ = '^[^"\\\\]*(?:\\\\.[^"\\\\]*)*"';
// tail end of ''' string
var Single3 = "[^'\\\\]*(?:(?:\\\\.|'(?!''))[^'\\\\]*)*'''";
// tail end of """ string
var Double3 = '[^"\\\\]*(?:(?:\\\\.|"(?!""))[^"\\\\]*)*"""';
var Triple = group("[ubUB]?[rR]?'''", '[ubUB]?[rR]?"""');
var String_ = group("[uU]?[rR]?'[^\\n'\\\\]*(?:\\\\.[^\\n'\\\\]*)*'",
    '[uU]?[rR]?"[^\\n"\\\\]*(?:\\\\.[^\\n"\\\\]*)*"');

// Because of leftmost-then-longest match semantics, be sure to put the
// longest operators first (e.g., if = came before ==, == would get
// recognized as two instances of =).
var Operator = group("\\*\\*=?", ">>=?", "<<=?", "<>", "!=",
    "//=?", "->",
    "[+\\-*/%&|^=<>]=?",
    "~");

var Bracket = "[\\][(){}]";
var Special = group("\\r?\\n", "[:;.,`@]");
var Funny = group(Operator, Bracket, Special);

var ContStr = group("[uUbB]?[rR]?'[^\\n'\\\\]*(?:\\\\.[^\\n'\\\\]*)*" +
        group("'", "\\\\\\r?\\n"),
        "[uUbB]?[rR]?\"[^\\n\"\\\\]*(?:\\\\.[^\\n\"\\\\]*)*" +
        group("\"", "\\\\\\r?\\n"));
var PseudoExtras = group("\\\\\\r?\\n", Comment_, Triple);
// Need to prefix with "^" as we only want to match what's next
var PseudoToken = "^" + group(PseudoExtras, Number_, Funny, ContStr, Ident);


var triple_quoted = {
    "'''"  : true, '"""': true,
    "r'''" : true, 'r"""': true, "R'''": true, 'R"""': true,
    "u'''" : true, 'u"""': true, "U'''": true, 'U"""': true,
    "b'''" : true, 'b"""': true, "B'''": true, 'B"""': true,
    "ur'''": true, 'ur"""': true, "Ur'''": true, 'Ur"""': true,
    "uR'''": true, 'uR"""': true, "UR'''": true, 'UR"""': true,
    "br'''": true, 'br"""': true, "Br'''": true, 'Br"""': true,
    "bR'''": true, 'bR"""': true, "BR'''": true, 'BR"""': true
};

var single_quoted = {
    "'"  : true, '"': true,
    "r'" : true, 'r"': true, "R'": true, 'R"': true,
    "u'" : true, 'u"': true, "U'": true, 'U"': true,
    "b'" : true, 'b"': true, "B'": true, 'B"': true,
    "ur'": true, 'ur"': true, "Ur'": true, 'Ur"': true,
    "uR'": true, 'uR"': true, "UR'": true, 'UR"': true,
    "br'": true, 'br"': true, "Br'": true, 'Br"': true,
    "bR'": true, 'bR"': true, "BR'": true, 'BR"': true
};

// hack to make closure keep those objects. not sure what a better way is.
(function () {
    var k;
    for (k in triple_quoted) {
    }
    for (k in single_quoted) {
    }
}());


var tabsize = 8;

function contains (a, obj) {
    var i = a.length;
    while (i--) {
        if (a[i] === obj) {
            return true;
        }
    }
    return false;
}

function rstrip (input, what) {
    var i;
    for (i = input.length; i > 0; --i) {
        if (what.indexOf(input.charAt(i - 1)) === -1) {
            break;
        }
    }
    return input.substring(0, i);
}

module.exports.prototype.generateTokens = function (line) {
    var nl_pos;
    var newl;
    var initial;
    var token;
    var epos;
    var spos;
    var start;
    var pseudomatch;
    var capos;
    var comment_token;
    var endmatch, pos, column, end, max;


    // bnm - Move these definitions in this function otherwise test state is preserved between
    // calls on single3prog and double3prog causing weird errors with having multiple instances
    // of triple quoted strings in the same program.

    var pseudoprog = new RegExp(PseudoToken);
    var single3prog = new RegExp(Single3, "g");
    var double3prog = new RegExp(Double3, "g");

    var endprogs = {     "'": new RegExp(Single, "g"), "\"": new RegExp(Double_, "g"),
        "'''"               : single3prog, '"""': double3prog,
        "r'''"              : single3prog, 'r"""': double3prog,
        "u'''"              : single3prog, 'u"""': double3prog,
        "b'''"              : single3prog, 'b"""': double3prog,
        "ur'''"             : single3prog, 'ur"""': double3prog,
        "br'''"             : single3prog, 'br"""': double3prog,
        "R'''"              : single3prog, 'R"""': double3prog,
        "U'''"              : single3prog, 'U"""': double3prog,
        "B'''"              : single3prog, 'B"""': double3prog,
        "uR'''"             : single3prog, 'uR"""': double3prog,
        "Ur'''"             : single3prog, 'Ur"""': double3prog,
        "UR'''"             : single3prog, 'UR"""': double3prog,
        "bR'''"             : single3prog, 'bR"""': double3prog,
        "Br'''"             : single3prog, 'Br"""': double3prog,
        "BR'''"             : single3prog, 'BR"""': double3prog,
        'r'                 : null, 'R': null,
        'u'                 : null, 'U': null,
        'b'                 : null, 'B': null
    };


    if (!line) {
        line = '';
    }
    //print("LINE:'"+line+"'");

    this.lnum += 1;
    pos = 0;
    max = line.length;

    if (this.contstr.length > 0) {
        if (!line) {
            throw new Sk.builtin.TokenError("EOF in multi-line string", this.filename, this.strstart[0], this.strstart[1], this.contline);
        }
        this.endprog.lastIndex = 0;
        endmatch = this.endprog.test(line);
        if (endmatch) {
            pos = end = this.endprog.lastIndex;
            if (this.callback(module.exports.Tokens.T_STRING, this.contstr + line.substring(0, end),
                this.strstart, [this.lnum, end], this.contline + line)) {
                return 'done';
            }
            this.contstr = '';
            this.needcont = false;
            this.contline = undefined;
        }
        else if (this.needcont && line.substring(line.length - 2) !== "\\\n" && line.substring(line.length - 3) !== "\\\r\n") {
            if (this.callback(module.exports.Tokens.T_ERRORTOKEN, this.contstr + line,
                this.strstart, [this.lnum, line.length], this.contline)) {
                return 'done';
            }
            this.contstr = '';
            this.contline = undefined;
            return false;
        }
        else {
            this.contstr += line;
            this.contline = this.contline + line;
            return false;
        }
    }
    else if (this.parenlev === 0 && !this.continued) {
        if (!line) {
            return this.doneFunc();
        }
        column = 0;
        while (pos < max) {
            if (line.charAt(pos) === ' ') {
                column += 1;
            }
            else if (line.charAt(pos) === '\t') {
                column = (column / tabsize + 1) * tabsize;
            }
            else if (line.charAt(pos) === '\f') {
                column = 0;
            }
            else {
                break;
            }
            pos = pos + 1;
        }
        if (pos === max) {
            return this.doneFunc();
        }

        if ("#\r\n".indexOf(line.charAt(pos)) !== -1) // skip comments or blank lines
        {
            if (line.charAt(pos) === '#') {
                comment_token = rstrip(line.substring(pos), '\r\n');
                nl_pos = pos + comment_token.length;
                if (this.callback(module.exports.Tokens.T_COMMENT, comment_token,
                    [this.lnum, pos], [this.lnum, pos + comment_token.length], line)) {
                    return 'done';
                }
                //print("HERE:1");
                if (this.callback(module.exports.Tokens.T_NL, line.substring(nl_pos),
                    [this.lnum, nl_pos], [this.lnum, line.length], line)) {
                    return 'done';
                }
                return false;
            }
            else {
                //print("HERE:2");
                if (this.callback(module.exports.Tokens.T_NL, line.substring(pos),
                    [this.lnum, pos], [this.lnum, line.length], line)) {
                    return 'done';
                }
                if (!this.interactive) {
                    return false;
                }
            }
        }

        if (column > this.indents[this.indents.length - 1]) // count indents or dedents
        {
            this.indents.push(column);
            if (this.callback(module.exports.Tokens.T_INDENT, line.substring(0, pos), [this.lnum, 0], [this.lnum, pos], line)) {
                return 'done';
            }
        }
        while (column < this.indents[this.indents.length - 1]) {
            if (!contains(this.indents, column)) {
                throw new Sk.builtin.IndentationError("unindent does not match any outer indentation level",
                    this.filename, this.lnum, pos, line);
            }
            this.indents.splice(this.indents.length - 1, 1);
            //print("dedent here");
            if (this.callback(module.exports.Tokens.T_DEDENT, '', [this.lnum, pos], [this.lnum, pos], line)) {
                return 'done';
            }
        }
    }
    else // continued statement
    {
        if (!line) {
            throw new Sk.builtin.TokenError("EOF in multi-line statement", this.filename, this.lnum, 0, line);
        }
        this.continued = false;
    }

    while (pos < max) {
        //print("pos:"+pos+":"+max);
        // js regexes don't return any info about matches, other than the
        // content. we'd like to put a \w+ before pseudomatch, but then we
        // can't get any data
        capos = line.charAt(pos);
        while (capos === ' ' || capos === '\f' || capos === '\t') {
            pos += 1;
            capos = line.charAt(pos);
        }
        pseudoprog.lastIndex = 0;
        pseudomatch = pseudoprog.exec(line.substring(pos));
        if (pseudomatch) {
            start = pos;
            end = start + pseudomatch[1].length;
            spos = [this.lnum, start];
            epos = [this.lnum, end];
            pos = end;
            token = line.substring(start, end);
            initial = line.charAt(start);
            //Sk.debugout("token:",token, "initial:",initial, start, end);
            if (this.numchars.indexOf(initial) !== -1 || (initial === '.' && token !== '.')) {
                if (this.callback(module.exports.Tokens.T_NUMBER, token, spos, epos, line)) {
                    return 'done';
                }
            }
            else if (initial === '\r' || initial === '\n') {
                newl = module.exports.Tokens.T_NEWLINE;
                //print("HERE:3");
                if (this.parenlev > 0) {
                    newl = module.exports.Tokens.T_NL;
                }
                if (this.callback(newl, token, spos, epos, line)) {
                    return 'done';
                }
            }
            else if (initial === '#') {
                if (this.callback(module.exports.Tokens.T_COMMENT, token, spos, epos, line)) {
                    return 'done';
                }
            }
            else if (triple_quoted.hasOwnProperty(token)) {
                this.endprog = endprogs[token];
                this.endprog.lastIndex = 0;
                endmatch = this.endprog.test(line.substring(pos));
                if (endmatch) {
                    pos = this.endprog.lastIndex + pos;
                    token = line.substring(start, pos);
                    if (this.callback(module.exports.Tokens.T_STRING, token, spos, [this.lnum, pos], line)) {
                        return 'done';
                    }
                }
                else {
                    this.strstart = [this.lnum, start];
                    this.contstr = line.substring(start);
                    this.contline = line;
                    return false;
                }
            }
            else if (single_quoted.hasOwnProperty(initial) ||
                single_quoted.hasOwnProperty(token.substring(0, 2)) ||
                single_quoted.hasOwnProperty(token.substring(0, 3))) {
                if (token[token.length - 1] === '\n') {
                    this.strstart = [this.lnum, start];
                    this.endprog = endprogs[initial] || endprogs[token[1]] || endprogs[token[2]];
                    this.contstr = line.substring(start);
                    this.needcont = true;
                    this.contline = line;
                    //print("i, t1, t2", initial, token[1], token[2]);
                    //print("ep, cs", this.endprog, this.contstr);
                    return false;
                }
                else {
                    if (this.callback(module.exports.Tokens.T_STRING, token, spos, epos, line)) {
                        return 'done';
                    }
                }
            }
            else if (this.namechars.indexOf(initial) !== -1) {
                if (this.callback(module.exports.Tokens.T_NAME, token, spos, epos, line)) {
                    return 'done';
                }
            }
            else if (initial === '\\') {
                //print("HERE:4");
                if (this.callback(module.exports.Tokens.T_NL, token, spos, [this.lnum, pos], line)) {
                    return 'done';
                }
                this.continued = true;
            }
            else {
                if ('([{'.indexOf(initial) !== -1) {
                    this.parenlev += 1;
                }
                else if (')]}'.indexOf(initial) !== -1) {
                    this.parenlev -= 1;
                }
                if (this.callback(module.exports.Tokens.T_OP, token, spos, epos, line)) {
                    return 'done';
                }
            }
        }
        else {
            if (this.callback(module.exports.Tokens.T_ERRORTOKEN, line.charAt(pos),
                [this.lnum, pos], [this.lnum, pos + 1], line)) {
                return 'done';
            }
            pos += 1;
        }
    }

    return false;
};

module.exports.tokenNames = {
    0  : 'T_ENDMARKER', 1: 'T_NAME', 2: 'T_NUMBER', 3: 'T_STRING', 4: 'T_NEWLINE',
    5  : 'T_INDENT', 6: 'T_DEDENT', 7: 'T_LPAR', 8: 'T_RPAR', 9: 'T_LSQB',
    10 : 'T_RSQB', 11: 'T_COLON', 12: 'T_COMMA', 13: 'T_SEMI', 14: 'T_PLUS',
    15 : 'T_MINUS', 16: 'T_STAR', 17: 'T_SLASH', 18: 'T_VBAR', 19: 'T_AMPER',
    20 : 'T_LESS', 21: 'T_GREATER', 22: 'T_EQUAL', 23: 'T_DOT', 24: 'T_PERCENT',
    25 : 'T_BACKQUOTE', 26: 'T_LBRACE', 27: 'T_RBRACE', 28: 'T_EQEQUAL', 29: 'T_NOTEQUAL',
    30 : 'T_LESSEQUAL', 31: 'T_GREATEREQUAL', 32: 'T_TILDE', 33: 'T_CIRCUMFLEX', 34: 'T_LEFTSHIFT',
    35 : 'T_RIGHTSHIFT', 36: 'T_DOUBLESTAR', 37: 'T_PLUSEQUAL', 38: 'T_MINEQUAL', 39: 'T_STAREQUAL',
    40 : 'T_SLASHEQUAL', 41: 'T_PERCENTEQUAL', 42: 'T_AMPEREQUAL', 43: 'T_VBAREQUAL', 44: 'T_CIRCUMFLEXEQUAL',
    45 : 'T_LEFTSHIFTEQUAL', 46: 'T_RIGHTSHIFTEQUAL', 47: 'T_DOUBLESTAREQUAL', 48: 'T_DOUBLESLASH', 49: 'T_DOUBLESLASHEQUAL',
    50 : 'T_AT', 51: 'T_OP', 52: 'T_COMMENT', 53: 'T_NL', 54: 'T_RARROW',
    55 : 'T_ERRORTOKEN', 56: 'T_N_TOKENS',
    256: 'T_NT_OFFSET'
};

},{}]},{},[3])(3)
});