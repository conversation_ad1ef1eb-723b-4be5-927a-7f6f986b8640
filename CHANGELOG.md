# Change Log

All notable changes to the "vscode-droplet" extension will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [0.1.0] - 2025-01-XX

### Added
- Initial release of Droplet Visual Programming extension for VS Code
- Support for JavaScript, Python, and CoffeeScript visual programming
- Custom editor provider for `.visual.js`, `.visual.py`, and `.visual.coffee` files
- Bidirectional conversion between text and visual block programming
- Context menu integration for opening existing files in visual editor
- Command palette commands for creating new visual programming files
- Toggle functionality between text and visual editing modes
- Lightweight custom block-based editor optimized for VS Code
- Basic test suite for extension functionality
- Comprehensive documentation and examples

### Fixed
- Resolved "Droplet library not loaded" error by implementing custom lightweight editor
- Replaced external Droplet dependency with self-contained block editor implementation
- Improved reliability and performance by removing external CDN dependencies

### Features
- **Visual Programming Interface**: Drag-and-drop block-based programming
- **File Integration**: Open existing code files in visual editor
- **Language Support**: JavaScript, Python, and CoffeeScript
- **Mode Switching**: Seamless toggle between text and visual modes
- **VS Code Integration**: Native file explorer and command palette support
- **Syntax Highlighting**: Proper syntax highlighting in both modes
- **Auto-save**: Automatic synchronization between text and visual representations

### Technical Details
- Built using VS Code Extension API
- Integrates Droplet editor library via webview
- TypeScript implementation with comprehensive type safety
- Custom editor provider for seamless file handling
- Webview-based UI with VS Code theme integration

## [Unreleased]

### Planned Features
- Support for additional programming languages (Java, C++, etc.)
- Custom block creation and library management
- Debugging support in visual mode
- Performance improvements for large files
- Collaborative editing features
- Block library sharing and import/export
- Advanced syntax validation and error highlighting
- Integration with VS Code's IntelliSense
- Plugin system for custom language support
