{"name": "antlr4", "version": "4.13.2", "type": "module", "description": "JavaScript runtime for ANTLR4", "browser": "dist/antlr4.web.mjs", "main": "dist/antlr4.node.cjs", "module": "dist/antlr4.node.mjs", "types": "src/antlr4/index.d.cts", "repository": "antlr/antlr4.git", "keywords": ["lexer", "parser", "antlr", "antlr4", "grammar"], "files": ["dist", "src/**/*.d.cts", "src/**/*.d.ts"], "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/antlr/antlr4/issues"}, "homepage": "https://github.com/antlr/antlr4", "devDependencies": {"@babel/core": "^7.19.1", "@babel/eslint-parser": "^7.19.1", "@babel/preset-env": "^7.19.4", "@types/node": "^18.7.23", "babel-loader": "^8.2.5", "c8": "^7.12.0", "compression-webpack-plugin": "^10.0.0", "eslint": "^8.23.1", "eslint-webpack-plugin": "^3.2.0", "glob-parent": "^6.0.2", "jasmine": "^4.0.2", "jasmine-spec-reporter": "^7.0.0", "minimist": "^1.2.6", "source-map-support": "^0.5.21", "terser-webpack-plugin": "^5.3.6", "typescript": "^4.8.3", "webpack": "^5.76.0", "webpack-cli": "^4.10.0"}, "scripts": {"build": "webpack", "test": "jasmine", "coverage": "c8 jasmine", "lint": "eslint src/antlr4/"}, "engines": {"node": ">=16"}, "exports": {".": {"types": "./src/antlr4/index.d.cts", "node": {"import": "./dist/antlr4.node.mjs", "require": "./dist/antlr4.node.cjs", "default": "./dist/antlr4.node.mjs"}, "browser": {"import": "./dist/antlr4.web.mjs", "require": "./dist/antlr4.web.cjs", "default": "./dist/antlr4.web.mjs"}}}}