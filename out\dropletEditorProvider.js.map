{"version": 3, "file": "dropletEditorProvider.js", "sourceRoot": "", "sources": ["../src/dropletEditorProvider.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AACjC,6BAA6B;AAE7B,MAAa,qBAAqB;IAG9B,YAA6B,OAAgC;QAAhC,YAAO,GAAP,OAAO,CAAyB;IAAG,CAAC;IAE1D,MAAM,CAAC,QAAQ,CAAC,OAAgC;QACnD,MAAM,QAAQ,GAAG,IAAI,qBAAqB,CAAC,OAAO,CAAC,CAAC;QACpD,MAAM,oBAAoB,GAAG,MAAM,CAAC,MAAM,CAAC,4BAA4B,CACnE,qBAAqB,CAAC,QAAQ,EAC9B,QAAQ,CACX,CAAC;QACF,OAAO,oBAAoB,CAAC;IAChC,CAAC;IAEM,KAAK,CAAC,uBAAuB,CAChC,QAA6B,EAC7B,YAAiC,EACjC,MAAgC;QAEhC,wCAAwC;QACxC,YAAY,CAAC,OAAO,CAAC,OAAO,GAAG;YAC3B,aAAa,EAAE,IAAI;YACnB,kBAAkB,EAAE;gBAChB,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,SAAS,CAAC;gBACzD,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,cAAc,CAAC;aACjE;SACJ,CAAC;QAEF,YAAY,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QAEnF,SAAS,aAAa;YAClB,YAAY,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC7B,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,QAAQ,CAAC,OAAO,EAAE;gBACxB,QAAQ,EAAE,uBAAuB,CAAC,QAAQ,CAAC,QAAQ,CAAC;aACvD,CAAC,CAAC;QACP,CAAC;QAED,uFAAuF;QACvF,MAAM,0BAA0B,GAAG,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC,CAAC,EAAE;YAC5E,IAAI,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE;gBACvD,aAAa,EAAE,CAAC;aACnB;QACL,CAAC,CAAC,CAAC;QAEH,iEAAiE;QACjE,YAAY,CAAC,YAAY,CAAC,GAAG,EAAE;YAC3B,0BAA0B,CAAC,OAAO,EAAE,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,mCAAmC;QACnC,YAAY,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,EAAE;YACzC,QAAQ,CAAC,CAAC,IAAI,EAAE;gBACZ,KAAK,MAAM;oBACP,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;oBAC1C,OAAO;gBACX,KAAK,OAAO;oBACR,aAAa,EAAE,CAAC;oBAChB,OAAO;aACd;QACL,CAAC,CAAC,CAAC;QAEH,aAAa,EAAE,CAAC;IACpB,CAAC;IAEO,iBAAiB,CAAC,OAAuB,EAAE,QAA6B;QAC5E,2GAA2G;QAC3G,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC;QAC7G,MAAM,QAAQ,GAAG,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC;QAE9G,wCAAwC;QACxC,MAAM,UAAU,GAAG,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,cAAc,EAAE,gBAAgB,EAAE,MAAM,EAAE,qBAAqB,CAAC,CAAC,CAAC;QAEzJ,wDAAwD;QACxD,MAAM,KAAK,GAAG,QAAQ,EAAE,CAAC;QAEzB,OAAO;;;;oGAIqF,OAAO,CAAC,SAAS,uCAAuC,KAAK;;8BAEnI,QAAQ;;;;;;;6DAOuB,uBAAuB,CAAC,QAAQ,CAAC,QAAQ,CAAC;;;;;iCAKtE,KAAK,UAAU,UAAU;iCACzB,KAAK,UAAU,SAAS;;oBAErC,CAAC;IACjB,CAAC;IAEO,kBAAkB,CAAC,QAA6B,EAAE,IAAY;QAClE,MAAM,IAAI,GAAG,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;QAExC,sCAAsC;QACtC,IAAI,CAAC,OAAO,CACR,QAAQ,CAAC,GAAG,EACZ,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC,EAC7C,IAAI,CACP,CAAC;QAEF,OAAO,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IAC5C,CAAC;;AA/GL,sDAgHC;AA/G2B,8BAAQ,GAAG,gBAAgB,CAAC;AAiHxD,SAAS,QAAQ;IACb,IAAI,IAAI,GAAG,EAAE,CAAC;IACd,MAAM,QAAQ,GAAG,gEAAgE,CAAC;IAClF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;QACzB,IAAI,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;KACxE;IACD,OAAO,IAAI,CAAC;AAChB,CAAC;AAED,SAAS,uBAAuB,CAAC,QAAgB;IAC7C,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;IACnC,QAAQ,GAAG,EAAE;QACT,KAAK,KAAK;YACN,OAAO,YAAY,CAAC;QACxB,KAAK,KAAK;YACN,OAAO,QAAQ,CAAC;QACpB,KAAK,SAAS;YACV,OAAO,cAAc,CAAC;QAC1B;YACI,OAAO,YAAY,CAAC;KAC3B;AACL,CAAC"}