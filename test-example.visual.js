// Test file for Droplet Visual Programming Extension
// This file can be opened in the visual editor to test functionality

console.log("Hello, Visual Programming!");

let message = "Welcome to Droplet";
const version = "1.0";

function greet(name) {
    return "Hello, " + name + "!";
}

if (message) {
    console.log(greet("World"));
}

for (let i = 0; i < 3; i++) {
    console.log("Count: " + i);
}
