{"name": "vscode-droplet", "displayName": "Droplet Visual Programming", "description": "Block-based visual programming editor integration for VS Code using Droplet", "version": "0.1.0", "publisher": "droplet-editor", "engines": {"vscode": "^1.74.0"}, "categories": ["Other", "Education"], "keywords": ["visual programming", "blocks", "education", "beginner", "scratch"], "activationEvents": ["onCommand:droplet.openEditor", "onCommand:droplet.createNew", "onCustomEditor:droplet.editor"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "droplet.openEditor", "title": "Open in Droplet Visual Editor", "category": "Droplet"}, {"command": "droplet.createNew", "title": "Create New Visual Programming File", "category": "Droplet"}, {"command": "droplet.toggleMode", "title": "Toggle Text/Visual Mode", "category": "Droplet"}], "menus": {"explorer/context": [{"command": "droplet.openEditor", "when": "resourceExtname =~ /\\.(js|py|coffee)$/", "group": "navigation"}], "editor/title": [{"command": "droplet.toggleMode", "when": "activeCustomEditorId == droplet.editor", "group": "navigation"}], "commandPalette": [{"command": "droplet.openEditor", "when": "editorIsOpen"}, {"command": "droplet.createNew"}, {"command": "droplet.toggleMode", "when": "activeCustomEditorId == droplet.editor"}]}, "customEditors": [{"viewType": "droplet.editor", "displayName": "Droplet Visual Editor", "selector": [{"filenamePattern": "*.visual.js"}, {"filenamePattern": "*.visual.py"}, {"filenamePattern": "*.visual.coffee"}], "priority": "option"}]}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile", "test": "node ./out/test/runTest.js"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "@types/mocha": "^10.0.1", "@types/glob": "^8.0.0", "@vscode/test-electron": "^2.2.0", "typescript": "^4.9.4", "mocha": "^10.1.0", "glob": "^8.0.3"}, "dependencies": {"droplet-editor": "^0.0.2"}, "repository": {"type": "git", "url": "https://github.com/droplet-editor/vscode-droplet.git"}, "bugs": {"url": "https://github.com/droplet-editor/vscode-droplet/issues"}, "homepage": "https://github.com/droplet-editor/vscode-droplet#readme"}