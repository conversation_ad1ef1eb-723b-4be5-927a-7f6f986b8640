# Droplet Visual Programming for VS Code

A Visual Studio Code extension that integrates the [Droplet block-based visual programming editor](https://github.com/droplet-editor/droplet) to provide a seamless transition between text-based and visual programming.

## Features

- **Visual Programming Interface**: Edit JavaScript, Python, and CoffeeScript files using drag-and-drop blocks
- **Bidirectional Conversion**: Seamlessly switch between text and visual editing modes
- **File Integration**: Open existing code files in the visual editor
- **New File Creation**: Create new visual programming files directly from VS Code
- **Language Support**: Full support for JavaScript, Python, and CoffeeScript
- **VS Code Integration**: Native file explorer integration and command palette support

## Installation

### Quick Install (Recommended)

The extension is ready to install! A pre-built package is included:

1. **Install the extension:**
   ```bash
   code --install-extension vscode-droplet-0.1.0.vsix
   ```

2. **Restart VS Code** to activate the extension

3. **Verify installation** by opening Command Palette (`Ctrl+Shift+P`) and typing "Droplet"

### From Source (Development)

1. Clone this repository:
   ```bash
   git clone https://github.com/droplet-editor/vscode-droplet.git
   cd vscode-droplet
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Compile the extension:
   ```bash
   npm run compile
   ```

4. **Option A - Development Mode:**
   - Open the project in VS Code and press `F5` to launch Extension Development Host

5. **Option B - Package and Install:**
   ```bash
   npx vsce package --no-dependencies
   code --install-extension vscode-droplet-0.1.0.vsix
   ```

## Usage

### Opening Existing Files in Visual Editor

1. **Right-click method**: Right-click on any `.js`, `.py`, or `.coffee` file in the Explorer and select "Open in Droplet Visual Editor"

2. **Command Palette**: 
   - Open Command Palette (`Ctrl+Shift+P` / `Cmd+Shift+P`)
   - Type "Droplet: Open in Droplet Visual Editor"
   - Select the command

### Creating New Visual Programming Files

1. **Command Palette**:
   - Open Command Palette (`Ctrl+Shift+P` / `Cmd+Shift+P`)
   - Type "Droplet: Create New Visual Programming File"
   - Select your preferred language (JavaScript, Python, or CoffeeScript)
   - Enter a filename

### Switching Between Text and Visual Modes

- Click the "Toggle Text/Visual Mode" button in the editor toolbar
- Use the Command Palette: "Droplet: Toggle Text/Visual Mode"

### Saving Files

- Use `Ctrl+S` / `Cmd+S` to save changes
- Click the "Save" button in the editor toolbar
- Changes are automatically synchronized between text and visual modes

## Supported Languages

### JavaScript
- Full ES6+ syntax support
- Common functions and methods
- Control flow structures (if/else, loops)
- Function definitions and calls

### Python
- Python 3 syntax support
- Built-in functions
- Control structures
- Function definitions with proper indentation

### CoffeeScript
- CoffeeScript syntax support
- Simplified function syntax
- Control structures
- Method chaining

## File Naming Convention

The extension uses a special naming convention for visual programming files:
- `filename.visual.js` for JavaScript
- `filename.visual.py` for Python
- `filename.visual.coffee` for CoffeeScript

This allows VS Code to properly associate files with the Droplet editor while maintaining compatibility with standard file extensions.

## Extension Commands

| Command | Description |
|---------|-------------|
| `droplet.openEditor` | Open current file in Droplet Visual Editor |
| `droplet.createNew` | Create a new visual programming file |
| `droplet.toggleMode` | Toggle between text and visual editing modes |

## Configuration

The extension works out of the box with sensible defaults. The visual editor includes:

- **Color-coded blocks** for different programming constructs
- **Drag-and-drop interface** for building programs
- **Syntax validation** in real-time
- **Auto-completion** for common programming patterns

## Troubleshooting

### Editor Not Loading
- Ensure you have a stable internet connection (for loading Droplet dependencies)
- Check the VS Code Developer Console for error messages
- Try reloading the window (`Ctrl+R` / `Cmd+R`)

### File Not Opening in Visual Editor
- Verify the file has a supported extension (`.js`, `.py`, `.coffee`)
- Check that the file is not too large (>1MB files may have performance issues)
- Ensure the file contains valid syntax for the detected language

### Blocks Not Displaying Correctly
- Try toggling between text and visual modes
- Check that the code follows the supported syntax patterns
- Some advanced language features may not have visual block representations

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## Development

### Building
```bash
npm run compile
```

### Watching for Changes
```bash
npm run watch
```

### Testing
```bash
npm test
```

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- [Droplet Editor](https://github.com/droplet-editor/droplet) - The amazing block-based editor that powers this extension
- [Visual Studio Code](https://code.visualstudio.com/) - The extensible editor platform
- The open-source community for inspiration and support

## Roadmap

- [ ] Add support for more programming languages
- [ ] Implement custom block creation
- [ ] Add debugging support in visual mode
- [ ] Improve performance for large files
- [ ] Add collaborative editing features
- [ ] Implement block library sharing
