T__0=1
T__1=2
T__2=3
T__3=4
T__4=5
T__5=6
T__6=7
T__7=8
T__8=9
T__9=10
T__10=11
T__11=12
T__12=13
T__13=14
Auto=15
Break=16
Case=17
Char=18
Const=19
Continue=20
Default=21
Do=22
Double=23
Else=24
Enum=25
Extern=26
Float=27
For=28
Goto=29
If=30
Inline=31
Int=32
Long=33
Register=34
Restrict=35
Return=36
Short=37
Signed=38
Sizeof=39
Static=40
Struct=41
Switch=42
Typedef=43
Union=44
Unsigned=45
Void=46
Volatile=47
While=48
Alignas=49
Alignof=50
Atomic=51
Bool=52
Complex=53
Generic=54
Imaginary=55
Noreturn=56
StaticAssert=57
ThreadLocal=58
LeftParen=59
RightParen=60
LeftBracket=61
RightBracket=62
LeftBrace=63
RightBrace=64
Less=65
LessEqual=66
Greater=67
GreaterEqual=68
LeftShift=69
RightShift=70
Plus=71
PlusPlus=72
Minus=73
MinusMinus=74
Star=75
Div=76
Mod=77
And=78
Or=79
AndAnd=80
OrOr=81
Caret=82
Not=83
Tilde=84
Question=85
Colon=86
Semi=87
Comma=88
Assign=89
StarAssign=90
DivAssign=91
ModAssign=92
PlusAssign=93
MinusAssign=94
LeftShiftAssign=95
RightShiftAssign=96
AndAssign=97
XorAssign=98
OrAssign=99
Equal=100
NotEqual=101
Arrow=102
Dot=103
Ellipsis=104
Identifier=105
Constant=106
StringLiteral=107
LineDirective=108
PragmaDirective=109
Whitespace=110
Newline=111
BlockComment=112
LineComment=113
'__extension__'=1
'__builtin_va_arg'=2
'__builtin_offsetof'=3
'__m128'=4
'__m128d'=5
'__m128i'=6
'__typeof__'=7
'__inline__'=8
'__stdcall'=9
'__declspec'=10
'__asm'=11
'__attribute__'=12
'__asm__'=13
'__volatile__'=14
'auto'=15
'break'=16
'case'=17
'char'=18
'const'=19
'continue'=20
'default'=21
'do'=22
'double'=23
'else'=24
'enum'=25
'extern'=26
'float'=27
'for'=28
'goto'=29
'if'=30
'inline'=31
'int'=32
'long'=33
'register'=34
'restrict'=35
'return'=36
'short'=37
'signed'=38
'sizeof'=39
'static'=40
'struct'=41
'switch'=42
'typedef'=43
'union'=44
'unsigned'=45
'void'=46
'volatile'=47
'while'=48
'_Alignas'=49
'_Alignof'=50
'_Atomic'=51
'_Bool'=52
'_Complex'=53
'_Generic'=54
'_Imaginary'=55
'_Noreturn'=56
'_Static_assert'=57
'_Thread_local'=58
'('=59
')'=60
'['=61
']'=62
'{'=63
'}'=64
'<'=65
'<='=66
'>'=67
'>='=68
'<<'=69
'>>'=70
'+'=71
'++'=72
'-'=73
'--'=74
'*'=75
'/'=76
'%'=77
'&'=78
'|'=79
'&&'=80
'||'=81
'^'=82
'!'=83
'~'=84
'?'=85
':'=86
';'=87
','=88
'='=89
'*='=90
'/='=91
'%='=92
'+='=93
'-='=94
'<<='=95
'>>='=96
'&='=97
'^='=98
'|='=99
'=='=100
'!='=101
'->'=102
'.'=103
'...'=104
