(function() {
    const vscode = acquireVsCodeApi();
    
    let dropletEditor = null;
    let currentLanguage = 'javascript';
    let isBlockMode = true;
    let currentText = '';

    // Initialize the editor when the page loads
    window.addEventListener('load', function() {
        initializeDropletEditor();
        setupEventListeners();
        
        // Notify VS Code that the webview is ready
        vscode.postMessage({ type: 'ready' });
    });

    function initializeDropletEditor() {
        const container = document.getElementById('droplet-editor');

        if (!container) {
            showError('Editor container not found');
            return;
        }

        // Show loading state
        container.innerHTML = '<div class="loading">Loading Droplet Editor...</div>';

        try {
            // Check if droplet is available
            if (typeof droplet === 'undefined') {
                throw new Error('Droplet library not loaded');
            }

            // Create the Droplet editor instance
            dropletEditor = new droplet.Editor(container, {
                mode: currentLanguage
            });

            // Set up event handlers
            dropletEditor.on('change', function() {
                currentText = dropletEditor.getValue();
                saveToVSCode();
            });

            dropletEditor.on('toggledone', function() {
                isBlockMode = dropletEditor.currentlyUsingBlocks;
                updateModeIndicator();
            });

            // Add mode indicator
            addModeIndicator();

        } catch (error) {
            console.error('Failed to initialize Droplet editor:', error);
            showError('Failed to initialize Droplet editor: ' + error.message);
        }
    }

    function setupEventListeners() {
        // Toggle mode button
        const toggleButton = document.getElementById('toggleMode');
        if (toggleButton) {
            toggleButton.addEventListener('click', function() {
                if (dropletEditor) {
                    dropletEditor.setEditorState(!dropletEditor.currentlyUsingBlocks);
                }
            });
        }

        // Save button
        const saveButton = document.getElementById('saveFile');
        if (saveButton) {
            saveButton.addEventListener('click', function() {
                saveToVSCode();
            });
        }
    }

    function saveToVSCode() {
        if (dropletEditor) {
            const text = dropletEditor.getValue();
            vscode.postMessage({
                type: 'save',
                text: text
            });
        }
    }

    function addModeIndicator() {
        const indicator = document.createElement('div');
        indicator.id = 'mode-indicator';
        indicator.className = 'mode-indicator';
        document.getElementById('editor-container').appendChild(indicator);
        updateModeIndicator();
    }

    function updateModeIndicator() {
        const indicator = document.getElementById('mode-indicator');
        if (indicator) {
            indicator.textContent = isBlockMode ? 'Visual Mode' : 'Text Mode';
        }
    }

    function showError(message) {
        const container = document.getElementById('droplet-editor');
        if (container) {
            container.innerHTML = `<div class="error">${message}</div>`;
        }
    }

    // Palette and mode options are now handled by our minimal Droplet implementation

    // Handle messages from VS Code
    window.addEventListener('message', event => {
        const message = event.data;
        
        switch (message.type) {
            case 'update':
                if (dropletEditor && message.text !== currentText) {
                    currentText = message.text;
                    currentLanguage = message.language;
                    
                    // Update the editor content
                    dropletEditor.setValue(message.text);
                    
                    // Update language indicator
                    const languageIndicator = document.getElementById('languageIndicator');
                    if (languageIndicator) {
                        languageIndicator.textContent = `Language: ${currentLanguage}`;
                    }
                }
                break;
        }
    });
})();
