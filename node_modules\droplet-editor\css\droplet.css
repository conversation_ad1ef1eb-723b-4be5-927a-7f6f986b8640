.droplet-palette-canvas, .droplet-drag-canvas, .droplet-main-canvas, .droplet-lasso-select-canvas, .droplet-highlight-canvas {
  position: absolute;
}
.droplet-main-canvas, .droplet-drag-canvas, .droplet-lasso-select-canvas, .droplet-highlight-canvas {
  top: 0;
}
.droplet-drag-canvas {
  z-index: 258;
  margin-top: -2px;
  margin-left: -1px;
  opacity: 0.85;
}
.droplet-palette-canvas {
  z-index: 1;
  width: 300px;
}
.droplet-main-canvas {
  background-color:#FFF;
}
.droplet-main-canvas {
  z-index: 0;
  border-right: none;
}
.droplet-lasso-select-canvas {
  left: 0;
  z-index: 3;
  border-right: none;
}
.droplet-highlight-canvas {
  left: 0;
  z-index: 2;
  border-right: none;
}
.droplet-palette-canvas {
  background-color:#FFF;
}
.droplet-wrapper-div {
  position: absolute;
  top: 0; left: 0;
  cursor: default;
  background-color: #FFF;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  overflow: hidden;
  outline: none;
}
.droplet-ace {
  position: absolute;
  top: -9999px; left: -9999px;
  font-family: Courier, monospace;
}
.droplet-main-scroller {
  position: absolute;
  top: 0; left: 0; bottom: 0; right: 0;
  z-index: 300;
  overflow: auto;
}
.droplet-palette-scroller {
  position: absolute;
  left: 0; top: 150px; bottom: 0; right: 0;
  z-index: 300;
  overflow-y: auto;
  overflow-x: hidden;
}
.droplet-palette-wrapper {
  position: absolute;
  top: 0; bottom: 0; width: 300px;
  max-width: 40%;
  z-index: 257;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.droplet-palette-element {
  position: absolute;
  height: 100%;
  width: 100%;
  overflow: hidden;
}
.droplet-palette-header {
  z-index: 257;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  background-color: #FFF;
  line-height: 10px;
  border-spacing: 4px;
  border-bottom: 2px solid #000;
  display: table;
}
.droplet-palette-header-row {
  display: table-row;
  height: 30px;
  width: 100%;
}
.droplet-palette-group-header {
  display: table-cell;
  width: 50%;
  font-family: sans-serif;
  line-height: 30px;
  cursor: pointer;
  overflow: hidden;
  padding-left: 10px;
  border-left: 10px solid;
  color: dimgray;
}
.droplet-palette-group-header-selected {
  display: table-cell;
  width: 50%;
  cursor: pointer;
  background-color: #000;
  color: #FFF;
  font-weight: bold;
}
.droplet-palette-group-header.command { border-left: 10px solid #8fbfef; }
.droplet-palette-group-header-selected.command { background-color: #8fbfef; }
.droplet-palette-group-header.value { border-left: 10px solid #8cec79; }
.droplet-palette-group-header-selected.value { background-color: #8cec79; }
.droplet-palette-group-header.control { border-left: 10px solid #efcf8f; }
.droplet-palette-group-header-selected.control { background-color: #efcf8f; }
.droplet-palette-group-header.return { border-left: 10px solid #f2a6a6; }
.droplet-palette-group-header-selected.return { background-color: #f2a6a6; }
.droplet-palette-group-header.containers { border-left: 10px solid #bfa6f2; }
.droplet-palette-group-header-selected.containers { background-color: #bfa6f2; }
.droplet-palette-group-header.logic { border-left: 10px solid #ecec79; }
.droplet-palette-group-header-selected.logic { background-color: #ecec79; }
.droplet-palette-group-header.math { border-left: 10px solid #f2a6e5; }
.droplet-palette-group-header-selected.math { background-color: #f2a6e5; }

.droplet-hidden-input {
  position: absolute;
  z-index: -999;
  opacity: 0;
  -webkit-touch-callout: none;
  width: 1px;
  height: 1px;
  padding: 0;
  border: 0;
  margin: 0;
}
.droplet-hover-div {
  position: absolute;
}
.droplet-main-scroller {
  z-index: 10;
}
.droplet-main-scroller-stuffing {
  z-index: 10;
}
.droplet-palette-scroller-stuffing {
  /* Temporary hack to prevent bad side-scrolling
   * behaviour when dragging blocks out of the palette */
  overflow-x: hidden;
}
.droplet-transition-container {
  position: absolute;
  top: 0; bottom: 0; left: 0; right: 0;
  overflow: visible;
}
.droplet-gutter {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  background-color: #ebebeb;
  height: 100%;
  box-sizing: border-box;
  font-size: 15px;
  font-family: Courier New;
}
.droplet-gutter-line {
  position: absolute;
  box-sizing: border-box;
  text-align: right;
  padding-right: 13px;
  line-height: normal;
  width: 100%;
}
.droplet-drag-cover {
  position: absolute;
  top: 0; bottom: 0; right: 0; left: 0;
  z-index: 9999;
}
.droplet-transitioning-element {
  position: absolute;
  white-space: pre;
  z-index: 256;
}
.droplet-transitioning-gutter {
  box-sizing: border-box;
  text-align: right;
  padding-right: 13px;
}
.droplet-palette-highlight-canvas {
  position: absolute;
  z-index: 257;
}
.droplet-palette-group-header.red { border-left: 10px solid #ef9a9a; }
.droplet-palette-group-header.pink { border-left: 10px solid #f48fb1; }
.droplet-palette-group-header.purple { border-left: 10px solid #ce93d8; }
.droplet-palette-group-header.deeppurple { border-left: 10px solid #b39ddb; }
.droplet-palette-group-header.indigo { border-left: 10px solid #9fa8da; }
.droplet-palette-group-header.blue { border-left: 10px solid #90caf9; }
.droplet-palette-group-header.lightblue { border-left: 10px solid #81d4fa; }
.droplet-palette-group-header.cyan { border-left: 10px solid #80deea; }
.droplet-palette-group-header.teal { border-left: 10px solid #80cbc4; }
.droplet-palette-group-header.green { border-left: 10px solid #a5d6a7; }
.droplet-palette-group-header.lightgreen { border-left: 10px solid #c5e1a5; }
.droplet-palette-group-header.lime { border-left: 10px solid #e6ee9c; }
.droplet-palette-group-header.yellow { border-left: 10px solid #fff59d; }
.droplet-palette-group-header.amber { border-left: 10px solid #ffe082; }
.droplet-palette-group-header.orange { border-left: 10px solid #ffcc80; }
.droplet-palette-group-header.deeporange { border-left: 10px solid #ffab91; }
.droplet-palette-group-header.brown { border-left: 10px solid #bcaaa4; }
.droplet-palette-group-header.grey { border-left: 10px solid #eeeeee; }
.droplet-palette-group-header.bluegrey { border-left: 10px solid #b0bec5; }

.droplet-palette-group-header-selected.red { background-color: #ef9a9a; }
.droplet-palette-group-header-selected.pink { background-color: #f48fb1; }
.droplet-palette-group-header-selected.purple { background-color: #ce93d8; }
.droplet-palette-group-header-selected.deeppurple { background-color: #b39ddb; }
.droplet-palette-group-header-selected.indigo { background-color: #9fa8da; }
.droplet-palette-group-header-selected.blue { background-color: #90caf9; }
.droplet-palette-group-header-selected.lightblue { background-color: #81d4fa; }
.droplet-palette-group-header-selected.cyan { background-color: #80deea; }
.droplet-palette-group-header-selected.teal { background-color: #80cbc4; }
.droplet-palette-group-header-selected.green { background-color: #a5d6a7; }
.droplet-palette-group-header-selected.lightgreen { background-color: #c5e1a5; }
.droplet-palette-group-header-selected.lime { background-color: #e6ee9c; }
.droplet-palette-group-header-selected.yellow { background-color: #fff59d; }
.droplet-palette-group-header-selected.amber { background-color: #ffe082; }
.droplet-palette-group-header-selected.orange { background-color: #ffcc80; }
.droplet-palette-group-header-selected.deeporange { background-color: #ffab91; }
.droplet-palette-group-header-selected.brown { background-color: #bcaaa4; }
.droplet-palette-group-header-selected.grey { background-color: #eeeeee; }
.droplet-palette-group-header-selected.bluegrey { background-color: #b0bec5; }

.droplet-cursor-canvas {
  opacity: 0.5;
}
.droplet-dropdown {
  display: none;
  max-height: 150px;
  position: absolute;
  background-color: #FFF;
  z-index: 10000;
  border: 1px solid #DDD;
  border-radius: 2px;
  overflow-y: auto;
  overflow-x: hidden;
}
.droplet-dropdown-item {
  cursor: pointer;
  padding-right: 5px;
}
.droplet-dropdown-item:hover {
  background-color: #DDD;
}
.droplet_error {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAMAAAAoLQ9TAAABOFBMVEX/////////QRswFAb/Ui4wFAYwFAYwFAaWGAfDRymzOSH/PxswFAb/SiUwFAYwFAbUPRvjQiDllog5HhHdRybsTi3/Tyv9Tir+Syj/UC3////XurebMBIwFAb/RSHbPx/gUzfdwL3kzMivKBAwFAbbvbnhPx66NhowFAYwFAaZJg8wFAaxKBDZurf/RB6mMxb/SCMwFAYwFAbxQB3+RB4wFAb/Qhy4Oh+4QifbNRcwFAYwFAYwFAb/QRzdNhgwFAYwFAbav7v/Uy7oaE68MBK5LxLewr/r2NXewLswFAaxJw4wFAbkPRy2PyYwFAaxKhLm1tMwFAazPiQwFAaUGAb/QBrfOx3bvrv/VC/maE4wFAbRPBq6MRO8Qynew8Dp2tjfwb0wFAbx6eju5+by6uns4uH9/f36+vr/GkHjAAAAYnRSTlMAGt+64rnWu/bo8eAA4InH3+DwoN7j4eLi4xP99Nfg4+b+/u9B/eDs1MD1mO7+4PHg2MXa347g7vDizMLN4eG+Pv7i5evs/v79yu7S3/DV7/498Yv24eH+4ufQ3Ozu/v7+y13sRqwAAADLSURBVHjaZc/XDsFgGIBhtDrshlitmk2IrbHFqL2pvXf/+78DPokj7+Fz9qpU/9UXJIlhmPaTaQ6QPaz0mm+5gwkgovcV6GZzd5JtCQwgsxoHOvJO15kleRLAnMgHFIESUEPmawB9ngmelTtipwwfASilxOLyiV5UVUyVAfbG0cCPHig+GBkzAENHS0AstVF6bacZIOzgLmxsHbt2OecNgJC83JERmePUYq8ARGkJx6XtFsdddBQgZE2nPR6CICZhawjA4Fb/chv+399kfR+MMMDGOQAAAABJRU5ErkJggg==");
  background-repeat: no-repeat;
  background-position: 2px center;
}

.droplet_warning {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAMAAAAoLQ9TAAAAmVBMVEX///8AAAD///8AAAAAAABPSzb/5sAAAAB/blH/73z/ulkAAAAAAAD85pkAAAAAAAACAgP/vGz/rkDerGbGrV7/pkQICAf////e0IsAAAD/oED/qTvhrnUAAAD/yHD/njcAAADuv2r/nz//oTj/p064oGf/zHAAAAA9Nir/tFIAAAD/tlTiuWf/tkIAAACynXEAAAAAAAAtIRW7zBpBAAAAM3RSTlMAABR1m7RXO8Ln31Z36zT+neXe5OzooRDfn+TZ4p3h2hTf4t3k3ucyrN1K5+Xaks52Sfs9CXgrAAAAjklEQVR42o3PbQ+CIBQFYEwboPhSYgoYunIqqLn6/z8uYdH8Vmdnu9vz4WwXgN/xTPRD2+sgOcZjsge/whXZgUaYYvT8QnuJaUrjrHUQreGczuEafQCO/SJTufTbroWsPgsllVhq3wJEk2jUSzX3CUEDJC84707djRc5MTAQxoLgupWRwW6UB5fS++NV8AbOZgnsC7BpEAAAAABJRU5ErkJggg==");
  background-repeat: no-repeat;
  background-position: 2px center;
}

.droplet_info {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAAAAAA6mKC9AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAAJ0Uk5TAAB2k804AAAAPklEQVQY02NgIB68QuO3tiLznjAwpKTgNyDbMegwisCHZUETUZV0ZqOquBpXj2rtnpSJT1AEnnRmL2OgGgAAIKkRQap2htgAAAAASUVORK5CYII=");
  background-repeat: no-repeat;
  background-position: 2px center;
}
.droplet-tooltip {
  font-family: Courier, monospace;
  margin-top: 15px;
  margin-left: 15px;
  display: none;
  background-color: #FFF;
  background-image: -webkit-linear-gradient(top, transparent, rgba(0, 0, 0, 0.1));
  background-image: linear-gradient(to bottom, transparent, rgba(0, 0, 0, 0.1));
  border: 1px solid gray;
  border-radius: 1px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  color: black;
  max-width: 100%;
  padding: 3px 4px;
  position: fixed;
  z-index: 999999;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  cursor: default;
  white-space: pre;
  word-wrap: break-word;
  line-height: normal;
  font-style: normal;
  font-weight: normal;
  font-size: 15px;
  letter-spacing: normal;
  pointer-events: none;
}
