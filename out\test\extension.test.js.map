{"version": 3, "file": "extension.test.js", "sourceRoot": "", "sources": ["../../src/test/extension.test.ts"], "names": [], "mappings": ";;AAAA,iCAAiC;AACjC,iCAAiC;AAEjC,KAAK,CAAC,sBAAsB,EAAE,GAAG,EAAE;IAC/B,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,CAAC;IAEzD,IAAI,CAAC,6BAA6B,EAAE,GAAG,EAAE;QACrC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,+BAA+B,CAAC,CAAC,CAAC;IAC/E,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,2BAA2B,EAAE,KAAK,IAAI,EAAE;QACzC,MAAM,SAAS,GAAG,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,+BAA+B,CAAC,CAAC;QAClF,IAAI,SAAS,EAAE;YACX,MAAM,SAAS,CAAC,QAAQ,EAAE,CAAC;YAC3B,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;SACjC;IACL,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;QAC7C,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAEzD,MAAM,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAC,CAAC;QACnD,MAAM,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC,CAAC;QAClD,MAAM,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAC,CAAC;IACvD,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,oCAAoC,EAAE,GAAG,EAAE;QAC5C,mEAAmE;QACnE,8EAA8E;QAC9E,uCAAuC;QACvC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,4DAA4D;IACjF,CAAC,CAAC,CAAC;AACP,CAAC,CAAC,CAAC"}