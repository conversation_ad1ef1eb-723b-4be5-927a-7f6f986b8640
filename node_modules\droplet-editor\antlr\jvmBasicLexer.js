// Generated from jvmBasic.g4 by ANTLR 4.5
// jshint ignore: start
var antlr4 = require('antlr4/index');


var serializedATN = ["\3\u0430\ud6d1\u8206\uad2d\u4417\uaef1\u8d80\uaadd",
    "\2}\u0343\b\1\4\2\t\2\4\3\t\3\4\4\t\4\4\5\t\5\4\6\t\6\4\7\t\7\4\b\t",
    "\b\4\t\t\t\4\n\t\n\4\13\t\13\4\f\t\f\4\r\t\r\4\16\t\16\4\17\t\17\4\20",
    "\t\20\4\21\t\21\4\22\t\22\4\23\t\23\4\24\t\24\4\25\t\25\4\26\t\26\4",
    "\27\t\27\4\30\t\30\4\31\t\31\4\32\t\32\4\33\t\33\4\34\t\34\4\35\t\35",
    "\4\36\t\36\4\37\t\37\4 \t \4!\t!\4\"\t\"\4#\t#\4$\t$\4%\t%\4&\t&\4\'",
    "\t\'\4(\t(\4)\t)\4*\t*\4+\t+\4,\t,\4-\t-\4.\t.\4/\t/\4\60\t\60\4\61",
    "\t\61\4\62\t\62\4\63\t\63\4\64\t\64\4\65\t\65\4\66\t\66\4\67\t\67\4",
    "8\t8\49\t9\4:\t:\4;\t;\4<\t<\4=\t=\4>\t>\4?\t?\4@\t@\4A\tA\4B\tB\4C",
    "\tC\4D\tD\4E\tE\4F\tF\4G\tG\4H\tH\4I\tI\4J\tJ\4K\tK\4L\tL\4M\tM\4N\t",
    "N\4O\tO\4P\tP\4Q\tQ\4R\tR\4S\tS\4T\tT\4U\tU\4V\tV\4W\tW\4X\tX\4Y\tY",
    "\4Z\tZ\4[\t[\4\\\t\\\4]\t]\4^\t^\4_\t_\4`\t`\4a\ta\4b\tb\4c\tc\4d\t",
    "d\4e\te\4f\tf\4g\tg\4h\th\4i\ti\4j\tj\4k\tk\4l\tl\4m\tm\4n\tn\4o\to",
    "\4p\tp\4q\tq\4r\tr\4s\ts\4t\tt\4u\tu\4v\tv\4w\tw\4x\tx\4y\ty\4z\tz\4",
    "{\t{\4|\t|\3\2\3\2\3\3\3\3\3\4\3\4\3\4\3\4\3\4\3\4\3\4\3\5\3\5\3\5\3",
    "\5\3\5\3\5\3\6\3\6\3\6\3\6\3\6\3\7\3\7\3\7\3\7\3\7\3\7\3\b\3\b\3\b\3",
    "\t\3\t\3\t\3\t\3\t\3\n\3\n\3\n\3\n\3\n\3\13\3\13\3\13\3\13\3\f\3\f\3",
    "\f\3\f\3\f\3\r\3\r\3\r\3\r\3\r\3\16\3\16\3\16\3\16\3\16\3\16\3\17\3",
    "\17\3\17\3\17\3\17\3\17\3\17\3\20\3\20\3\20\3\20\3\20\3\21\3\21\3\22",
    "\3\22\3\23\3\23\3\24\3\24\3\25\3\25\3\26\3\26\3\27\3\27\3\27\3\27\3",
    "\27\3\27\3\30\3\30\3\30\3\30\3\31\3\31\3\31\3\31\3\32\3\32\3\33\3\33",
    "\3\34\3\34\3\34\3\34\3\35\3\35\3\36\3\36\3\36\3\36\3\36\3\37\3\37\3",
    "\37\3\37\3 \3 \3 \3 \3!\3!\3!\3!\3\"\3\"\3#\3#\3#\3#\3$\3$\3$\3%\3%",
    "\3%\3%\3%\3&\3&\3&\3&\3&\3&\3\'\3\'\3(\3(\3(\3(\3)\3)\3)\3)\3*\3*\3",
    "+\3+\3+\3+\3+\3,\3,\3,\3,\3-\3-\3-\3-\3-\3.\3.\3.\3.\3/\3/\3/\3/\3/",
    "\3\60\3\60\3\60\3\60\3\61\3\61\3\61\3\61\3\61\3\61\3\62\3\62\3\62\3",
    "\62\3\62\3\62\3\63\3\63\3\63\3\63\3\64\3\64\3\64\3\64\3\65\3\65\3\65",
    "\3\65\3\65\3\66\3\66\3\66\3\66\3\66\3\67\3\67\3\67\3\67\3\67\38\38\3",
    "8\39\39\39\39\3:\3:\3:\3:\3:\3;\3;\3;\3;\3;\3<\3<\3<\3<\3<\3=\3=\3=",
    "\3=\3>\3>\3>\3>\3>\3?\3?\3?\3?\3?\3?\3@\3@\3@\3@\3@\3@\3A\3A\3A\3A\3",
    "A\3A\3B\3B\3B\3B\3B\3B\3B\3B\3C\3C\3C\3C\3C\3C\3C\3D\3D\3D\3D\3D\3D",
    "\3E\3E\3E\3E\3F\3F\3F\3F\3G\3G\3G\3G\3H\3H\3H\3H\3I\3I\3I\3I\3I\3I\3",
    "J\3J\3J\3J\3J\3J\3J\3J\3K\3K\3K\3K\3L\3L\3L\3M\3M\3M\3M\3M\3N\3N\3N",
    "\3N\3N\3O\3O\3O\3O\3O\3P\3P\3P\3P\3P\3P\3Q\3Q\3Q\3Q\3Q\3R\3R\3R\3S\3",
    "S\3S\3S\3T\3T\3T\3U\3U\3U\3U\3V\3V\3V\3V\3W\3W\3W\3W\3W\3W\3X\3X\3X",
    "\3X\3Y\3Y\3Y\3Y\3Y\3Y\3Z\3Z\3Z\3Z\3Z\3Z\3[\3[\3[\3[\3[\3[\3[\3\\\3\\",
    "\3\\\3\\\3\\\3]\3]\3]\3]\3]\3^\3^\3^\3^\3^\3_\3_\3_\3_\3`\3`\3`\3`\3",
    "`\3`\3`\3a\3a\3a\3a\3b\3b\3b\3b\3c\3c\3c\3c\3d\3d\3d\3d\3e\3e\3e\3e",
    "\3f\3f\3f\3f\3g\3g\3g\3g\3h\3h\3h\3h\3i\3i\3i\3i\3j\3j\3j\3j\3j\3j\3",
    "k\3k\3k\3k\3k\3k\3k\3l\3l\3l\3l\3m\3m\3n\3n\3o\3o\3o\3p\3p\3p\3p\3q",
    "\3q\3q\3q\3q\3q\3q\3q\3r\3r\3r\3r\3r\3s\3s\3s\3s\3s\3t\3t\3u\3u\3u\3",
    "u\3u\3u\3u\3u\3v\3v\7v\u0302\nv\fv\16v\u0305\13v\3w\3w\7w\u0309\nw\f",
    "w\16w\u030c\13w\3w\3w\3x\6x\u0311\nx\rx\16x\u0312\3y\6y\u0316\ny\ry",
    "\16y\u0317\3y\3y\7y\u031c\ny\fy\16y\u031f\13y\3z\7z\u0322\nz\fz\16z",
    "\u0325\13z\3z\3z\6z\u0329\nz\rz\16z\u032a\3z\3z\6z\u032f\nz\rz\16z\u0330",
    "\7z\u0333\nz\fz\16z\u0336\13z\3{\6{\u0339\n{\r{\16{\u033a\3|\6|\u033e",
    "\n|\r|\16|\u033f\3|\3|\2\2}\3\3\5\4\7\5\t\6\13\7\r\b\17\t\21\n\23\13",
    "\25\f\27\r\31\16\33\17\35\20\37\21!\22#\23%\24\'\25)\26+\27-\30/\31",
    "\61\32\63\33\65\34\67\359\36;\37= ?!A\"C#E$G%I&K\'M(O)Q*S+U,W-Y.[/]",
    "\60_\61a\62c\63e\64g\65i\66k\67m8o9q:s;u<w=y>{?}@\177A\u0081B\u0083",
    "C\u0085D\u0087E\u0089F\u008bG\u008dH\u008fI\u0091J\u0093K\u0095L\u0097",
    "M\u0099N\u009bO\u009dP\u009fQ\u00a1R\u00a3S\u00a5T\u00a7U\u00a9V\u00ab",
    "W\u00adX\u00afY\u00b1Z\u00b3[\u00b5\\\u00b7]\u00b9^\u00bb_\u00bd`\u00bf",
    "a\u00c1b\u00c3c\u00c5d\u00c7e\u00c9f\u00cbg\u00cdh\u00cfi\u00d1j\u00d3",
    "k\u00d5l\u00d7m\u00d9n\u00dbo\u00ddp\u00dfq\u00e1r\u00e3s\u00e5t\u00e7",
    "u\u00e9v\u00ebw\u00edx\u00efy\u00f1z\u00f3{\u00f5|\u00f7}\3\2\7\4\2",
    "\f\f\17\17\5\2\f\f\17\17$$\4\2C\\c|\4\2GGgg\4\2\13\13\"\"\u034d\2\3",
    "\3\2\2\2\2\5\3\2\2\2\2\7\3\2\2\2\2\t\3\2\2\2\2\13\3\2\2\2\2\r\3\2\2",
    "\2\2\17\3\2\2\2\2\21\3\2\2\2\2\23\3\2\2\2\2\25\3\2\2\2\2\27\3\2\2\2",
    "\2\31\3\2\2\2\2\33\3\2\2\2\2\35\3\2\2\2\2\37\3\2\2\2\2!\3\2\2\2\2#\3",
    "\2\2\2\2%\3\2\2\2\2\'\3\2\2\2\2)\3\2\2\2\2+\3\2\2\2\2-\3\2\2\2\2/\3",
    "\2\2\2\2\61\3\2\2\2\2\63\3\2\2\2\2\65\3\2\2\2\2\67\3\2\2\2\29\3\2\2",
    "\2\2;\3\2\2\2\2=\3\2\2\2\2?\3\2\2\2\2A\3\2\2\2\2C\3\2\2\2\2E\3\2\2\2",
    "\2G\3\2\2\2\2I\3\2\2\2\2K\3\2\2\2\2M\3\2\2\2\2O\3\2\2\2\2Q\3\2\2\2\2",
    "S\3\2\2\2\2U\3\2\2\2\2W\3\2\2\2\2Y\3\2\2\2\2[\3\2\2\2\2]\3\2\2\2\2_",
    "\3\2\2\2\2a\3\2\2\2\2c\3\2\2\2\2e\3\2\2\2\2g\3\2\2\2\2i\3\2\2\2\2k\3",
    "\2\2\2\2m\3\2\2\2\2o\3\2\2\2\2q\3\2\2\2\2s\3\2\2\2\2u\3\2\2\2\2w\3\2",
    "\2\2\2y\3\2\2\2\2{\3\2\2\2\2}\3\2\2\2\2\177\3\2\2\2\2\u0081\3\2\2\2",
    "\2\u0083\3\2\2\2\2\u0085\3\2\2\2\2\u0087\3\2\2\2\2\u0089\3\2\2\2\2\u008b",
    "\3\2\2\2\2\u008d\3\2\2\2\2\u008f\3\2\2\2\2\u0091\3\2\2\2\2\u0093\3\2",
    "\2\2\2\u0095\3\2\2\2\2\u0097\3\2\2\2\2\u0099\3\2\2\2\2\u009b\3\2\2\2",
    "\2\u009d\3\2\2\2\2\u009f\3\2\2\2\2\u00a1\3\2\2\2\2\u00a3\3\2\2\2\2\u00a5",
    "\3\2\2\2\2\u00a7\3\2\2\2\2\u00a9\3\2\2\2\2\u00ab\3\2\2\2\2\u00ad\3\2",
    "\2\2\2\u00af\3\2\2\2\2\u00b1\3\2\2\2\2\u00b3\3\2\2\2\2\u00b5\3\2\2\2",
    "\2\u00b7\3\2\2\2\2\u00b9\3\2\2\2\2\u00bb\3\2\2\2\2\u00bd\3\2\2\2\2\u00bf",
    "\3\2\2\2\2\u00c1\3\2\2\2\2\u00c3\3\2\2\2\2\u00c5\3\2\2\2\2\u00c7\3\2",
    "\2\2\2\u00c9\3\2\2\2\2\u00cb\3\2\2\2\2\u00cd\3\2\2\2\2\u00cf\3\2\2\2",
    "\2\u00d1\3\2\2\2\2\u00d3\3\2\2\2\2\u00d5\3\2\2\2\2\u00d7\3\2\2\2\2\u00d9",
    "\3\2\2\2\2\u00db\3\2\2\2\2\u00dd\3\2\2\2\2\u00df\3\2\2\2\2\u00e1\3\2",
    "\2\2\2\u00e3\3\2\2\2\2\u00e5\3\2\2\2\2\u00e7\3\2\2\2\2\u00e9\3\2\2\2",
    "\2\u00eb\3\2\2\2\2\u00ed\3\2\2\2\2\u00ef\3\2\2\2\2\u00f1\3\2\2\2\2\u00f3",
    "\3\2\2\2\2\u00f5\3\2\2\2\2\u00f7\3\2\2\2\3\u00f9\3\2\2\2\5\u00fb\3\2",
    "\2\2\7\u00fd\3\2\2\2\t\u0104\3\2\2\2\13\u010a\3\2\2\2\r\u010f\3\2\2",
    "\2\17\u0115\3\2\2\2\21\u0118\3\2\2\2\23\u011d\3\2\2\2\25\u0122\3\2\2",
    "\2\27\u0126\3\2\2\2\31\u012b\3\2\2\2\33\u0130\3\2\2\2\35\u0136\3\2\2",
    "\2\37\u013d\3\2\2\2!\u0142\3\2\2\2#\u0144\3\2\2\2%\u0146\3\2\2\2\'\u0148",
    "\3\2\2\2)\u014a\3\2\2\2+\u014c\3\2\2\2-\u014e\3\2\2\2/\u0154\3\2\2\2",
    "\61\u0158\3\2\2\2\63\u015c\3\2\2\2\65\u015e\3\2\2\2\67\u0160\3\2\2\2",
    "9\u0164\3\2\2\2;\u0166\3\2\2\2=\u016b\3\2\2\2?\u016f\3\2\2\2A\u0173",
    "\3\2\2\2C\u0177\3\2\2\2E\u0179\3\2\2\2G\u017d\3\2\2\2I\u0180\3\2\2\2",
    "K\u0185\3\2\2\2M\u018b\3\2\2\2O\u018d\3\2\2\2Q\u0191\3\2\2\2S\u0195",
    "\3\2\2\2U\u0197\3\2\2\2W\u019c\3\2\2\2Y\u01a0\3\2\2\2[\u01a5\3\2\2\2",
    "]\u01a9\3\2\2\2_\u01ae\3\2\2\2a\u01b2\3\2\2\2c\u01b8\3\2\2\2e\u01be",
    "\3\2\2\2g\u01c2\3\2\2\2i\u01c6\3\2\2\2k\u01cb\3\2\2\2m\u01d0\3\2\2\2",
    "o\u01d5\3\2\2\2q\u01d8\3\2\2\2s\u01dc\3\2\2\2u\u01e1\3\2\2\2w\u01e6",
    "\3\2\2\2y\u01eb\3\2\2\2{\u01ef\3\2\2\2}\u01f4\3\2\2\2\177\u01fa\3\2",
    "\2\2\u0081\u0200\3\2\2\2\u0083\u0206\3\2\2\2\u0085\u020e\3\2\2\2\u0087",
    "\u0215\3\2\2\2\u0089\u021b\3\2\2\2\u008b\u021f\3\2\2\2\u008d\u0223\3",
    "\2\2\2\u008f\u0227\3\2\2\2\u0091\u022b\3\2\2\2\u0093\u0231\3\2\2\2\u0095",
    "\u0239\3\2\2\2\u0097\u023d\3\2\2\2\u0099\u0240\3\2\2\2\u009b\u0245\3",
    "\2\2\2\u009d\u024a\3\2\2\2\u009f\u024f\3\2\2\2\u00a1\u0255\3\2\2\2\u00a3",
    "\u025a\3\2\2\2\u00a5\u025d\3\2\2\2\u00a7\u0261\3\2\2\2\u00a9\u0264\3",
    "\2\2\2\u00ab\u0268\3\2\2\2\u00ad\u026c\3\2\2\2\u00af\u0272\3\2\2\2\u00b1",
    "\u0276\3\2\2\2\u00b3\u027c\3\2\2\2\u00b5\u0282\3\2\2\2\u00b7\u0289\3",
    "\2\2\2\u00b9\u028e\3\2\2\2\u00bb\u0293\3\2\2\2\u00bd\u0298\3\2\2\2\u00bf",
    "\u029c\3\2\2\2\u00c1\u02a3\3\2\2\2\u00c3\u02a7\3\2\2\2\u00c5\u02ab\3",
    "\2\2\2\u00c7\u02af\3\2\2\2\u00c9\u02b3\3\2\2\2\u00cb\u02b7\3\2\2\2\u00cd",
    "\u02bb\3\2\2\2\u00cf\u02bf\3\2\2\2\u00d1\u02c3\3\2\2\2\u00d3\u02c7\3",
    "\2\2\2\u00d5\u02cd\3\2\2\2\u00d7\u02d4\3\2\2\2\u00d9\u02d8\3\2\2\2\u00db",
    "\u02da\3\2\2\2\u00dd\u02dc\3\2\2\2\u00df\u02df\3\2\2\2\u00e1\u02e3\3",
    "\2\2\2\u00e3\u02eb\3\2\2\2\u00e5\u02f0\3\2\2\2\u00e7\u02f5\3\2\2\2\u00e9",
    "\u02f7\3\2\2\2\u00eb\u02ff\3\2\2\2\u00ed\u0306\3\2\2\2\u00ef\u0310\3",
    "\2\2\2\u00f1\u0315\3\2\2\2\u00f3\u0323\3\2\2\2\u00f5\u0338\3\2\2\2\u00f7",
    "\u033d\3\2\2\2\u00f9\u00fa\7&\2\2\u00fa\4\3\2\2\2\u00fb\u00fc\7\'\2",
    "\2\u00fc\6\3\2\2\2\u00fd\u00fe\7T\2\2\u00fe\u00ff\7G\2\2\u00ff\u0100",
    "\7V\2\2\u0100\u0101\7W\2\2\u0101\u0102\7T\2\2\u0102\u0103\7P\2\2\u0103",
    "\b\3\2\2\2\u0104\u0105\7R\2\2\u0105\u0106\7T\2\2\u0106\u0107\7K\2\2",
    "\u0107\u0108\7P\2\2\u0108\u0109\7V\2\2\u0109\n\3\2\2\2\u010a\u010b\7",
    "I\2\2\u010b\u010c\7Q\2\2\u010c\u010d\7V\2\2\u010d\u010e\7Q\2\2\u010e",
    "\f\3\2\2\2\u010f\u0110\7I\2\2\u0110\u0111\7Q\2\2\u0111\u0112\7U\2\2",
    "\u0112\u0113\7W\2\2\u0113\u0114\7D\2\2\u0114\16\3\2\2\2\u0115\u0116",
    "\7K\2\2\u0116\u0117\7H\2\2\u0117\20\3\2\2\2\u0118\u0119\7P\2\2\u0119",
    "\u011a\7G\2\2\u011a\u011b\7Z\2\2\u011b\u011c\7V\2\2\u011c\22\3\2\2\2",
    "\u011d\u011e\7V\2\2\u011e\u011f\7J\2\2\u011f\u0120\7G\2\2\u0120\u0121",
    "\7P\2\2\u0121\24\3\2\2\2\u0122\u0123\7T\2\2\u0123\u0124\7G\2\2\u0124",
    "\u0125\7O\2\2\u0125\26\3\2\2\2\u0126\u0127\7E\2\2\u0127\u0128\7J\2\2",
    "\u0128\u0129\7T\2\2\u0129\u012a\7&\2\2\u012a\30\3\2\2\2\u012b\u012c",
    "\7O\2\2\u012c\u012d\7K\2\2\u012d\u012e\7F\2\2\u012e\u012f\7&\2\2\u012f",
    "\32\3\2\2\2\u0130\u0131\7N\2\2\u0131\u0132\7G\2\2\u0132\u0133\7H\2\2",
    "\u0133\u0134\7V\2\2\u0134\u0135\7&\2\2\u0135\34\3\2\2\2\u0136\u0137",
    "\7T\2\2\u0137\u0138\7K\2\2\u0138\u0139\7I\2\2\u0139\u013a\7J\2\2\u013a",
    "\u013b\7V\2\2\u013b\u013c\7&\2\2\u013c\36\3\2\2\2\u013d\u013e\7U\2\2",
    "\u013e\u013f\7V\2\2\u013f\u0140\7T\2\2\u0140\u0141\7&\2\2\u0141 \3\2",
    "\2\2\u0142\u0143\7*\2\2\u0143\"\3\2\2\2\u0144\u0145\7+\2\2\u0145$\3",
    "\2\2\2\u0146\u0147\7-\2\2\u0147&\3\2\2\2\u0148\u0149\7/\2\2\u0149(\3",
    "\2\2\2\u014a\u014b\7,\2\2\u014b*\3\2\2\2\u014c\u014d\7\61\2\2\u014d",
    ",\3\2\2\2\u014e\u014f\7E\2\2\u014f\u0150\7N\2\2\u0150\u0151\7G\2\2\u0151",
    "\u0152\7C\2\2\u0152\u0153\7T\2\2\u0153.\3\2\2\2\u0154\u0155\7@\2\2\u0155",
    "\u0156\7<\2\2\u0156\u0157\7\"\2\2\u0157\60\3\2\2\2\u0158\u0159\7>\2",
    "\2\u0159\u015a\7<\2\2\u015a\u015b\7\"\2\2\u015b\62\3\2\2\2\u015c\u015d",
    "\7@\2\2\u015d\64\3\2\2\2\u015e\u015f\7>\2\2\u015f\66\3\2\2\2\u0160\u0161",
    "\7>\2\2\u0161\u0162\7\"\2\2\u0162\u0163\7@\2\2\u01638\3\2\2\2\u0164",
    "\u0165\7.\2\2\u0165:\3\2\2\2\u0166\u0167\7N\2\2\u0167\u0168\7K\2\2\u0168",
    "\u0169\7U\2\2\u0169\u016a\7V\2\2\u016a<\3\2\2\2\u016b\u016c\7T\2\2\u016c",
    "\u016d\7W\2\2\u016d\u016e\7P\2\2\u016e>\3\2\2\2\u016f\u0170\7G\2\2\u0170",
    "\u0171\7P\2\2\u0171\u0172\7F\2\2\u0172@\3\2\2\2\u0173\u0174\7N\2\2\u0174",
    "\u0175\7G\2\2\u0175\u0176\7V\2\2\u0176B\3\2\2\2\u0177\u0178\7?\2\2\u0178",
    "D\3\2\2\2\u0179\u017a\7H\2\2\u017a\u017b\7Q\2\2\u017b\u017c\7T\2\2\u017c",
    "F\3\2\2\2\u017d\u017e\7V\2\2\u017e\u017f\7Q\2\2\u017fH\3\2\2\2\u0180",
    "\u0181\7U\2\2\u0181\u0182\7V\2\2\u0182\u0183\7G\2\2\u0183\u0184\7R\2",
    "\2\u0184J\3\2\2\2\u0185\u0186\7K\2\2\u0186\u0187\7P\2\2\u0187\u0188",
    "\7R\2\2\u0188\u0189\7W\2\2\u0189\u018a\7V\2\2\u018aL\3\2\2\2\u018b\u018c",
    "\7=\2\2\u018cN\3\2\2\2\u018d\u018e\7F\2\2\u018e\u018f\7K\2\2\u018f\u0190",
    "\7O\2\2\u0190P\3\2\2\2\u0191\u0192\7U\2\2\u0192\u0193\7S\2\2\u0193\u0194",
    "\7T\2\2\u0194R\3\2\2\2\u0195\u0196\7<\2\2\u0196T\3\2\2\2\u0197\u0198",
    "\7V\2\2\u0198\u0199\7G\2\2\u0199\u019a\7Z\2\2\u019a\u019b\7V\2\2\u019b",
    "V\3\2\2\2\u019c\u019d\7J\2\2\u019d\u019e\7I\2\2\u019e\u019f\7T\2\2\u019f",
    "X\3\2\2\2\u01a0\u01a1\7J\2\2\u01a1\u01a2\7I\2\2\u01a2\u01a3\7T\2\2\u01a3",
    "\u01a4\7\64\2\2\u01a4Z\3\2\2\2\u01a5\u01a6\7N\2\2\u01a6\u01a7\7G\2\2",
    "\u01a7\u01a8\7P\2\2\u01a8\\\3\2\2\2\u01a9\u01aa\7E\2\2\u01aa\u01ab\7",
    "C\2\2\u01ab\u01ac\7N\2\2\u01ac\u01ad\7N\2\2\u01ad^\3\2\2\2\u01ae\u01af",
    "\7C\2\2\u01af\u01b0\7U\2\2\u01b0\u01b1\7E\2\2\u01b1`\3\2\2\2\u01b2\u01b3",
    "\7J\2\2\u01b3\u01b4\7R\2\2\u01b4\u01b5\7N\2\2\u01b5\u01b6\7Q\2\2\u01b6",
    "\u01b7\7V\2\2\u01b7b\3\2\2\2\u01b8\u01b9\7X\2\2\u01b9\u01ba\7R\2\2\u01ba",
    "\u01bb\7N\2\2\u01bb\u01bc\7Q\2\2\u01bc\u01bd\7V\2\2\u01bdd\3\2\2\2\u01be",
    "\u01bf\7R\2\2\u01bf\u01c0\7T\2\2\u01c0\u01c1\7%\2\2\u01c1f\3\2\2\2\u01c2",
    "\u01c3\7K\2\2\u01c3\u01c4\7P\2\2\u01c4\u01c5\7%\2\2\u01c5h\3\2\2\2\u01c6",
    "\u01c7\7X\2\2\u01c7\u01c8\7V\2\2\u01c8\u01c9\7C\2\2\u01c9\u01ca\7D\2",
    "\2\u01caj\3\2\2\2\u01cb\u01cc\7J\2\2\u01cc\u01cd\7V\2\2\u01cd\u01ce",
    "\7C\2\2\u01ce\u01cf\7D\2\2\u01cfl\3\2\2\2\u01d0\u01d1\7J\2\2\u01d1\u01d2",
    "\7Q\2\2\u01d2\u01d3\7O\2\2\u01d3\u01d4\7G\2\2\u01d4n\3\2\2\2\u01d5\u01d6",
    "\7Q\2\2\u01d6\u01d7\7P\2\2\u01d7p\3\2\2\2\u01d8\u01d9\7R\2\2\u01d9\u01da",
    "\7F\2\2\u01da\u01db\7N\2\2\u01dbr\3\2\2\2\u01dc\u01dd\7R\2\2\u01dd\u01de",
    "\7N\2\2\u01de\u01df\7Q\2\2\u01df\u01e0\7V\2\2\u01e0t\3\2\2\2\u01e1\u01e2",
    "\7R\2\2\u01e2\u01e3\7G\2\2\u01e3\u01e4\7G\2\2\u01e4\u01e5\7M\2\2\u01e5",
    "v\3\2\2\2\u01e6\u01e7\7R\2\2\u01e7\u01e8\7Q\2\2\u01e8\u01e9\7M\2\2\u01e9",
    "\u01ea\7G\2\2\u01eax\3\2\2\2\u01eb\u01ec\7K\2\2\u01ec\u01ed\7P\2\2\u01ed",
    "\u01ee\7V\2\2\u01eez\3\2\2\2\u01ef\u01f0\7U\2\2\u01f0\u01f1\7V\2\2\u01f1",
    "\u01f2\7Q\2\2\u01f2\u01f3\7R\2\2\u01f3|\3\2\2\2\u01f4\u01f5\7J\2\2\u01f5",
    "\u01f6\7K\2\2\u01f6\u01f7\7O\2\2\u01f7\u01f8\7G\2\2\u01f8\u01f9\7O\2",
    "\2\u01f9~\3\2\2\2\u01fa\u01fb\7N\2\2\u01fb\u01fc\7Q\2\2\u01fc\u01fd",
    "\7O\2\2\u01fd\u01fe\7G\2\2\u01fe\u01ff\7O\2\2\u01ff\u0080\3\2\2\2\u0200",
    "\u0201\7H\2\2\u0201\u0202\7N\2\2\u0202\u0203\7C\2\2\u0203\u0204\7U\2",
    "\2\u0204\u0205\7J\2\2\u0205\u0082\3\2\2\2\u0206\u0207\7K\2\2\u0207\u0208",
    "\7P\2\2\u0208\u0209\7X\2\2\u0209\u020a\7G\2\2\u020a\u020b\7T\2\2\u020b",
    "\u020c\7U\2\2\u020c\u020d\7G\2\2\u020d\u0084\3\2\2\2\u020e\u020f\7P",
    "\2\2\u020f\u0210\7Q\2\2\u0210\u0211\7T\2\2\u0211\u0212\7O\2\2\u0212",
    "\u0213\7C\2\2\u0213\u0214\7N\2\2\u0214\u0086\3\2\2\2\u0215\u0216\7Q",
    "\2\2\u0216\u0217\7P\2\2\u0217\u0218\7G\2\2\u0218\u0219\7T\2\2\u0219",
    "\u021a\7T\2\2\u021a\u0088\3\2\2\2\u021b\u021c\7U\2\2\u021c\u021d\7R",
    "\2\2\u021d\u021e\7E\2\2\u021e\u008a\3\2\2\2\u021f\u0220\7H\2\2\u0220",
    "\u0221\7T\2\2\u0221\u0222\7G\2\2\u0222\u008c\3\2\2\2\u0223\u0224\7R",
    "\2\2\u0224\u0225\7Q\2\2\u0225\u0226\7U\2\2\u0226\u008e\3\2\2\2\u0227",
    "\u0228\7W\2\2\u0228\u0229\7U\2\2\u0229\u022a\7T\2\2\u022a\u0090\3\2",
    "\2\2\u022b\u022c\7V\2\2\u022c\u022d\7T\2\2\u022d\u022e\7C\2\2\u022e",
    "\u022f\7E\2\2\u022f\u0230\7G\2\2\u0230\u0092\3\2\2\2\u0231\u0232\7P",
    "\2\2\u0232\u0233\7Q\2\2\u0233\u0234\7V\2\2\u0234\u0235\7T\2\2\u0235",
    "\u0236\7C\2\2\u0236\u0237\7E\2\2\u0237\u0238\7G\2\2\u0238\u0094\3\2",
    "\2\2\u0239\u023a\7C\2\2\u023a\u023b\7P\2\2\u023b\u023c\7F\2\2\u023c",
    "\u0096\3\2\2\2\u023d\u023e\7Q\2\2\u023e\u023f\7T\2\2\u023f\u0098\3\2",
    "\2\2\u0240\u0241\7F\2\2\u0241\u0242\7C\2\2\u0242\u0243\7V\2\2\u0243",
    "\u0244\7C\2\2\u0244\u009a\3\2\2\2\u0245\u0246\7Y\2\2\u0246\u0247\7C",
    "\2\2\u0247\u0248\7K\2\2\u0248\u0249\7V\2\2\u0249\u009c\3\2\2\2\u024a",
    "\u024b\7T\2\2\u024b\u024c\7G\2\2\u024c\u024d\7C\2\2\u024d\u024e\7F\2",
    "\2\u024e\u009e\3\2\2\2\u024f\u0250\7Z\2\2\u0250\u0251\7F\2\2\u0251\u0252",
    "\7T\2\2\u0252\u0253\7C\2\2\u0253\u0254\7Y\2\2\u0254\u00a0\3\2\2\2\u0255",
    "\u0256\7F\2\2\u0256\u0257\7T\2\2\u0257\u0258\7C\2\2\u0258\u0259\7Y\2",
    "\2\u0259\u00a2\3\2\2\2\u025a\u025b\7C\2\2\u025b\u025c\7V\2\2\u025c\u00a4",
    "\3\2\2\2\u025d\u025e\7F\2\2\u025e\u025f\7G\2\2\u025f\u0260\7H\2\2\u0260",
    "\u00a6\3\2\2\2\u0261\u0262\7H\2\2\u0262\u0263\7P\2\2\u0263\u00a8\3\2",
    "\2\2\u0264\u0265\7X\2\2\u0265\u0266\7C\2\2\u0266\u0267\7N\2\2\u0267",
    "\u00aa\3\2\2\2\u0268\u0269\7V\2\2\u0269\u026a\7C\2\2\u026a\u026b\7D",
    "\2\2\u026b\u00ac\3\2\2\2\u026c\u026d\7U\2\2\u026d\u026e\7R\2\2\u026e",
    "\u026f\7G\2\2\u026f\u0270\7G\2\2\u0270\u0271\7F\2\2\u0271\u00ae\3\2",
    "\2\2\u0272\u0273\7T\2\2\u0273\u0274\7Q\2\2\u0274\u0275\7V\2\2\u0275",
    "\u00b0\3\2\2\2\u0276\u0277\7U\2\2\u0277\u0278\7E\2\2\u0278\u0279\7C",
    "\2\2\u0279\u027a\7N\2\2\u027a\u027b\7G\2\2\u027b\u00b2\3\2\2\2\u027c",
    "\u027d\7E\2\2\u027d\u027e\7Q\2\2\u027e\u027f\7N\2\2\u027f\u0280\7Q\2",
    "\2\u0280\u0281\7T\2\2\u0281\u00b4\3\2\2\2\u0282\u0283\7J\2\2\u0283\u0284",
    "\7E\2\2\u0284\u0285\7Q\2\2\u0285\u0286\7N\2\2\u0286\u0287\7Q\2\2\u0287",
    "\u0288\7T\2\2\u0288\u00b6\3\2\2\2\u0289\u028a\7J\2\2\u028a\u028b\7N",
    "\2\2\u028b\u028c\7K\2\2\u028c\u028d\7P\2\2\u028d\u00b8\3\2\2\2\u028e",
    "\u028f\7X\2\2\u028f\u0290\7N\2\2\u0290\u0291\7K\2\2\u0291\u0292\7P\2",
    "\2\u0292\u00ba\3\2\2\2\u0293\u0294\7U\2\2\u0294\u0295\7E\2\2\u0295\u0296",
    "\7T\2\2\u0296\u0297\7P\2\2\u0297\u00bc\3\2\2\2\u0298\u0299\7R\2\2\u0299",
    "\u029a\7Q\2\2\u029a\u029b\7R\2\2\u029b\u00be\3\2\2\2\u029c\u029d\7U",
    "\2\2\u029d\u029e\7J\2\2\u029e\u029f\7N\2\2\u029f\u02a0\7Q\2\2\u02a0",
    "\u02a1\7C\2\2\u02a1\u02a2\7F\2\2\u02a2\u00c0\3\2\2\2\u02a3\u02a4\7U",
    "\2\2\u02a4\u02a5\7K\2\2\u02a5\u02a6\7P\2\2\u02a6\u00c2\3\2\2\2\u02a7",
    "\u02a8\7E\2\2\u02a8\u02a9\7Q\2\2\u02a9\u02aa\7U\2\2\u02aa\u00c4\3\2",
    "\2\2\u02ab\u02ac\7V\2\2\u02ac\u02ad\7C\2\2\u02ad\u02ae\7P\2\2\u02ae",
    "\u00c6\3\2\2\2\u02af\u02b0\7C\2\2\u02b0\u02b1\7V\2\2\u02b1\u02b2\7P",
    "\2\2\u02b2\u00c8\3\2\2\2\u02b3\u02b4\7T\2\2\u02b4\u02b5\7P\2\2\u02b5",
    "\u02b6\7F\2\2\u02b6\u00ca\3\2\2\2\u02b7\u02b8\7U\2\2\u02b8\u02b9\7I",
    "\2\2\u02b9\u02ba\7P\2\2\u02ba\u00cc\3\2\2\2\u02bb\u02bc\7G\2\2\u02bc",
    "\u02bd\7Z\2\2\u02bd\u02be\7R\2\2\u02be\u00ce\3\2\2\2\u02bf\u02c0\7N",
    "\2\2\u02c0\u02c1\7Q\2\2\u02c1\u02c2\7I\2\2\u02c2\u00d0\3\2\2\2\u02c3",
    "\u02c4\7C\2\2\u02c4\u02c5\7D\2\2\u02c5\u02c6\7U\2\2\u02c6\u00d2\3\2",
    "\2\2\u02c7\u02c8\7U\2\2\u02c8\u02c9\7V\2\2\u02c9\u02ca\7Q\2\2\u02ca",
    "\u02cb\7T\2\2\u02cb\u02cc\7G\2\2\u02cc\u00d4\3\2\2\2\u02cd\u02ce\7T",
    "\2\2\u02ce\u02cf\7G\2\2\u02cf\u02d0\7E\2\2\u02d0\u02d1\7C\2\2\u02d1",
    "\u02d2\7N\2\2\u02d2\u02d3\7N\2\2\u02d3\u00d6\3\2\2\2\u02d4\u02d5\7I",
    "\2\2\u02d5\u02d6\7G\2\2\u02d6\u02d7\7V\2\2\u02d7\u00d8\3\2\2\2\u02d8",
    "\u02d9\7`\2\2\u02d9\u00da\3\2\2\2\u02da\u02db\7(\2\2\u02db\u00dc\3\2",
    "\2\2\u02dc\u02dd\7I\2\2\u02dd\u02de\7T\2\2\u02de\u00de\3\2\2\2\u02df",
    "\u02e0\7P\2\2\u02e0\u02e1\7Q\2\2\u02e1\u02e2\7V\2\2\u02e2\u00e0\3\2",
    "\2\2\u02e3\u02e4\7T\2\2\u02e4\u02e5\7G\2\2\u02e5\u02e6\7U\2\2\u02e6",
    "\u02e7\7V\2\2\u02e7\u02e8\7Q\2\2\u02e8\u02e9\7T\2\2\u02e9\u02ea\7G\2",
    "\2\u02ea\u00e2\3\2\2\2\u02eb\u02ec\7U\2\2\u02ec\u02ed\7C\2\2\u02ed\u02ee",
    "\7X\2\2\u02ee\u02ef\7G\2\2\u02ef\u00e4\3\2\2\2\u02f0\u02f1\7N\2\2\u02f1",
    "\u02f2\7Q\2\2\u02f2\u02f3\7C\2\2\u02f3\u02f4\7F\2\2\u02f4\u00e6\3\2",
    "\2\2\u02f5\u02f6\7A\2\2\u02f6\u00e8\3\2\2\2\u02f7\u02f8\7K\2\2\u02f8",
    "\u02f9\7P\2\2\u02f9\u02fa\7E\2\2\u02fa\u02fb\7N\2\2\u02fb\u02fc\7W\2",
    "\2\u02fc\u02fd\7F\2\2\u02fd\u02fe\7G\2\2\u02fe\u00ea\3\2\2\2\u02ff\u0303",
    "\5\25\13\2\u0300\u0302\n\2\2\2\u0301\u0300\3\2\2\2\u0302\u0305\3\2\2",
    "\2\u0303\u0301\3\2\2\2\u0303\u0304\3\2\2\2\u0304\u00ec\3\2\2\2\u0305",
    "\u0303\3\2\2\2\u0306\u030a\7$\2\2\u0307\u0309\n\3\2\2\u0308\u0307\3",
    "\2\2\2\u0309\u030c\3\2\2\2\u030a\u0308\3\2\2\2\u030a\u030b\3\2\2\2\u030b",
    "\u030d\3\2\2\2\u030c\u030a\3\2\2\2\u030d\u030e\7$\2\2\u030e\u00ee\3",
    "\2\2\2\u030f\u0311\t\4\2\2\u0310\u030f\3\2\2\2\u0311\u0312\3\2\2\2\u0312",
    "\u0310\3\2\2\2\u0312\u0313\3\2\2\2\u0313\u00f0\3\2\2\2\u0314\u0316\4",
    "\62;\2\u0315\u0314\3\2\2\2\u0316\u0317\3\2\2\2\u0317\u0315\3\2\2\2\u0317",
    "\u0318\3\2\2\2\u0318\u031d\3\2\2\2\u0319\u031a\t\5\2\2\u031a\u031c\5",
    "\u00f1y\2\u031b\u0319\3\2\2\2\u031c\u031f\3\2\2\2\u031d\u031b\3\2\2",
    "\2\u031d\u031e\3\2\2\2\u031e\u00f2\3\2\2\2\u031f\u031d\3\2\2\2\u0320",
    "\u0322\4\62;\2\u0321\u0320\3\2\2\2\u0322\u0325\3\2\2\2\u0323\u0321\3",
    "\2\2\2\u0323\u0324\3\2\2\2\u0324\u0326\3\2\2\2\u0325\u0323\3\2\2\2\u0326",
    "\u0328\7\60\2\2\u0327\u0329\4\62;\2\u0328\u0327\3\2\2\2\u0329\u032a",
    "\3\2\2\2\u032a\u0328\3\2\2\2\u032a\u032b\3\2\2\2\u032b\u0334\3\2\2\2",
    "\u032c\u032e\t\5\2\2\u032d\u032f\4\62;\2\u032e\u032d\3\2\2\2\u032f\u0330",
    "\3\2\2\2\u0330\u032e\3\2\2\2\u0330\u0331\3\2\2\2\u0331\u0333\3\2\2\2",
    "\u0332\u032c\3\2\2\2\u0333\u0336\3\2\2\2\u0334\u0332\3\2\2\2\u0334\u0335",
    "\3\2\2\2\u0335\u00f4\3\2\2\2\u0336\u0334\3\2\2\2\u0337\u0339\t\2\2\2",
    "\u0338\u0337\3\2\2\2\u0339\u033a\3\2\2\2\u033a\u0338\3\2\2\2\u033a\u033b",
    "\3\2\2\2\u033b\u00f6\3\2\2\2\u033c\u033e\t\6\2\2\u033d\u033c\3\2\2\2",
    "\u033e\u033f\3\2\2\2\u033f\u033d\3\2\2\2\u033f\u0340\3\2\2\2\u0340\u0341",
    "\3\2\2\2\u0341\u0342\b|\2\2\u0342\u00f8\3\2\2\2\16\2\u0303\u030a\u0312",
    "\u0317\u031d\u0323\u032a\u0330\u0334\u033a\u033f\3\2\3\2"].join("");


var atn = new antlr4.atn.ATNDeserializer().deserialize(serializedATN);

var decisionsToDFA = atn.decisionToState.map( function(ds, index) { return new antlr4.dfa.DFA(ds, index); });

function jvmBasicLexer(input) {
	antlr4.Lexer.call(this, input);
    this._interp = new antlr4.atn.LexerATNSimulator(this, atn, decisionsToDFA, new antlr4.PredictionContextCache());
    return this;
}

jvmBasicLexer.prototype = Object.create(antlr4.Lexer.prototype);
jvmBasicLexer.prototype.constructor = jvmBasicLexer;

jvmBasicLexer.EOF = antlr4.Token.EOF;
jvmBasicLexer.DOLLAR = 1;
jvmBasicLexer.PERCENT = 2;
jvmBasicLexer.RETURN = 3;
jvmBasicLexer.PRINT = 4;
jvmBasicLexer.GOTO = 5;
jvmBasicLexer.GOSUB = 6;
jvmBasicLexer.IF = 7;
jvmBasicLexer.NEXT = 8;
jvmBasicLexer.THEN = 9;
jvmBasicLexer.REM = 10;
jvmBasicLexer.CHR = 11;
jvmBasicLexer.MID = 12;
jvmBasicLexer.LEFT = 13;
jvmBasicLexer.RIGHT = 14;
jvmBasicLexer.STR = 15;
jvmBasicLexer.LPAREN = 16;
jvmBasicLexer.RPAREN = 17;
jvmBasicLexer.PLUS = 18;
jvmBasicLexer.MINUS = 19;
jvmBasicLexer.TIMES = 20;
jvmBasicLexer.DIV = 21;
jvmBasicLexer.CLEAR = 22;
jvmBasicLexer.GTE = 23;
jvmBasicLexer.LTE = 24;
jvmBasicLexer.GT = 25;
jvmBasicLexer.LT = 26;
jvmBasicLexer.NEQ = 27;
jvmBasicLexer.COMMA = 28;
jvmBasicLexer.LIST = 29;
jvmBasicLexer.RUN = 30;
jvmBasicLexer.END = 31;
jvmBasicLexer.LET = 32;
jvmBasicLexer.EQ = 33;
jvmBasicLexer.FOR = 34;
jvmBasicLexer.TO = 35;
jvmBasicLexer.STEP = 36;
jvmBasicLexer.INPUT = 37;
jvmBasicLexer.SEMICOLON = 38;
jvmBasicLexer.DIM = 39;
jvmBasicLexer.SQR = 40;
jvmBasicLexer.COLON = 41;
jvmBasicLexer.TEXT = 42;
jvmBasicLexer.HGR = 43;
jvmBasicLexer.HGR2 = 44;
jvmBasicLexer.LEN = 45;
jvmBasicLexer.CALL = 46;
jvmBasicLexer.ASC = 47;
jvmBasicLexer.HPLOT = 48;
jvmBasicLexer.VPLOT = 49;
jvmBasicLexer.PRNUMBER = 50;
jvmBasicLexer.INNUMBER = 51;
jvmBasicLexer.VTAB = 52;
jvmBasicLexer.HTAB = 53;
jvmBasicLexer.HOME = 54;
jvmBasicLexer.ON = 55;
jvmBasicLexer.PDL = 56;
jvmBasicLexer.PLOT = 57;
jvmBasicLexer.PEEK = 58;
jvmBasicLexer.POKE = 59;
jvmBasicLexer.INTF = 60;
jvmBasicLexer.STOP = 61;
jvmBasicLexer.HIMEM = 62;
jvmBasicLexer.LOMEM = 63;
jvmBasicLexer.FLASH = 64;
jvmBasicLexer.INVERSE = 65;
jvmBasicLexer.NORMAL = 66;
jvmBasicLexer.ONERR = 67;
jvmBasicLexer.SPC = 68;
jvmBasicLexer.FRE = 69;
jvmBasicLexer.POS = 70;
jvmBasicLexer.USR = 71;
jvmBasicLexer.TRACE = 72;
jvmBasicLexer.NOTRACE = 73;
jvmBasicLexer.AND = 74;
jvmBasicLexer.OR = 75;
jvmBasicLexer.DATA = 76;
jvmBasicLexer.WAIT = 77;
jvmBasicLexer.READ = 78;
jvmBasicLexer.XDRAW = 79;
jvmBasicLexer.DRAW = 80;
jvmBasicLexer.AT = 81;
jvmBasicLexer.DEF = 82;
jvmBasicLexer.FN = 83;
jvmBasicLexer.VAL = 84;
jvmBasicLexer.TAB = 85;
jvmBasicLexer.SPEED = 86;
jvmBasicLexer.ROT = 87;
jvmBasicLexer.SCALE = 88;
jvmBasicLexer.COLOR = 89;
jvmBasicLexer.HCOLOR = 90;
jvmBasicLexer.HLIN = 91;
jvmBasicLexer.VLIN = 92;
jvmBasicLexer.SCRN = 93;
jvmBasicLexer.POP = 94;
jvmBasicLexer.SHLOAD = 95;
jvmBasicLexer.SIN = 96;
jvmBasicLexer.COS = 97;
jvmBasicLexer.TAN = 98;
jvmBasicLexer.ATAN = 99;
jvmBasicLexer.RND = 100;
jvmBasicLexer.SGN = 101;
jvmBasicLexer.EXP = 102;
jvmBasicLexer.LOG = 103;
jvmBasicLexer.ABS = 104;
jvmBasicLexer.STORE = 105;
jvmBasicLexer.RECALL = 106;
jvmBasicLexer.GET = 107;
jvmBasicLexer.EXPONENT = 108;
jvmBasicLexer.AMPERSAND = 109;
jvmBasicLexer.GR = 110;
jvmBasicLexer.NOT = 111;
jvmBasicLexer.RESTORE = 112;
jvmBasicLexer.SAVE = 113;
jvmBasicLexer.LOAD = 114;
jvmBasicLexer.QUESTION = 115;
jvmBasicLexer.INCLUDE = 116;
jvmBasicLexer.COMMENT = 117;
jvmBasicLexer.STRINGLITERAL = 118;
jvmBasicLexer.LETTERS = 119;
jvmBasicLexer.NUMBER = 120;
jvmBasicLexer.FLOAT = 121;
jvmBasicLexer.CR = 122;
jvmBasicLexer.WS = 123;


jvmBasicLexer.modeNames = [ "DEFAULT_MODE" ];

jvmBasicLexer.literalNames = [ 'null', "'$'", "'%'", "'RETURN'", "'PRINT'", 
                               "'GOTO'", "'GOSUB'", "'IF'", "'NEXT'", "'THEN'", 
                               "'REM'", "'CHR$'", "'MID$'", "'LEFT$'", "'RIGHT$'", 
                               "'STR$'", "'('", "')'", "'+'", "'-'", "'*'", 
                               "'/'", "'CLEAR'", "'>: '", "'<: '", "'>'", 
                               "'<'", "'< >'", "','", "'LIST'", "'RUN'", 
                               "'END'", "'LET'", "'='", "'FOR'", "'TO'", 
                               "'STEP'", "'INPUT'", "';'", "'DIM'", "'SQR'", 
                               "':'", "'TEXT'", "'HGR'", "'HGR2'", "'LEN'", 
                               "'CALL'", "'ASC'", "'HPLOT'", "'VPLOT'", 
                               "'PR#'", "'IN#'", "'VTAB'", "'HTAB'", "'HOME'", 
                               "'ON'", "'PDL'", "'PLOT'", "'PEEK'", "'POKE'", 
                               "'INT'", "'STOP'", "'HIMEM'", "'LOMEM'", 
                               "'FLASH'", "'INVERSE'", "'NORMAL'", "'ONERR'", 
                               "'SPC'", "'FRE'", "'POS'", "'USR'", "'TRACE'", 
                               "'NOTRACE'", "'AND'", "'OR'", "'DATA'", "'WAIT'", 
                               "'READ'", "'XDRAW'", "'DRAW'", "'AT'", "'DEF'", 
                               "'FN'", "'VAL'", "'TAB'", "'SPEED'", "'ROT'", 
                               "'SCALE'", "'COLOR'", "'HCOLOR'", "'HLIN'", 
                               "'VLIN'", "'SCRN'", "'POP'", "'SHLOAD'", 
                               "'SIN'", "'COS'", "'TAN'", "'ATN'", "'RND'", 
                               "'SGN'", "'EXP'", "'LOG'", "'ABS'", "'STORE'", 
                               "'RECALL'", "'GET'", "'^'", "'&'", "'GR'", 
                               "'NOT'", "'RESTORE'", "'SAVE'", "'LOAD'", 
                               "'?'", "'INCLUDE'" ];

jvmBasicLexer.symbolicNames = [ 'null', "DOLLAR", "PERCENT", "RETURN", "PRINT", 
                                "GOTO", "GOSUB", "IF", "NEXT", "THEN", "REM", 
                                "CHR", "MID", "LEFT", "RIGHT", "STR", "LPAREN", 
                                "RPAREN", "PLUS", "MINUS", "TIMES", "DIV", 
                                "CLEAR", "GTE", "LTE", "GT", "LT", "NEQ", 
                                "COMMA", "LIST", "RUN", "END", "LET", "EQ", 
                                "FOR", "TO", "STEP", "INPUT", "SEMICOLON", 
                                "DIM", "SQR", "COLON", "TEXT", "HGR", "HGR2", 
                                "LEN", "CALL", "ASC", "HPLOT", "VPLOT", 
                                "PRNUMBER", "INNUMBER", "VTAB", "HTAB", 
                                "HOME", "ON", "PDL", "PLOT", "PEEK", "POKE", 
                                "INTF", "STOP", "HIMEM", "LOMEM", "FLASH", 
                                "INVERSE", "NORMAL", "ONERR", "SPC", "FRE", 
                                "POS", "USR", "TRACE", "NOTRACE", "AND", 
                                "OR", "DATA", "WAIT", "READ", "XDRAW", "DRAW", 
                                "AT", "DEF", "FN", "VAL", "TAB", "SPEED", 
                                "ROT", "SCALE", "COLOR", "HCOLOR", "HLIN", 
                                "VLIN", "SCRN", "POP", "SHLOAD", "SIN", 
                                "COS", "TAN", "ATAN", "RND", "SGN", "EXP", 
                                "LOG", "ABS", "STORE", "RECALL", "GET", 
                                "EXPONENT", "AMPERSAND", "GR", "NOT", "RESTORE", 
                                "SAVE", "LOAD", "QUESTION", "INCLUDE", "COMMENT", 
                                "STRINGLITERAL", "LETTERS", "NUMBER", "FLOAT", 
                                "CR", "WS" ];

jvmBasicLexer.ruleNames = [ "DOLLAR", "PERCENT", "RETURN", "PRINT", "GOTO", 
                            "GOSUB", "IF", "NEXT", "THEN", "REM", "CHR", 
                            "MID", "LEFT", "RIGHT", "STR", "LPAREN", "RPAREN", 
                            "PLUS", "MINUS", "TIMES", "DIV", "CLEAR", "GTE", 
                            "LTE", "GT", "LT", "NEQ", "COMMA", "LIST", "RUN", 
                            "END", "LET", "EQ", "FOR", "TO", "STEP", "INPUT", 
                            "SEMICOLON", "DIM", "SQR", "COLON", "TEXT", 
                            "HGR", "HGR2", "LEN", "CALL", "ASC", "HPLOT", 
                            "VPLOT", "PRNUMBER", "INNUMBER", "VTAB", "HTAB", 
                            "HOME", "ON", "PDL", "PLOT", "PEEK", "POKE", 
                            "INTF", "STOP", "HIMEM", "LOMEM", "FLASH", "INVERSE", 
                            "NORMAL", "ONERR", "SPC", "FRE", "POS", "USR", 
                            "TRACE", "NOTRACE", "AND", "OR", "DATA", "WAIT", 
                            "READ", "XDRAW", "DRAW", "AT", "DEF", "FN", 
                            "VAL", "TAB", "SPEED", "ROT", "SCALE", "COLOR", 
                            "HCOLOR", "HLIN", "VLIN", "SCRN", "POP", "SHLOAD", 
                            "SIN", "COS", "TAN", "ATAN", "RND", "SGN", "EXP", 
                            "LOG", "ABS", "STORE", "RECALL", "GET", "EXPONENT", 
                            "AMPERSAND", "GR", "NOT", "RESTORE", "SAVE", 
                            "LOAD", "QUESTION", "INCLUDE", "COMMENT", "STRINGLITERAL", 
                            "LETTERS", "NUMBER", "FLOAT", "CR", "WS" ];

jvmBasicLexer.grammarFileName = "jvmBasic.g4";



exports.jvmBasicLexer = jvmBasicLexer;

