{"name": "melt-editor", "version": "0.3.0", "description": "The block-code editor that can melt blocks to code.", "homepage": "https://github.com/dabbler0/melt", "license": "MIT", "authors": ["dabbler0 <<EMAIL>>"], "main": "dist/melt-full.min.js", "ignore": ["**/.*", "node_modules", "bower_components", "test", "tests"], "dependencies": {"ace-builds": "PencilCode/pencilcode-ace-builds#master", "coffee-script": "dabbler0/coffeescript#astbin", "quadtree": "silflow/quadtree-javascript#master", "qunit": "latest", "requirejs": "latest", "acorn": "marijnh/acorn#master", "sax": "isaacs/sax-js"}}