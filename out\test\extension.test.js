"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const assert = require("assert");
const vscode = require("vscode");
suite('Extension Test Suite', () => {
    vscode.window.showInformationMessage('Start all tests.');
    test('Extension should be present', () => {
        assert.ok(vscode.extensions.getExtension('droplet-editor.vscode-droplet'));
    });
    test('Extension should activate', async () => {
        const extension = vscode.extensions.getExtension('droplet-editor.vscode-droplet');
        if (extension) {
            await extension.activate();
            assert.ok(extension.isActive);
        }
    });
    test('Commands should be registered', async () => {
        const commands = await vscode.commands.getCommands(true);
        assert.ok(commands.includes('droplet.openEditor'));
        assert.ok(commands.includes('droplet.createNew'));
        assert.ok(commands.includes('droplet.toggleMode'));
    });
    test('Custom editor should be registered', () => {
        // This test verifies that the custom editor provider is registered
        // In a real test environment, we would check if files with .visual extensions
        // can be opened with our custom editor
        assert.ok(true); // Placeholder - would need more complex setup for full test
    });
});
//# sourceMappingURL=extension.test.js.map