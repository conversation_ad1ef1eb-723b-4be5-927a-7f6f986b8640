// Generated from Java.g4 by ANTLR 4.5
// jshint ignore: start
var antlr4 = require('antlr4/index');
var JavaListener = require('./JavaListener').JavaListener;
var grammarFileName = "Java.g4";

var serializedATN = ["\3\u0430\ud6d1\u8206\uad2d\u4417\uaef1\u8d80\uaadd",
    "\3k\u0501\4\2\t\2\4\3\t\3\4\4\t\4\4\5\t\5\4\6\t\6\4\7\t\7\4\b\t\b\4",
    "\t\t\t\4\n\t\n\4\13\t\13\4\f\t\f\4\r\t\r\4\16\t\16\4\17\t\17\4\20\t",
    "\20\4\21\t\21\4\22\t\22\4\23\t\23\4\24\t\24\4\25\t\25\4\26\t\26\4\27",
    "\t\27\4\30\t\30\4\31\t\31\4\32\t\32\4\33\t\33\4\34\t\34\4\35\t\35\4",
    "\36\t\36\4\37\t\37\4 \t \4!\t!\4\"\t\"\4#\t#\4$\t$\4%\t%\4&\t&\4\'\t",
    "\'\4(\t(\4)\t)\4*\t*\4+\t+\4,\t,\4-\t-\4.\t.\4/\t/\4\60\t\60\4\61\t",
    "\61\4\62\t\62\4\63\t\63\4\64\t\64\4\65\t\65\4\66\t\66\4\67\t\67\48\t",
    "8\49\t9\4:\t:\4;\t;\4<\t<\4=\t=\4>\t>\4?\t?\4@\t@\4A\tA\4B\tB\4C\tC",
    "\4D\tD\4E\tE\4F\tF\4G\tG\4H\tH\4I\tI\4J\tJ\4K\tK\4L\tL\4M\tM\4N\tN\4",
    "O\tO\4P\tP\4Q\tQ\4R\tR\4S\tS\4T\tT\4U\tU\4V\tV\4W\tW\4X\tX\4Y\tY\4Z",
    "\tZ\4[\t[\4\\\t\\\4]\t]\4^\t^\4_\t_\4`\t`\4a\ta\4b\tb\4c\tc\4d\td\4",
    "e\te\4f\tf\3\2\5\2\u00ce\n\2\3\2\7\2\u00d1\n\2\f\2\16\2\u00d4\13\2\3",
    "\2\7\2\u00d7\n\2\f\2\16\2\u00da\13\2\3\2\3\2\3\3\7\3\u00df\n\3\f\3\16",
    "\3\u00e2\13\3\3\3\3\3\3\3\3\3\3\4\3\4\5\4\u00ea\n\4\3\4\3\4\3\4\5\4",
    "\u00ef\n\4\3\4\3\4\3\5\7\5\u00f4\n\5\f\5\16\5\u00f7\13\5\3\5\3\5\7\5",
    "\u00fb\n\5\f\5\16\5\u00fe\13\5\3\5\3\5\7\5\u0102\n\5\f\5\16\5\u0105",
    "\13\5\3\5\3\5\7\5\u0109\n\5\f\5\16\5\u010c\13\5\3\5\3\5\5\5\u0110\n",
    "\5\3\6\3\6\5\6\u0114\n\6\3\7\3\7\5\7\u0118\n\7\3\b\3\b\5\b\u011c\n\b",
    "\3\t\3\t\3\t\5\t\u0121\n\t\3\t\3\t\5\t\u0125\n\t\3\t\3\t\5\t\u0129\n",
    "\t\3\t\3\t\3\n\3\n\3\n\3\n\7\n\u0131\n\n\f\n\16\n\u0134\13\n\3\n\3\n",
    "\3\13\3\13\3\13\5\13\u013b\n\13\3\f\3\f\3\f\7\f\u0140\n\f\f\f\16\f\u0143",
    "\13\f\3\r\3\r\3\r\3\r\5\r\u0149\n\r\3\r\3\r\5\r\u014d\n\r\3\r\5\r\u0150",
    "\n\r\3\r\5\r\u0153\n\r\3\r\3\r\3\16\3\16\3\16\7\16\u015a\n\16\f\16\16",
    "\16\u015d\13\16\3\17\7\17\u0160\n\17\f\17\16\17\u0163\13\17\3\17\3\17",
    "\5\17\u0167\n\17\3\17\5\17\u016a\n\17\3\20\3\20\7\20\u016e\n\20\f\20",
    "\16\20\u0171\13\20\3\21\3\21\3\21\5\21\u0176\n\21\3\21\3\21\5\21\u017a",
    "\n\21\3\21\3\21\3\22\3\22\3\22\7\22\u0181\n\22\f\22\16\22\u0184\13\22",
    "\3\23\3\23\7\23\u0188\n\23\f\23\16\23\u018b\13\23\3\23\3\23\3\24\3\24",
    "\7\24\u0191\n\24\f\24\16\24\u0194\13\24\3\24\3\24\3\25\3\25\5\25\u019a",
    "\n\25\3\25\3\25\7\25\u019e\n\25\f\25\16\25\u01a1\13\25\3\25\5\25\u01a4",
    "\n\25\3\26\3\26\3\26\3\26\3\26\3\26\3\26\3\26\3\26\5\26\u01af\n\26\3",
    "\27\3\27\5\27\u01b3\n\27\3\27\3\27\3\27\3\27\7\27\u01b9\n\27\f\27\16",
    "\27\u01bc\13\27\3\27\3\27\5\27\u01c0\n\27\3\27\3\27\5\27\u01c4\n\27",
    "\3\30\3\30\3\30\3\31\3\31\3\31\3\31\5\31\u01cd\n\31\3\31\3\31\3\32\3",
    "\32\3\32\3\33\3\33\3\33\3\33\3\34\7\34\u01d9\n\34\f\34\16\34\u01dc\13",
    "\34\3\34\3\34\5\34\u01e0\n\34\3\35\3\35\3\35\3\35\3\35\3\35\3\35\5\35",
    "\u01e9\n\35\3\36\3\36\3\36\3\36\7\36\u01ef\n\36\f\36\16\36\u01f2\13",
    "\36\3\36\3\36\3\37\3\37\3\37\7\37\u01f9\n\37\f\37\16\37\u01fc\13\37",
    "\3\37\3\37\3\37\3 \3 \5 \u0203\n \3 \3 \3 \3 \7 \u0209\n \f \16 \u020c",
    "\13 \3 \3 \5 \u0210\n \3 \3 \3!\3!\3!\3\"\3\"\3\"\7\"\u021a\n\"\f\"",
    "\16\"\u021d\13\"\3#\3#\3#\5#\u0222\n#\3$\3$\3$\7$\u0227\n$\f$\16$\u022a",
    "\13$\3%\3%\5%\u022e\n%\3&\3&\3&\3&\7&\u0234\n&\f&\16&\u0237\13&\3&\5",
    "&\u023a\n&\5&\u023c\n&\3&\3&\3\'\3\'\3(\3(\3(\7(\u0245\n(\f(\16(\u0248",
    "\13(\3(\3(\3(\7(\u024d\n(\f(\16(\u0250\13(\5(\u0252\n(\3)\3)\5)\u0256",
    "\n)\3)\3)\3)\5)\u025b\n)\7)\u025d\n)\f)\16)\u0260\13)\3*\3*\3+\3+\3",
    "+\3+\7+\u0268\n+\f+\16+\u026b\13+\3+\3+\3,\3,\3,\3,\5,\u0273\n,\5,\u0275",
    "\n,\3-\3-\3-\7-\u027a\n-\f-\16-\u027d\13-\3.\3.\5.\u0281\n.\3.\3.\3",
    "/\3/\3/\7/\u0288\n/\f/\16/\u028b\13/\3/\3/\5/\u028f\n/\3/\5/\u0292\n",
    "/\3\60\7\60\u0295\n\60\f\60\16\60\u0298\13\60\3\60\3\60\3\60\3\61\7",
    "\61\u029e\n\61\f\61\16\61\u02a1\13\61\3\61\3\61\3\61\3\61\3\62\3\62",
    "\3\63\3\63\3\64\3\64\3\64\7\64\u02ae\n\64\f\64\16\64\u02b1\13\64\3\65",
    "\3\65\3\66\3\66\3\66\3\66\3\66\5\66\u02ba\n\66\3\66\5\66\u02bd\n\66",
    "\3\67\3\67\38\38\38\78\u02c4\n8\f8\168\u02c7\138\39\39\39\39\3:\3:\3",
    ":\5:\u02d0\n:\3;\3;\3;\3;\7;\u02d6\n;\f;\16;\u02d9\13;\5;\u02db\n;\3",
    ";\5;\u02de\n;\3;\3;\3<\3<\3<\3<\3<\3=\3=\7=\u02e9\n=\f=\16=\u02ec\13",
    "=\3=\3=\3>\7>\u02f1\n>\f>\16>\u02f4\13>\3>\3>\5>\u02f8\n>\3?\3?\3?\3",
    "?\3?\3?\5?\u0300\n?\3?\3?\5?\u0304\n?\3?\3?\5?\u0308\n?\3?\3?\5?\u030c",
    "\n?\5?\u030e\n?\3@\3@\5@\u0312\n@\3A\3A\3A\3A\5A\u0318\nA\3B\3B\3C\3",
    "C\3C\3D\3D\7D\u0321\nD\fD\16D\u0324\13D\3D\3D\3E\3E\3E\5E\u032b\nE\3",
    "F\3F\3F\3G\7G\u0331\nG\fG\16G\u0334\13G\3G\3G\3G\3H\3H\3H\3H\3H\5H\u033e",
    "\nH\3H\3H\3H\3H\3H\3H\3H\5H\u0347\nH\3H\3H\3H\3H\3H\3H\3H\3H\3H\3H\3",
    "H\3H\3H\3H\3H\3H\3H\3H\3H\6H\u035c\nH\rH\16H\u035d\3H\5H\u0361\nH\3",
    "H\5H\u0364\nH\3H\3H\3H\3H\7H\u036a\nH\fH\16H\u036d\13H\3H\5H\u0370\n",
    "H\3H\3H\3H\3H\7H\u0376\nH\fH\16H\u0379\13H\3H\7H\u037c\nH\fH\16H\u037f",
    "\13H\3H\3H\3H\3H\3H\3H\3H\3H\5H\u0389\nH\3H\3H\3H\3H\3H\3H\3H\5H\u0392",
    "\nH\3H\3H\3H\5H\u0397\nH\3H\3H\3H\3H\3H\3H\3H\3H\5H\u03a1\nH\3I\3I\3",
    "I\7I\u03a6\nI\fI\16I\u03a9\13I\3I\3I\3I\3I\3I\3J\3J\3J\7J\u03b3\nJ\f",
    "J\16J\u03b6\13J\3K\3K\3K\3L\3L\3L\5L\u03be\nL\3L\3L\3M\3M\3M\7M\u03c5",
    "\nM\fM\16M\u03c8\13M\3N\7N\u03cb\nN\fN\16N\u03ce\13N\3N\3N\3N\3N\3N",
    "\3O\6O\u03d6\nO\rO\16O\u03d7\3O\6O\u03db\nO\rO\16O\u03dc\3P\3P\3P\3",
    "P\3P\3P\3P\3P\3P\3P\5P\u03e9\nP\3Q\3Q\5Q\u03ed\nQ\3Q\3Q\5Q\u03f1\nQ",
    "\3Q\3Q\5Q\u03f5\nQ\5Q\u03f7\nQ\3R\3R\5R\u03fb\nR\3S\7S\u03fe\nS\fS\16",
    "S\u0401\13S\3S\3S\3S\3S\3S\3T\3T\3U\3U\3U\3U\3V\3V\3V\7V\u0411\nV\f",
    "V\16V\u0414\13V\3W\3W\3X\3X\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\5",
    "Y\u0427\nY\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\5Y\u0437\nY\3Y",
    "\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3",
    "Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\5Y\u0462\nY\3Y",
    "\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\3Y\5Y\u0474\nY\3Y\3Y\3Y\3",
    "Y\3Y\3Y\7Y\u047c\nY\fY\16Y\u047f\13Y\3Z\3Z\3Z\3Z\3Z\3Z\3Z\3Z\3Z\3Z\3",
    "Z\3Z\3Z\3Z\3Z\3Z\3Z\3Z\3Z\5Z\u0494\nZ\5Z\u0496\nZ\3[\3[\3[\3[\3[\3[",
    "\3[\5[\u049f\n[\5[\u04a1\n[\3\\\3\\\5\\\u04a5\n\\\3\\\3\\\3\\\5\\\u04aa",
    "\n\\\7\\\u04ac\n\\\f\\\16\\\u04af\13\\\3\\\5\\\u04b2\n\\\3]\3]\5]\u04b6",
    "\n]\3]\3]\3^\3^\3^\3^\7^\u04be\n^\f^\16^\u04c1\13^\3^\3^\3^\3^\3^\3",
    "^\3^\7^\u04ca\n^\f^\16^\u04cd\13^\3^\3^\7^\u04d1\n^\f^\16^\u04d4\13",
    "^\5^\u04d6\n^\3_\3_\5_\u04da\n_\3`\3`\3`\3a\3a\3a\3a\3b\3b\3b\5b\u04e6",
    "\nb\3c\3c\3c\5c\u04eb\nc\3d\3d\3d\3d\5d\u04f1\nd\5d\u04f3\nd\3e\3e\3",
    "e\3e\5e\u04f9\ne\3f\3f\5f\u04fd\nf\3f\3f\3f\2\3\u00b0g\2\4\6\b\n\f\16",
    "\20\22\24\26\30\32\34\36 \"$&(*,.\60\62\64\668:<>@BDFHJLNPRTVXZ\\^`",
    "bdfhjlnprtvxz|~\u0080\u0082\u0084\u0086\u0088\u008a\u008c\u008e\u0090",
    "\u0092\u0094\u0096\u0098\u009a\u009c\u009e\u00a0\u00a2\u00a4\u00a6\u00a8",
    "\u00aa\u00ac\u00ae\u00b0\u00b2\u00b4\u00b6\u00b8\u00ba\u00bc\u00be\u00c0",
    "\u00c2\u00c4\u00c6\u00c8\u00ca\2\17\6\2  ,,\60\60\63\63\6\2\3\3\24\24",
    "#%()\n\2\5\5\7\7\n\n\20\20\26\26\35\35\37\37\'\'\4\2\23\23**\3\2\65",
    ":\3\2QT\3\2GH\4\2UVZZ\3\2ST\4\2EFLM\4\2KKNN\4\2DD[e\3\2QR\u0573\2\u00cd",
    "\3\2\2\2\4\u00e0\3\2\2\2\6\u00e7\3\2\2\2\b\u010f\3\2\2\2\n\u0113\3\2",
    "\2\2\f\u0117\3\2\2\2\16\u011b\3\2\2\2\20\u011d\3\2\2\2\22\u012c\3\2",
    "\2\2\24\u0137\3\2\2\2\26\u013c\3\2\2\2\30\u0144\3\2\2\2\32\u0156\3\2",
    "\2\2\34\u0161\3\2\2\2\36\u016b\3\2\2\2 \u0172\3\2\2\2\"\u017d\3\2\2",
    "\2$\u0185\3\2\2\2&\u018e\3\2\2\2(\u01a3\3\2\2\2*\u01ae\3\2\2\2,\u01b2",
    "\3\2\2\2.\u01c5\3\2\2\2\60\u01c8\3\2\2\2\62\u01d0\3\2\2\2\64\u01d3\3",
    "\2\2\2\66\u01df\3\2\2\28\u01e8\3\2\2\2:\u01ea\3\2\2\2<\u01f5\3\2\2\2",
    ">\u0202\3\2\2\2@\u0213\3\2\2\2B\u0216\3\2\2\2D\u021e\3\2\2\2F\u0223",
    "\3\2\2\2H\u022d\3\2\2\2J\u022f\3\2\2\2L\u023f\3\2\2\2N\u0251\3\2\2\2",
    "P\u0253\3\2\2\2R\u0261\3\2\2\2T\u0263\3\2\2\2V\u0274\3\2\2\2X\u0276",
    "\3\2\2\2Z\u027e\3\2\2\2\\\u0291\3\2\2\2^\u0296\3\2\2\2`\u029f\3\2\2",
    "\2b\u02a6\3\2\2\2d\u02a8\3\2\2\2f\u02aa\3\2\2\2h\u02b2\3\2\2\2j\u02b4",
    "\3\2\2\2l\u02be\3\2\2\2n\u02c0\3\2\2\2p\u02c8\3\2\2\2r\u02cf\3\2\2\2",
    "t\u02d1\3\2\2\2v\u02e1\3\2\2\2x\u02e6\3\2\2\2z\u02f7\3\2\2\2|\u030d",
    "\3\2\2\2~\u0311\3\2\2\2\u0080\u0313\3\2\2\2\u0082\u0319\3\2\2\2\u0084",
    "\u031b\3\2\2\2\u0086\u031e\3\2\2\2\u0088\u032a\3\2\2\2\u008a\u032c\3",
    "\2\2\2\u008c\u0332\3\2\2\2\u008e\u03a0\3\2\2\2\u0090\u03a2\3\2\2\2\u0092",
    "\u03af\3\2\2\2\u0094\u03b7\3\2\2\2\u0096\u03ba\3\2\2\2\u0098\u03c1\3",
    "\2\2\2\u009a\u03cc\3\2\2\2\u009c\u03d5\3\2\2\2\u009e\u03e8\3\2\2\2\u00a0",
    "\u03f6\3\2\2\2\u00a2\u03fa\3\2\2\2\u00a4\u03ff\3\2\2\2\u00a6\u0407\3",
    "\2\2\2\u00a8\u0409\3\2\2\2\u00aa\u040d\3\2\2\2\u00ac\u0415\3\2\2\2\u00ae",
    "\u0417\3\2\2\2\u00b0\u0426\3\2\2\2\u00b2\u0495\3\2\2\2\u00b4\u04a0\3",
    "\2\2\2\u00b6\u04b1\3\2\2\2\u00b8\u04b3\3\2\2\2\u00ba\u04b9\3\2\2\2\u00bc",
    "\u04d7\3\2\2\2\u00be\u04db\3\2\2\2\u00c0\u04de\3\2\2\2\u00c2\u04e5\3",
    "\2\2\2\u00c4\u04ea\3\2\2\2\u00c6\u04f2\3\2\2\2\u00c8\u04f8\3\2\2\2\u00ca",
    "\u04fa\3\2\2\2\u00cc\u00ce\5\4\3\2\u00cd\u00cc\3\2\2\2\u00cd\u00ce\3",
    "\2\2\2\u00ce\u00d2\3\2\2\2\u00cf\u00d1\5\6\4\2\u00d0\u00cf\3\2\2\2\u00d1",
    "\u00d4\3\2\2\2\u00d2\u00d0\3\2\2\2\u00d2\u00d3\3\2\2\2\u00d3\u00d8\3",
    "\2\2\2\u00d4\u00d2\3\2\2\2\u00d5\u00d7\5\b\5\2\u00d6\u00d5\3\2\2\2\u00d7",
    "\u00da\3\2\2\2\u00d8\u00d6\3\2\2\2\u00d8\u00d9\3\2\2\2\u00d9\u00db\3",
    "\2\2\2\u00da\u00d8\3\2\2\2\u00db\u00dc\7\2\2\3\u00dc\3\3\2\2\2\u00dd",
    "\u00df\5j\66\2\u00de\u00dd\3\2\2\2\u00df\u00e2\3\2\2\2\u00e0\u00de\3",
    "\2\2\2\u00e0\u00e1\3\2\2\2\u00e1\u00e3\3\2\2\2\u00e2\u00e0\3\2\2\2\u00e3",
    "\u00e4\7\"\2\2\u00e4\u00e5\5f\64\2\u00e5\u00e6\7A\2\2\u00e6\5\3\2\2",
    "\2\u00e7\u00e9\7\33\2\2\u00e8\u00ea\7(\2\2\u00e9\u00e8\3\2\2\2\u00e9",
    "\u00ea\3\2\2\2\u00ea\u00eb\3\2\2\2\u00eb\u00ee\5f\64\2\u00ec\u00ed\7",
    "C\2\2\u00ed\u00ef\7U\2\2\u00ee\u00ec\3\2\2\2\u00ee\u00ef\3\2\2\2\u00ef",
    "\u00f0\3\2\2\2\u00f0\u00f1\7A\2\2\u00f1\7\3\2\2\2\u00f2\u00f4\5\f\7",
    "\2\u00f3\u00f2\3\2\2\2\u00f4\u00f7\3\2\2\2\u00f5\u00f3\3\2\2\2\u00f5",
    "\u00f6\3\2\2\2\u00f6\u00f8\3\2\2\2\u00f7\u00f5\3\2\2\2\u00f8\u0110\5",
    "\20\t\2\u00f9\u00fb\5\f\7\2\u00fa\u00f9\3\2\2\2\u00fb\u00fe\3\2\2\2",
    "\u00fc\u00fa\3\2\2\2\u00fc\u00fd\3\2\2\2\u00fd\u00ff\3\2\2\2\u00fe\u00fc",
    "\3\2\2\2\u00ff\u0110\5\30\r\2\u0100\u0102\5\f\7\2\u0101\u0100\3\2\2",
    "\2\u0102\u0105\3\2\2\2\u0103\u0101\3\2\2\2\u0103\u0104\3\2\2\2\u0104",
    "\u0106\3\2\2\2\u0105\u0103\3\2\2\2\u0106\u0110\5 \21\2\u0107\u0109\5",
    "\f\7\2\u0108\u0107\3\2\2\2\u0109\u010c\3\2\2\2\u010a\u0108\3\2\2\2\u010a",
    "\u010b\3\2\2\2\u010b\u010d\3\2\2\2\u010c\u010a\3\2\2\2\u010d\u0110\5",
    "v<\2\u010e\u0110\7A\2\2\u010f\u00f5\3\2\2\2\u010f\u00fc\3\2\2\2\u010f",
    "\u0103\3\2\2\2\u010f\u010a\3\2\2\2\u010f\u010e\3\2\2\2\u0110\t\3\2\2",
    "\2\u0111\u0114\5\f\7\2\u0112\u0114\t\2\2\2\u0113\u0111\3\2\2\2\u0113",
    "\u0112\3\2\2\2\u0114\13\3\2\2\2\u0115\u0118\5j\66\2\u0116\u0118\t\3",
    "\2\2\u0117\u0115\3\2\2\2\u0117\u0116\3\2\2\2\u0118\r\3\2\2\2\u0119\u011c",
    "\7\24\2\2\u011a\u011c\5j\66\2\u011b\u0119\3\2\2\2\u011b\u011a\3\2\2",
    "\2\u011c\17\3\2\2\2\u011d\u011e\7\13\2\2\u011e\u0120\7f\2\2\u011f\u0121",
    "\5\22\n\2\u0120\u011f\3\2\2\2\u0120\u0121\3\2\2\2\u0121\u0124\3\2\2",
    "\2\u0122\u0123\7\23\2\2\u0123\u0125\5N(\2\u0124\u0122\3\2\2\2\u0124",
    "\u0125\3\2\2\2\u0125\u0128\3\2\2\2\u0126\u0127\7\32\2\2\u0127\u0129",
    "\5\"\22\2\u0128\u0126\3\2\2\2\u0128\u0129\3\2\2\2\u0129\u012a\3\2\2",
    "\2\u012a\u012b\5$\23\2\u012b\21\3\2\2\2\u012c\u012d\7F\2\2\u012d\u0132",
    "\5\24\13\2\u012e\u012f\7B\2\2\u012f\u0131\5\24\13\2\u0130\u012e\3\2",
    "\2\2\u0131\u0134\3\2\2\2\u0132\u0130\3\2\2\2\u0132\u0133\3\2\2\2\u0133",
    "\u0135\3\2\2\2\u0134\u0132\3\2\2\2\u0135\u0136\7E\2\2\u0136\23\3\2\2",
    "\2\u0137\u013a\7f\2\2\u0138\u0139\7\23\2\2\u0139\u013b\5\26\f\2\u013a",
    "\u0138\3\2\2\2\u013a\u013b\3\2\2\2\u013b\25\3\2\2\2\u013c\u0141\5N(",
    "\2\u013d\u013e\7W\2\2\u013e\u0140\5N(\2\u013f\u013d\3\2\2\2\u0140\u0143",
    "\3\2\2\2\u0141\u013f\3\2\2\2\u0141\u0142\3\2\2\2\u0142\27\3\2\2\2\u0143",
    "\u0141\3\2\2\2\u0144\u0145\7\22\2\2\u0145\u0148\7f\2\2\u0146\u0147\7",
    "\32\2\2\u0147\u0149\5\"\22\2\u0148\u0146\3\2\2\2\u0148\u0149\3\2\2\2",
    "\u0149\u014a\3\2\2\2\u014a\u014c\7=\2\2\u014b\u014d\5\32\16\2\u014c",
    "\u014b\3\2\2\2\u014c\u014d\3\2\2\2\u014d\u014f\3\2\2\2\u014e\u0150\7",
    "B\2\2\u014f\u014e\3\2\2\2\u014f\u0150\3\2\2\2\u0150\u0152\3\2\2\2\u0151",
    "\u0153\5\36\20\2\u0152\u0151\3\2\2\2\u0152\u0153\3\2\2\2\u0153\u0154",
    "\3\2\2\2\u0154\u0155\7>\2\2\u0155\31\3\2\2\2\u0156\u015b\5\34\17\2\u0157",
    "\u0158\7B\2\2\u0158\u015a\5\34\17\2\u0159\u0157\3\2\2\2\u015a\u015d",
    "\3\2\2\2\u015b\u0159\3\2\2\2\u015b\u015c\3\2\2\2\u015c\33\3\2\2\2\u015d",
    "\u015b\3\2\2\2\u015e\u0160\5j\66\2\u015f\u015e\3\2\2\2\u0160\u0163\3",
    "\2\2\2\u0161\u015f\3\2\2\2\u0161\u0162\3\2\2\2\u0162\u0164\3\2\2\2\u0163",
    "\u0161\3\2\2\2\u0164\u0166\7f\2\2\u0165\u0167\5\u00caf\2\u0166\u0165",
    "\3\2\2\2\u0166\u0167\3\2\2\2\u0167\u0169\3\2\2\2\u0168\u016a\5$\23\2",
    "\u0169\u0168\3\2\2\2\u0169\u016a\3\2\2\2\u016a\35\3\2\2\2\u016b\u016f",
    "\7A\2\2\u016c\u016e\5(\25\2\u016d\u016c\3\2\2\2\u016e\u0171\3\2\2\2",
    "\u016f\u016d\3\2\2\2\u016f\u0170\3\2\2\2\u0170\37\3\2\2\2\u0171\u016f",
    "\3\2\2\2\u0172\u0173\7\36\2\2\u0173\u0175\7f\2\2\u0174\u0176\5\22\n",
    "\2\u0175\u0174\3\2\2\2\u0175\u0176\3\2\2\2\u0176\u0179\3\2\2\2\u0177",
    "\u0178\7\23\2\2\u0178\u017a\5\"\22\2\u0179\u0177\3\2\2\2\u0179\u017a",
    "\3\2\2\2\u017a\u017b\3\2\2\2\u017b\u017c\5&\24\2\u017c!\3\2\2\2\u017d",
    "\u0182\5N(\2\u017e\u017f\7B\2\2\u017f\u0181\5N(\2\u0180\u017e\3\2\2",
    "\2\u0181\u0184\3\2\2\2\u0182\u0180\3\2\2\2\u0182\u0183\3\2\2\2\u0183",
    "#\3\2\2\2\u0184\u0182\3\2\2\2\u0185\u0189\7=\2\2\u0186\u0188\5(\25\2",
    "\u0187\u0186\3\2\2\2\u0188\u018b\3\2\2\2\u0189\u0187\3\2\2\2\u0189\u018a",
    "\3\2\2\2\u018a\u018c\3\2\2\2\u018b\u0189\3\2\2\2\u018c\u018d\7>\2\2",
    "\u018d%\3\2\2\2\u018e\u0192\7=\2\2\u018f\u0191\5\66\34\2\u0190\u018f",
    "\3\2\2\2\u0191\u0194\3\2\2\2\u0192\u0190\3\2\2\2\u0192\u0193\3\2\2\2",
    "\u0193\u0195\3\2\2\2\u0194\u0192\3\2\2\2\u0195\u0196\7>\2\2\u0196\'",
    "\3\2\2\2\u0197\u01a4\7A\2\2\u0198\u019a\7(\2\2\u0199\u0198\3\2\2\2\u0199",
    "\u019a\3\2\2\2\u019a\u019b\3\2\2\2\u019b\u01a4\5\u0086D\2\u019c\u019e",
    "\5\n\6\2\u019d\u019c\3\2\2\2\u019e\u01a1\3\2\2\2\u019f\u019d\3\2\2\2",
    "\u019f\u01a0\3\2\2\2\u01a0\u01a2\3\2\2\2\u01a1\u019f\3\2\2\2\u01a2\u01a4",
    "\5*\26\2\u01a3\u0197\3\2\2\2\u01a3\u0199\3\2\2\2\u01a3\u019f\3\2\2\2",
    "\u01a4)\3\2\2\2\u01a5\u01af\5,\27\2\u01a6\u01af\5.\30\2\u01a7\u01af",
    "\5\64\33\2\u01a8\u01af\5\60\31\2\u01a9\u01af\5\62\32\2\u01aa\u01af\5",
    " \21\2\u01ab\u01af\5v<\2\u01ac\u01af\5\20\t\2\u01ad\u01af\5\30\r\2\u01ae",
    "\u01a5\3\2\2\2\u01ae\u01a6\3\2\2\2\u01ae\u01a7\3\2\2\2\u01ae\u01a8\3",
    "\2\2\2\u01ae\u01a9\3\2\2\2\u01ae\u01aa\3\2\2\2\u01ae\u01ab\3\2\2\2\u01ae",
    "\u01ac\3\2\2\2\u01ae\u01ad\3\2\2\2\u01af+\3\2\2\2\u01b0\u01b3\5N(\2",
    "\u01b1\u01b3\7\62\2\2\u01b2\u01b0\3\2\2\2\u01b2\u01b1\3\2\2\2\u01b3",
    "\u01b4\3\2\2\2\u01b4\u01b5\7f\2\2\u01b5\u01ba\5Z.\2\u01b6\u01b7\7?\2",
    "\2\u01b7\u01b9\7@\2\2\u01b8\u01b6\3\2\2\2\u01b9\u01bc\3\2\2\2\u01ba",
    "\u01b8\3\2\2\2\u01ba\u01bb\3\2\2\2\u01bb\u01bf\3\2\2\2\u01bc\u01ba\3",
    "\2\2\2\u01bd\u01be\7/\2\2\u01be\u01c0\5X-\2\u01bf\u01bd\3\2\2\2\u01bf",
    "\u01c0\3\2\2\2\u01c0\u01c3\3\2\2\2\u01c1\u01c4\5b\62\2\u01c2\u01c4\7",
    "A\2\2\u01c3\u01c1\3\2\2\2\u01c3\u01c2\3\2\2\2\u01c4-\3\2\2\2\u01c5\u01c6",
    "\5\22\n\2\u01c6\u01c7\5,\27\2\u01c7/\3\2\2\2\u01c8\u01c9\7f\2\2\u01c9",
    "\u01cc\5Z.\2\u01ca\u01cb\7/\2\2\u01cb\u01cd\5X-\2\u01cc\u01ca\3\2\2",
    "\2\u01cc\u01cd\3\2\2\2\u01cd\u01ce\3\2\2\2\u01ce\u01cf\5d\63\2\u01cf",
    "\61\3\2\2\2\u01d0\u01d1\5\22\n\2\u01d1\u01d2\5\60\31\2\u01d2\63\3\2",
    "\2\2\u01d3\u01d4\5N(\2\u01d4\u01d5\5B\"\2\u01d5\u01d6\7A\2\2\u01d6\65",
    "\3\2\2\2\u01d7\u01d9\5\n\6\2\u01d8\u01d7\3\2\2\2\u01d9\u01dc\3\2\2\2",
    "\u01da\u01d8\3\2\2\2\u01da\u01db\3\2\2\2\u01db\u01dd\3\2\2\2\u01dc\u01da",
    "\3\2\2\2\u01dd\u01e0\58\35\2\u01de\u01e0\7A\2\2\u01df\u01da\3\2\2\2",
    "\u01df\u01de\3\2\2\2\u01e0\67\3\2\2\2\u01e1\u01e9\5:\36\2\u01e2\u01e9",
    "\5> \2\u01e3\u01e9\5@!\2\u01e4\u01e9\5 \21\2\u01e5\u01e9\5v<\2\u01e6",
    "\u01e9\5\20\t\2\u01e7\u01e9\5\30\r\2\u01e8\u01e1\3\2\2\2\u01e8\u01e2",
    "\3\2\2\2\u01e8\u01e3\3\2\2\2\u01e8\u01e4\3\2\2\2\u01e8\u01e5\3\2\2\2",
    "\u01e8\u01e6\3\2\2\2\u01e8\u01e7\3\2\2\2\u01e99\3\2\2\2\u01ea\u01eb",
    "\5N(\2\u01eb\u01f0\5<\37\2\u01ec\u01ed\7B\2\2\u01ed\u01ef\5<\37\2\u01ee",
    "\u01ec\3\2\2\2\u01ef\u01f2\3\2\2\2\u01f0\u01ee\3\2\2\2\u01f0\u01f1\3",
    "\2\2\2\u01f1\u01f3\3\2\2\2\u01f2\u01f0\3\2\2\2\u01f3\u01f4\7A\2\2\u01f4",
    ";\3\2\2\2\u01f5\u01fa\7f\2\2\u01f6\u01f7\7?\2\2\u01f7\u01f9\7@\2\2\u01f8",
    "\u01f6\3\2\2\2\u01f9\u01fc\3\2\2\2\u01fa\u01f8\3\2\2\2\u01fa\u01fb\3",
    "\2\2\2\u01fb\u01fd\3\2\2\2\u01fc\u01fa\3\2\2\2\u01fd\u01fe\7D\2\2\u01fe",
    "\u01ff\5H%\2\u01ff=\3\2\2\2\u0200\u0203\5N(\2\u0201\u0203\7\62\2\2\u0202",
    "\u0200\3\2\2\2\u0202\u0201\3\2\2\2\u0203\u0204\3\2\2\2\u0204\u0205\7",
    "f\2\2\u0205\u020a\5Z.\2\u0206\u0207\7?\2\2\u0207\u0209\7@\2\2\u0208",
    "\u0206\3\2\2\2\u0209\u020c\3\2\2\2\u020a\u0208\3\2\2\2\u020a\u020b\3",
    "\2\2\2\u020b\u020f\3\2\2\2\u020c\u020a\3\2\2\2\u020d\u020e\7/\2\2\u020e",
    "\u0210\5X-\2\u020f\u020d\3\2\2\2\u020f\u0210\3\2\2\2\u0210\u0211\3\2",
    "\2\2\u0211\u0212\7A\2\2\u0212?\3\2\2\2\u0213\u0214\5\22\n\2\u0214\u0215",
    "\5> \2\u0215A\3\2\2\2\u0216\u021b\5D#\2\u0217\u0218\7B\2\2\u0218\u021a",
    "\5D#\2\u0219\u0217\3\2\2\2\u021a\u021d\3\2\2\2\u021b\u0219\3\2\2\2\u021b",
    "\u021c\3\2\2\2\u021cC\3\2\2\2\u021d\u021b\3\2\2\2\u021e\u0221\5F$\2",
    "\u021f\u0220\7D\2\2\u0220\u0222\5H%\2\u0221\u021f\3\2\2\2\u0221\u0222",
    "\3\2\2\2\u0222E\3\2\2\2\u0223\u0228\7f\2\2\u0224\u0225\7?\2\2\u0225",
    "\u0227\7@\2\2\u0226\u0224\3\2\2\2\u0227\u022a\3\2\2\2\u0228\u0226\3",
    "\2\2\2\u0228\u0229\3\2\2\2\u0229G\3\2\2\2\u022a\u0228\3\2\2\2\u022b",
    "\u022e\5J&\2\u022c\u022e\5\u00b0Y\2\u022d\u022b\3\2\2\2\u022d\u022c",
    "\3\2\2\2\u022eI\3\2\2\2\u022f\u023b\7=\2\2\u0230\u0235\5H%\2\u0231\u0232",
    "\7B\2\2\u0232\u0234\5H%\2\u0233\u0231\3\2\2\2\u0234\u0237\3\2\2\2\u0235",
    "\u0233\3\2\2\2\u0235\u0236\3\2\2\2\u0236\u0239\3\2\2\2\u0237\u0235\3",
    "\2\2\2\u0238\u023a\7B\2\2\u0239\u0238\3\2\2\2\u0239\u023a\3\2\2\2\u023a",
    "\u023c\3\2\2\2\u023b\u0230\3\2\2\2\u023b\u023c\3\2\2\2\u023c\u023d\3",
    "\2\2\2\u023d\u023e\7>\2\2\u023eK\3\2\2\2\u023f\u0240\7f\2\2\u0240M\3",
    "\2\2\2\u0241\u0246\5P)\2\u0242\u0243\7?\2\2\u0243\u0245\7@\2\2\u0244",
    "\u0242\3\2\2\2\u0245\u0248\3\2\2\2\u0246\u0244\3\2\2\2\u0246\u0247\3",
    "\2\2\2\u0247\u0252\3\2\2\2\u0248\u0246\3\2\2\2\u0249\u024e\5R*\2\u024a",
    "\u024b\7?\2\2\u024b\u024d\7@\2\2\u024c\u024a\3\2\2\2\u024d\u0250\3\2",
    "\2\2\u024e\u024c\3\2\2\2\u024e\u024f\3\2\2\2\u024f\u0252\3\2\2\2\u0250",
    "\u024e\3\2\2\2\u0251\u0241\3\2\2\2\u0251\u0249\3\2\2\2\u0252O\3\2\2",
    "\2\u0253\u0255\7f\2\2\u0254\u0256\5T+\2\u0255\u0254\3\2\2\2\u0255\u0256",
    "\3\2\2\2\u0256\u025e\3\2\2\2\u0257\u0258\7C\2\2\u0258\u025a\7f\2\2\u0259",
    "\u025b\5T+\2\u025a\u0259\3\2\2\2\u025a\u025b\3\2\2\2\u025b\u025d\3\2",
    "\2\2\u025c\u0257\3\2\2\2\u025d\u0260\3\2\2\2\u025e\u025c\3\2\2\2\u025e",
    "\u025f\3\2\2\2\u025fQ\3\2\2\2\u0260\u025e\3\2\2\2\u0261\u0262\t\4\2",
    "\2\u0262S\3\2\2\2\u0263\u0264\7F\2\2\u0264\u0269\5V,\2\u0265\u0266\7",
    "B\2\2\u0266\u0268\5V,\2\u0267\u0265\3\2\2\2\u0268\u026b\3\2\2\2\u0269",
    "\u0267\3\2\2\2\u0269\u026a\3\2\2\2\u026a\u026c\3\2\2\2\u026b\u0269\3",
    "\2\2\2\u026c\u026d\7E\2\2\u026dU\3\2\2\2\u026e\u0275\5N(\2\u026f\u0272",
    "\7I\2\2\u0270\u0271\t\5\2\2\u0271\u0273\5N(\2\u0272\u0270\3\2\2\2\u0272",
    "\u0273\3\2\2\2\u0273\u0275\3\2\2\2\u0274\u026e\3\2\2\2\u0274\u026f\3",
    "\2\2\2\u0275W\3\2\2\2\u0276\u027b\5f\64\2\u0277\u0278\7B\2\2\u0278\u027a",
    "\5f\64\2\u0279\u0277\3\2\2\2\u027a\u027d\3\2\2\2\u027b\u0279\3\2\2\2",
    "\u027b\u027c\3\2\2\2\u027cY\3\2\2\2\u027d\u027b\3\2\2\2\u027e\u0280",
    "\7;\2\2\u027f\u0281\5\\/\2\u0280\u027f\3\2\2\2\u0280\u0281\3\2\2\2\u0281",
    "\u0282\3\2\2\2\u0282\u0283\7<\2\2\u0283[\3\2\2\2\u0284\u0289\5^\60\2",
    "\u0285\u0286\7B\2\2\u0286\u0288\5^\60\2\u0287\u0285\3\2\2\2\u0288\u028b",
    "\3\2\2\2\u0289\u0287\3\2\2\2\u0289\u028a\3\2\2\2\u028a\u028e\3\2\2\2",
    "\u028b\u0289\3\2\2\2\u028c\u028d\7B\2\2\u028d\u028f\5`\61\2\u028e\u028c",
    "\3\2\2\2\u028e\u028f\3\2\2\2\u028f\u0292\3\2\2\2\u0290\u0292\5`\61\2",
    "\u0291\u0284\3\2\2\2\u0291\u0290\3\2\2\2\u0292]\3\2\2\2\u0293\u0295",
    "\5\16\b\2\u0294\u0293\3\2\2\2\u0295\u0298\3\2\2\2\u0296\u0294\3\2\2",
    "\2\u0296\u0297\3\2\2\2\u0297\u0299\3\2\2\2\u0298\u0296\3\2\2\2\u0299",
    "\u029a\5N(\2\u029a\u029b\5F$\2\u029b_\3\2\2\2\u029c\u029e\5\16\b\2\u029d",
    "\u029c\3\2\2\2\u029e\u02a1\3\2\2\2\u029f\u029d\3\2\2\2\u029f\u02a0\3",
    "\2\2\2\u02a0\u02a2\3\2\2\2\u02a1\u029f\3\2\2\2\u02a2\u02a3\5N(\2\u02a3",
    "\u02a4\7h\2\2\u02a4\u02a5\5F$\2\u02a5a\3\2\2\2\u02a6\u02a7\5\u0086D",
    "\2\u02a7c\3\2\2\2\u02a8\u02a9\5\u0086D\2\u02a9e\3\2\2\2\u02aa\u02af",
    "\7f\2\2\u02ab\u02ac\7C\2\2\u02ac\u02ae\7f\2\2\u02ad\u02ab\3\2\2\2\u02ae",
    "\u02b1\3\2\2\2\u02af\u02ad\3\2\2\2\u02af\u02b0\3\2\2\2\u02b0g\3\2\2",
    "\2\u02b1\u02af\3\2\2\2\u02b2\u02b3\t\6\2\2\u02b3i\3\2\2\2\u02b4\u02b5",
    "\7g\2\2\u02b5\u02bc\5l\67\2\u02b6\u02b9\7;\2\2\u02b7\u02ba\5n8\2\u02b8",
    "\u02ba\5r:\2\u02b9\u02b7\3\2\2\2\u02b9\u02b8\3\2\2\2\u02b9\u02ba\3\2",
    "\2\2\u02ba\u02bb\3\2\2\2\u02bb\u02bd\7<\2\2\u02bc\u02b6\3\2\2\2\u02bc",
    "\u02bd\3\2\2\2\u02bdk\3\2\2\2\u02be\u02bf\5f\64\2\u02bfm\3\2\2\2\u02c0",
    "\u02c5\5p9\2\u02c1\u02c2\7B\2\2\u02c2\u02c4\5p9\2\u02c3\u02c1\3\2\2",
    "\2\u02c4\u02c7\3\2\2\2\u02c5\u02c3\3\2\2\2\u02c5\u02c6\3\2\2\2\u02c6",
    "o\3\2\2\2\u02c7\u02c5\3\2\2\2\u02c8\u02c9\7f\2\2\u02c9\u02ca\7D\2\2",
    "\u02ca\u02cb\5r:\2\u02cbq\3\2\2\2\u02cc\u02d0\5\u00b0Y\2\u02cd\u02d0",
    "\5j\66\2\u02ce\u02d0\5t;\2\u02cf\u02cc\3\2\2\2\u02cf\u02cd\3\2\2\2\u02cf",
    "\u02ce\3\2\2\2\u02d0s\3\2\2\2\u02d1\u02da\7=\2\2\u02d2\u02d7\5r:\2\u02d3",
    "\u02d4\7B\2\2\u02d4\u02d6\5r:\2\u02d5\u02d3\3\2\2\2\u02d6\u02d9\3\2",
    "\2\2\u02d7\u02d5\3\2\2\2\u02d7\u02d8\3\2\2\2\u02d8\u02db\3\2\2\2\u02d9",
    "\u02d7\3\2\2\2\u02da\u02d2\3\2\2\2\u02da\u02db\3\2\2\2\u02db\u02dd\3",
    "\2\2\2\u02dc\u02de\7B\2\2\u02dd\u02dc\3\2\2\2\u02dd\u02de\3\2\2\2\u02de",
    "\u02df\3\2\2\2\u02df\u02e0\7>\2\2\u02e0u\3\2\2\2\u02e1\u02e2\7g\2\2",
    "\u02e2\u02e3\7\36\2\2\u02e3\u02e4\7f\2\2\u02e4\u02e5\5x=\2\u02e5w\3",
    "\2\2\2\u02e6\u02ea\7=\2\2\u02e7\u02e9\5z>\2\u02e8\u02e7\3\2\2\2\u02e9",
    "\u02ec\3\2\2\2\u02ea\u02e8\3\2\2\2\u02ea\u02eb\3\2\2\2\u02eb\u02ed\3",
    "\2\2\2\u02ec\u02ea\3\2\2\2\u02ed\u02ee\7>\2\2\u02eey\3\2\2\2\u02ef\u02f1",
    "\5\n\6\2\u02f0\u02ef\3\2\2\2\u02f1\u02f4\3\2\2\2\u02f2\u02f0\3\2\2\2",
    "\u02f2\u02f3\3\2\2\2\u02f3\u02f5\3\2\2\2\u02f4\u02f2\3\2\2\2\u02f5\u02f8",
    "\5|?\2\u02f6\u02f8\7A\2\2\u02f7\u02f2\3\2\2\2\u02f7\u02f6\3\2\2\2\u02f8",
    "{\3\2\2\2\u02f9\u02fa\5N(\2\u02fa\u02fb\5~@\2\u02fb\u02fc\7A\2\2\u02fc",
    "\u030e\3\2\2\2\u02fd\u02ff\5\20\t\2\u02fe\u0300\7A\2\2\u02ff\u02fe\3",
    "\2\2\2\u02ff\u0300\3\2\2\2\u0300\u030e\3\2\2\2\u0301\u0303\5 \21\2\u0302",
    "\u0304\7A\2\2\u0303\u0302\3\2\2\2\u0303\u0304\3\2\2\2\u0304\u030e\3",
    "\2\2\2\u0305\u0307\5\30\r\2\u0306\u0308\7A\2\2\u0307\u0306\3\2\2\2\u0307",
    "\u0308\3\2\2\2\u0308\u030e\3\2\2\2\u0309\u030b\5v<\2\u030a\u030c\7A",
    "\2\2\u030b\u030a\3\2\2\2\u030b\u030c\3\2\2\2\u030c\u030e\3\2\2\2\u030d",
    "\u02f9\3\2\2\2\u030d\u02fd\3\2\2\2\u030d\u0301\3\2\2\2\u030d\u0305\3",
    "\2\2\2\u030d\u0309\3\2\2\2\u030e}\3\2\2\2\u030f\u0312\5\u0080A\2\u0310",
    "\u0312\5\u0082B\2\u0311\u030f\3\2\2\2\u0311\u0310\3\2\2\2\u0312\177",
    "\3\2\2\2\u0313\u0314\7f\2\2\u0314\u0315\7;\2\2\u0315\u0317\7<\2\2\u0316",
    "\u0318\5\u0084C\2\u0317\u0316\3\2\2\2\u0317\u0318\3\2\2\2\u0318\u0081",
    "\3\2\2\2\u0319\u031a\5B\"\2\u031a\u0083\3\2\2\2\u031b\u031c\7\16\2\2",
    "\u031c\u031d\5r:\2\u031d\u0085\3\2\2\2\u031e\u0322\7=\2\2\u031f\u0321",
    "\5\u0088E\2\u0320\u031f\3\2\2\2\u0321\u0324\3\2\2\2\u0322\u0320\3\2",
    "\2\2\u0322\u0323\3\2\2\2\u0323\u0325\3\2\2\2\u0324\u0322\3\2\2\2\u0325",
    "\u0326\7>\2\2\u0326\u0087\3\2\2\2\u0327\u032b\5\u008aF\2\u0328\u032b",
    "\5\u008eH\2\u0329\u032b\5\b\5\2\u032a\u0327\3\2\2\2\u032a\u0328\3\2",
    "\2\2\u032a\u0329\3\2\2\2\u032b\u0089\3\2\2\2\u032c\u032d\5\u008cG\2",
    "\u032d\u032e\7A\2\2\u032e\u008b\3\2\2\2\u032f\u0331\5\16\b\2\u0330\u032f",
    "\3\2\2\2\u0331\u0334\3\2\2\2\u0332\u0330\3\2\2\2\u0332\u0333\3\2\2\2",
    "\u0333\u0335\3\2\2\2\u0334\u0332\3\2\2\2\u0335\u0336\5N(\2\u0336\u0337",
    "\5B\"\2\u0337\u008d\3\2\2\2\u0338\u03a1\5\u0086D\2\u0339\u033a\7\4\2",
    "\2\u033a\u033d\5\u00b0Y\2\u033b\u033c\7J\2\2\u033c\u033e\5\u00b0Y\2",
    "\u033d\u033b\3\2\2\2\u033d\u033e\3\2\2\2\u033e\u033f\3\2\2\2\u033f\u0340",
    "\7A\2\2\u0340\u03a1\3\2\2\2\u0341\u0342\7\30\2\2\u0342\u0343\5\u00a8",
    "U\2\u0343\u0346\5\u008eH\2\u0344\u0345\7\21\2\2\u0345\u0347\5\u008e",
    "H\2\u0346\u0344\3\2\2\2\u0346\u0347\3\2\2\2\u0347\u03a1\3\2\2\2\u0348",
    "\u0349\7\27\2\2\u0349\u034a\7;\2\2\u034a\u034b\5\u00a0Q\2\u034b\u034c",
    "\7<\2\2\u034c\u034d\5\u008eH\2\u034d\u03a1\3\2\2\2\u034e\u034f\7\64",
    "\2\2\u034f\u0350\5\u00a8U\2\u0350\u0351\5\u008eH\2\u0351\u03a1\3\2\2",
    "\2\u0352\u0353\7\17\2\2\u0353\u0354\5\u008eH\2\u0354\u0355\7\64\2\2",
    "\u0355\u0356\5\u00a8U\2\u0356\u0357\7A\2\2\u0357\u03a1\3\2\2\2\u0358",
    "\u0359\7\61\2\2\u0359\u0363\5\u0086D\2\u035a\u035c\5\u0090I\2\u035b",
    "\u035a\3\2\2\2\u035c\u035d\3\2\2\2\u035d\u035b\3\2\2\2\u035d\u035e\3",
    "\2\2\2\u035e\u0360\3\2\2\2\u035f\u0361\5\u0094K\2\u0360\u035f\3\2\2",
    "\2\u0360\u0361\3\2\2\2\u0361\u0364\3\2\2\2\u0362\u0364\5\u0094K\2\u0363",
    "\u035b\3\2\2\2\u0363\u0362\3\2\2\2\u0364\u03a1\3\2\2\2\u0365\u0366\7",
    "\61\2\2\u0366\u0367\5\u0096L\2\u0367\u036b\5\u0086D\2\u0368\u036a\5",
    "\u0090I\2\u0369\u0368\3\2\2\2\u036a\u036d\3\2\2\2\u036b\u0369\3\2\2",
    "\2\u036b\u036c\3\2\2\2\u036c\u036f\3\2\2\2\u036d\u036b\3\2\2\2\u036e",
    "\u0370\5\u0094K\2\u036f\u036e\3\2\2\2\u036f\u0370\3\2\2\2\u0370\u03a1",
    "\3\2\2\2\u0371\u0372\7+\2\2\u0372\u0373\5\u00a8U\2\u0373\u0377\7=\2",
    "\2\u0374\u0376\5\u009cO\2\u0375\u0374\3\2\2\2\u0376\u0379\3\2\2\2\u0377",
    "\u0375\3\2\2\2\u0377\u0378\3\2\2\2\u0378\u037d\3\2\2\2\u0379\u0377\3",
    "\2\2\2\u037a\u037c\5\u009eP\2\u037b\u037a\3\2\2\2\u037c\u037f\3\2\2",
    "\2\u037d\u037b\3\2\2\2\u037d\u037e\3\2\2\2\u037e\u0380\3\2\2\2\u037f",
    "\u037d\3\2\2\2\u0380\u0381\7>\2\2\u0381\u03a1\3\2\2\2\u0382\u0383\7",
    ",\2\2\u0383\u0384\5\u00a8U\2\u0384\u0385\5\u0086D\2\u0385\u03a1\3\2",
    "\2\2\u0386\u0388\7&\2\2\u0387\u0389\5\u00b0Y\2\u0388\u0387\3\2\2\2\u0388",
    "\u0389\3\2\2\2\u0389\u038a\3\2\2\2\u038a\u03a1\7A\2\2\u038b\u038c\7",
    ".\2\2\u038c\u038d\5\u00b0Y\2\u038d\u038e\7A\2\2\u038e\u03a1\3\2\2\2",
    "\u038f\u0391\7\6\2\2\u0390\u0392\7f\2\2\u0391\u0390\3\2\2\2\u0391\u0392",
    "\3\2\2\2\u0392\u0393\3\2\2\2\u0393\u03a1\7A\2\2\u0394\u0396\7\r\2\2",
    "\u0395\u0397\7f\2\2\u0396\u0395\3\2\2\2\u0396\u0397\3\2\2\2\u0397\u0398",
    "\3\2\2\2\u0398\u03a1\7A\2\2\u0399\u03a1\7A\2\2\u039a\u039b\5\u00acW",
    "\2\u039b\u039c\7A\2\2\u039c\u03a1\3\2\2\2\u039d\u039e\7f\2\2\u039e\u039f",
    "\7J\2\2\u039f\u03a1\5\u008eH\2\u03a0\u0338\3\2\2\2\u03a0\u0339\3\2\2",
    "\2\u03a0\u0341\3\2\2\2\u03a0\u0348\3\2\2\2\u03a0\u034e\3\2\2\2\u03a0",
    "\u0352\3\2\2\2\u03a0\u0358\3\2\2\2\u03a0\u0365\3\2\2\2\u03a0\u0371\3",
    "\2\2\2\u03a0\u0382\3\2\2\2\u03a0\u0386\3\2\2\2\u03a0\u038b\3\2\2\2\u03a0",
    "\u038f\3\2\2\2\u03a0\u0394\3\2\2\2\u03a0\u0399\3\2\2\2\u03a0\u039a\3",
    "\2\2\2\u03a0\u039d\3\2\2\2\u03a1\u008f\3\2\2\2\u03a2\u03a3\7\t\2\2\u03a3",
    "\u03a7\7;\2\2\u03a4\u03a6\5\16\b\2\u03a5\u03a4\3\2\2\2\u03a6\u03a9\3",
    "\2\2\2\u03a7\u03a5\3\2\2\2\u03a7\u03a8\3\2\2\2\u03a8\u03aa\3\2\2\2\u03a9",
    "\u03a7\3\2\2\2\u03aa\u03ab\5\u0092J\2\u03ab\u03ac\7f\2\2\u03ac\u03ad",
    "\7<\2\2\u03ad\u03ae\5\u0086D\2\u03ae\u0091\3\2\2\2\u03af\u03b4\5f\64",
    "\2\u03b0\u03b1\7X\2\2\u03b1\u03b3\5f\64\2\u03b2\u03b0\3\2\2\2\u03b3",
    "\u03b6\3\2\2\2\u03b4\u03b2\3\2\2\2\u03b4\u03b5\3\2\2\2\u03b5\u0093\3",
    "\2\2\2\u03b6\u03b4\3\2\2\2\u03b7\u03b8\7\25\2\2\u03b8\u03b9\5\u0086",
    "D\2\u03b9\u0095\3\2\2\2\u03ba\u03bb\7;\2\2\u03bb\u03bd\5\u0098M\2\u03bc",
    "\u03be\7A\2\2\u03bd\u03bc\3\2\2\2\u03bd\u03be\3\2\2\2\u03be\u03bf\3",
    "\2\2\2\u03bf\u03c0\7<\2\2\u03c0\u0097\3\2\2\2\u03c1\u03c6\5\u009aN\2",
    "\u03c2\u03c3\7A\2\2\u03c3\u03c5\5\u009aN\2\u03c4\u03c2\3\2\2\2\u03c5",
    "\u03c8\3\2\2\2\u03c6\u03c4\3\2\2\2\u03c6\u03c7\3\2\2\2\u03c7\u0099\3",
    "\2\2\2\u03c8\u03c6\3\2\2\2\u03c9\u03cb\5\16\b\2\u03ca\u03c9\3\2\2\2",
    "\u03cb\u03ce\3\2\2\2\u03cc\u03ca\3\2\2\2\u03cc\u03cd\3\2\2\2\u03cd\u03cf",
    "\3\2\2\2\u03ce\u03cc\3\2\2\2\u03cf\u03d0\5P)\2\u03d0\u03d1\5F$\2\u03d1",
    "\u03d2\7D\2\2\u03d2\u03d3\5\u00b0Y\2\u03d3\u009b\3\2\2\2\u03d4\u03d6",
    "\5\u009eP\2\u03d5\u03d4\3\2\2\2\u03d6\u03d7\3\2\2\2\u03d7\u03d5\3\2",
    "\2\2\u03d7\u03d8\3\2\2\2\u03d8\u03da\3\2\2\2\u03d9\u03db\5\u0088E\2",
    "\u03da\u03d9\3\2\2\2\u03db\u03dc\3\2\2\2\u03dc\u03da\3\2\2\2\u03dc\u03dd",
    "\3\2\2\2\u03dd\u009d\3\2\2\2\u03de\u03df\7\b\2\2\u03df\u03e0\5\u00ae",
    "X\2\u03e0\u03e1\7J\2\2\u03e1\u03e9\3\2\2\2\u03e2\u03e3\7\b\2\2\u03e3",
    "\u03e4\5L\'\2\u03e4\u03e5\7J\2\2\u03e5\u03e9\3\2\2\2\u03e6\u03e7\7\16",
    "\2\2\u03e7\u03e9\7J\2\2\u03e8\u03de\3\2\2\2\u03e8\u03e2\3\2\2\2\u03e8",
    "\u03e6\3\2\2\2\u03e9\u009f\3\2\2\2\u03ea\u03f7\5\u00a4S\2\u03eb\u03ed",
    "\5\u00a2R\2\u03ec\u03eb\3\2\2\2\u03ec\u03ed\3\2\2\2\u03ed\u03ee\3\2",
    "\2\2\u03ee\u03f0\7A\2\2\u03ef\u03f1\5\u00b0Y\2\u03f0\u03ef\3\2\2\2\u03f0",
    "\u03f1\3\2\2\2\u03f1\u03f2\3\2\2\2\u03f2\u03f4\7A\2\2\u03f3\u03f5\5",
    "\u00a6T\2\u03f4\u03f3\3\2\2\2\u03f4\u03f5\3\2\2\2\u03f5\u03f7\3\2\2",
    "\2\u03f6\u03ea\3\2\2\2\u03f6\u03ec\3\2\2\2\u03f7\u00a1\3\2\2\2\u03f8",
    "\u03fb\5\u008cG\2\u03f9\u03fb\5\u00aaV\2\u03fa\u03f8\3\2\2\2\u03fa\u03f9",
    "\3\2\2\2\u03fb\u00a3\3\2\2\2\u03fc\u03fe\5\16\b\2\u03fd\u03fc\3\2\2",
    "\2\u03fe\u0401\3\2\2\2\u03ff\u03fd\3\2\2\2\u03ff\u0400\3\2\2\2\u0400",
    "\u0402\3\2\2\2\u0401\u03ff\3\2\2\2\u0402\u0403\5N(\2\u0403\u0404\5F",
    "$\2\u0404\u0405\7J\2\2\u0405\u0406\5\u00b0Y\2\u0406\u00a5\3\2\2\2\u0407",
    "\u0408\5\u00aaV\2\u0408\u00a7\3\2\2\2\u0409\u040a\7;\2\2\u040a\u040b",
    "\5\u00b0Y\2\u040b\u040c\7<\2\2\u040c\u00a9\3\2\2\2\u040d\u0412\5\u00b0",
    "Y\2\u040e\u040f\7B\2\2\u040f\u0411\5\u00b0Y\2\u0410\u040e\3\2\2\2\u0411",
    "\u0414\3\2\2\2\u0412\u0410\3\2\2\2\u0412\u0413\3\2\2\2\u0413\u00ab\3",
    "\2\2\2\u0414\u0412\3\2\2\2\u0415\u0416\5\u00b0Y\2\u0416\u00ad\3\2\2",
    "\2\u0417\u0418\5\u00b0Y\2\u0418\u00af\3\2\2\2\u0419\u041a\bY\1\2\u041a",
    "\u041b\7;\2\2\u041b\u041c\5N(\2\u041c\u041d\7<\2\2\u041d\u041e\5\u00b0",
    "Y\23\u041e\u0427\3\2\2\2\u041f\u0420\t\7\2\2\u0420\u0427\5\u00b0Y\21",
    "\u0421\u0422\t\b\2\2\u0422\u0427\5\u00b0Y\20\u0423\u0427\5\u00b2Z\2",
    "\u0424\u0425\7!\2\2\u0425\u0427\5\u00b4[\2\u0426\u0419\3\2\2\2\u0426",
    "\u041f\3\2\2\2\u0426\u0421\3\2\2\2\u0426\u0423\3\2\2\2\u0426\u0424\3",
    "\2\2\2\u0427\u047d\3\2\2\2\u0428\u0429\f\17\2\2\u0429\u042a\t\t\2\2",
    "\u042a\u047c\5\u00b0Y\20\u042b\u042c\f\16\2\2\u042c\u042d\t\n\2\2\u042d",
    "\u047c\5\u00b0Y\17\u042e\u0436\f\r\2\2\u042f\u0430\7F\2\2\u0430\u0437",
    "\7F\2\2\u0431\u0432\7E\2\2\u0432\u0433\7E\2\2\u0433\u0437\7E\2\2\u0434",
    "\u0435\7E\2\2\u0435\u0437\7E\2\2\u0436\u042f\3\2\2\2\u0436\u0431\3\2",
    "\2\2\u0436\u0434\3\2\2\2\u0437\u0438\3\2\2\2\u0438\u047c\5\u00b0Y\16",
    "\u0439\u043a\f\f\2\2\u043a\u043b\t\13\2\2\u043b\u047c\5\u00b0Y\r\u043c",
    "\u043d\f\n\2\2\u043d\u043e\t\f\2\2\u043e\u047c\5\u00b0Y\13\u043f\u0440",
    "\f\t\2\2\u0440\u0441\7W\2\2\u0441\u047c\5\u00b0Y\n\u0442\u0443\f\b\2",
    "\2\u0443\u0444\7Y\2\2\u0444\u047c\5\u00b0Y\t\u0445\u0446\f\7\2\2\u0446",
    "\u0447\7X\2\2\u0447\u047c\5\u00b0Y\b\u0448\u0449\f\6\2\2\u0449\u044a",
    "\7O\2\2\u044a\u047c\5\u00b0Y\7\u044b\u044c\f\5\2\2\u044c\u044d\7P\2",
    "\2\u044d\u047c\5\u00b0Y\6\u044e\u044f\f\4\2\2\u044f\u0450\7I\2\2\u0450",
    "\u0451\5\u00b0Y\2\u0451\u0452\7J\2\2\u0452\u0453\5\u00b0Y\5\u0453\u047c",
    "\3\2\2\2\u0454\u0455\f\3\2\2\u0455\u0456\t\r\2\2\u0456\u047c\5\u00b0",
    "Y\3\u0457\u0458\f\33\2\2\u0458\u0459\7C\2\2\u0459\u047c\7f\2\2\u045a",
    "\u045b\f\32\2\2\u045b\u045c\7C\2\2\u045c\u047c\7-\2\2\u045d\u045e\f",
    "\31\2\2\u045e\u045f\7C\2\2\u045f\u0461\7!\2\2\u0460\u0462\5\u00c0a\2",
    "\u0461\u0460\3\2\2\2\u0461\u0462\3\2\2\2\u0462\u0463\3\2\2\2\u0463\u047c",
    "\5\u00b8]\2\u0464\u0465\f\30\2\2\u0465\u0466\7C\2\2\u0466\u0467\7*\2",
    "\2\u0467\u047c\5\u00c6d\2\u0468\u0469\f\27\2\2\u0469\u046a\7C\2\2\u046a",
    "\u047c\5\u00be`\2\u046b\u046c\f\26\2\2\u046c\u046d\7?\2\2\u046d\u046e",
    "\5\u00b0Y\2\u046e\u046f\7@\2\2\u046f\u047c\3\2\2\2\u0470\u0471\f\25",
    "\2\2\u0471\u0473\7;\2\2\u0472\u0474\5\u00aaV\2\u0473\u0472\3\2\2\2\u0473",
    "\u0474\3\2\2\2\u0474\u0475\3\2\2\2\u0475\u047c\7<\2\2\u0476\u0477\f",
    "\22\2\2\u0477\u047c\t\16\2\2\u0478\u0479\f\13\2\2\u0479\u047a\7\34\2",
    "\2\u047a\u047c\5N(\2\u047b\u0428\3\2\2\2\u047b\u042b\3\2\2\2\u047b\u042e",
    "\3\2\2\2\u047b\u0439\3\2\2\2\u047b\u043c\3\2\2\2\u047b\u043f\3\2\2\2",
    "\u047b\u0442\3\2\2\2\u047b\u0445\3\2\2\2\u047b\u0448\3\2\2\2\u047b\u044b",
    "\3\2\2\2\u047b\u044e\3\2\2\2\u047b\u0454\3\2\2\2\u047b\u0457\3\2\2\2",
    "\u047b\u045a\3\2\2\2\u047b\u045d\3\2\2\2\u047b\u0464\3\2\2\2\u047b\u0468",
    "\3\2\2\2\u047b\u046b\3\2\2\2\u047b\u0470\3\2\2\2\u047b\u0476\3\2\2\2",
    "\u047b\u0478\3\2\2\2\u047c\u047f\3\2\2\2\u047d\u047b\3\2\2\2\u047d\u047e",
    "\3\2\2\2\u047e\u00b1\3\2\2\2\u047f\u047d\3\2\2\2\u0480\u0481\7;\2\2",
    "\u0481\u0482\5\u00b0Y\2\u0482\u0483\7<\2\2\u0483\u0496\3\2\2\2\u0484",
    "\u0496\7-\2\2\u0485\u0496\7*\2\2\u0486\u0496\5h\65\2\u0487\u0496\7f",
    "\2\2\u0488\u0489\5N(\2\u0489\u048a\7C\2\2\u048a\u048b\7\13\2\2\u048b",
    "\u0496\3\2\2\2\u048c\u048d\7\62\2\2\u048d\u048e\7C\2\2\u048e\u0496\7",
    "\13\2\2\u048f\u0493\5\u00c0a\2\u0490\u0494\5\u00c8e\2\u0491\u0492\7",
    "-\2\2\u0492\u0494\5\u00caf\2\u0493\u0490\3\2\2\2\u0493\u0491\3\2\2\2",
    "\u0494\u0496\3\2\2\2\u0495\u0480\3\2\2\2\u0495\u0484\3\2\2\2\u0495\u0485",
    "\3\2\2\2\u0495\u0486\3\2\2\2\u0495\u0487\3\2\2\2\u0495\u0488\3\2\2\2",
    "\u0495\u048c\3\2\2\2\u0495\u048f\3\2\2\2\u0496\u00b3\3\2\2\2\u0497\u0498",
    "\5\u00c0a\2\u0498\u0499\5\u00b6\\\2\u0499\u049a\5\u00bc_\2\u049a\u04a1",
    "\3\2\2\2\u049b\u049e\5\u00b6\\\2\u049c\u049f\5\u00ba^\2\u049d\u049f",
    "\5\u00bc_\2\u049e\u049c\3\2\2\2\u049e\u049d\3\2\2\2\u049f\u04a1\3\2",
    "\2\2\u04a0\u0497\3\2\2\2\u04a0\u049b\3\2\2\2\u04a1\u00b5\3\2\2\2\u04a2",
    "\u04a4\7f\2\2\u04a3\u04a5\5\u00c2b\2\u04a4\u04a3\3\2\2\2\u04a4\u04a5",
    "\3\2\2\2\u04a5\u04ad\3\2\2\2\u04a6\u04a7\7C\2\2\u04a7\u04a9\7f\2\2\u04a8",
    "\u04aa\5\u00c2b\2\u04a9\u04a8\3\2\2\2\u04a9\u04aa\3\2\2\2\u04aa\u04ac",
    "\3\2\2\2\u04ab\u04a6\3\2\2\2\u04ac\u04af\3\2\2\2\u04ad\u04ab\3\2\2\2",
    "\u04ad\u04ae\3\2\2\2\u04ae\u04b2\3\2\2\2\u04af\u04ad\3\2\2\2\u04b0\u04b2",
    "\5R*\2\u04b1\u04a2\3\2\2\2\u04b1\u04b0\3\2\2\2\u04b2\u00b7\3\2\2\2\u04b3",
    "\u04b5\7f\2\2\u04b4\u04b6\5\u00c4c\2\u04b5\u04b4\3\2\2\2\u04b5\u04b6",
    "\3\2\2\2\u04b6\u04b7\3\2\2\2\u04b7\u04b8\5\u00bc_\2\u04b8\u00b9\3\2",
    "\2\2\u04b9\u04d5\7?\2\2\u04ba\u04bf\7@\2\2\u04bb\u04bc\7?\2\2\u04bc",
    "\u04be\7@\2\2\u04bd\u04bb\3\2\2\2\u04be\u04c1\3\2\2\2\u04bf\u04bd\3",
    "\2\2\2\u04bf\u04c0\3\2\2\2\u04c0\u04c2\3\2\2\2\u04c1\u04bf\3\2\2\2\u04c2",
    "\u04d6\5J&\2\u04c3\u04c4\5\u00b0Y\2\u04c4\u04cb\7@\2\2\u04c5\u04c6\7",
    "?\2\2\u04c6\u04c7\5\u00b0Y\2\u04c7\u04c8\7@\2\2\u04c8\u04ca\3\2\2\2",
    "\u04c9\u04c5\3\2\2\2\u04ca\u04cd\3\2\2\2\u04cb\u04c9\3\2\2\2\u04cb\u04cc",
    "\3\2\2\2\u04cc\u04d2\3\2\2\2\u04cd\u04cb\3\2\2\2\u04ce\u04cf\7?\2\2",
    "\u04cf\u04d1\7@\2\2\u04d0\u04ce\3\2\2\2\u04d1\u04d4\3\2\2\2\u04d2\u04d0",
    "\3\2\2\2\u04d2\u04d3\3\2\2\2\u04d3\u04d6\3\2\2\2\u04d4\u04d2\3\2\2\2",
    "\u04d5\u04ba\3\2\2\2\u04d5\u04c3\3\2\2\2\u04d6\u00bb\3\2\2\2\u04d7\u04d9",
    "\5\u00caf\2\u04d8\u04da\5$\23\2\u04d9\u04d8\3\2\2\2\u04d9\u04da\3\2",
    "\2\2\u04da\u00bd\3\2\2\2\u04db\u04dc\5\u00c0a\2\u04dc\u04dd\5\u00c8",
    "e\2\u04dd\u00bf\3\2\2\2\u04de\u04df\7F\2\2\u04df\u04e0\5\"\22\2\u04e0",
    "\u04e1\7E\2\2\u04e1\u00c1\3\2\2\2\u04e2\u04e3\7F\2\2\u04e3\u04e6\7E",
    "\2\2\u04e4\u04e6\5T+\2\u04e5\u04e2\3\2\2\2\u04e5\u04e4\3\2\2\2\u04e6",
    "\u00c3\3\2\2\2\u04e7\u04e8\7F\2\2\u04e8\u04eb\7E\2\2\u04e9\u04eb\5\u00c0",
    "a\2\u04ea\u04e7\3\2\2\2\u04ea\u04e9\3\2\2\2\u04eb\u00c5\3\2\2\2\u04ec",
    "\u04f3\5\u00caf\2\u04ed\u04ee\7C\2\2\u04ee\u04f0\7f\2\2\u04ef\u04f1",
    "\5\u00caf\2\u04f0\u04ef\3\2\2\2\u04f0\u04f1\3\2\2\2\u04f1\u04f3\3\2",
    "\2\2\u04f2\u04ec\3\2\2\2\u04f2\u04ed\3\2\2\2\u04f3\u00c7\3\2\2\2\u04f4",
    "\u04f5\7*\2\2\u04f5\u04f9\5\u00c6d\2\u04f6\u04f7\7f\2\2\u04f7\u04f9",
    "\5\u00caf\2\u04f8\u04f4\3\2\2\2\u04f8\u04f6\3\2\2\2\u04f9\u00c9\3\2",
    "\2\2\u04fa\u04fc\7;\2\2\u04fb\u04fd\5\u00aaV\2\u04fc\u04fb\3\2\2\2\u04fc",
    "\u04fd\3\2\2\2\u04fd\u04fe\3\2\2\2\u04fe\u04ff\7<\2\2\u04ff\u00cb\3",
    "\2\2\2\u0097\u00cd\u00d2\u00d8\u00e0\u00e9\u00ee\u00f5\u00fc\u0103\u010a",
    "\u010f\u0113\u0117\u011b\u0120\u0124\u0128\u0132\u013a\u0141\u0148\u014c",
    "\u014f\u0152\u015b\u0161\u0166\u0169\u016f\u0175\u0179\u0182\u0189\u0192",
    "\u0199\u019f\u01a3\u01ae\u01b2\u01ba\u01bf\u01c3\u01cc\u01da\u01df\u01e8",
    "\u01f0\u01fa\u0202\u020a\u020f\u021b\u0221\u0228\u022d\u0235\u0239\u023b",
    "\u0246\u024e\u0251\u0255\u025a\u025e\u0269\u0272\u0274\u027b\u0280\u0289",
    "\u028e\u0291\u0296\u029f\u02af\u02b9\u02bc\u02c5\u02cf\u02d7\u02da\u02dd",
    "\u02ea\u02f2\u02f7\u02ff\u0303\u0307\u030b\u030d\u0311\u0317\u0322\u032a",
    "\u0332\u033d\u0346\u035d\u0360\u0363\u036b\u036f\u0377\u037d\u0388\u0391",
    "\u0396\u03a0\u03a7\u03b4\u03bd\u03c6\u03cc\u03d7\u03dc\u03e8\u03ec\u03f0",
    "\u03f4\u03f6\u03fa\u03ff\u0412\u0426\u0436\u0461\u0473\u047b\u047d\u0493",
    "\u0495\u049e\u04a0\u04a4\u04a9\u04ad\u04b1\u04b5\u04bf\u04cb\u04d2\u04d5",
    "\u04d9\u04e5\u04ea\u04f0\u04f2\u04f8\u04fc"].join("");


var atn = new antlr4.atn.ATNDeserializer().deserialize(serializedATN);

var decisionsToDFA = atn.decisionToState.map( function(ds, index) { return new antlr4.dfa.DFA(ds, index); });

var sharedContextCache = new antlr4.PredictionContextCache();

var literalNames = [ 'null', "'abstract'", "'assert'", "'boolean'", "'break'", 
                     "'byte'", "'case'", "'catch'", "'char'", "'class'", 
                     "'const'", "'continue'", "'default'", "'do'", "'double'", 
                     "'else'", "'enum'", "'extends'", "'final'", "'finally'", 
                     "'float'", "'for'", "'if'", "'goto'", "'implements'", 
                     "'import'", "'instanceof'", "'int'", "'interface'", 
                     "'long'", "'native'", "'new'", "'package'", "'private'", 
                     "'protected'", "'public'", "'return'", "'short'", "'static'", 
                     "'strictfp'", "'super'", "'switch'", "'synchronized'", 
                     "'this'", "'throw'", "'throws'", "'transient'", "'try'", 
                     "'void'", "'volatile'", "'while'", 'null', 'null', 
                     'null', 'null', 'null', "'null'", "'('", "')'", "'{'", 
                     "'}'", "'['", "']'", "';'", "','", "'.'", "'='", "'>'", 
                     "'<'", "'!'", "'~'", "'?'", "':'", "'=='", "'<='", 
                     "'>='", "'!='", "'&&'", "'||'", "'++'", "'--'", "'+'", 
                     "'-'", "'*'", "'/'", "'&'", "'|'", "'^'", "'%'", "'+='", 
                     "'-='", "'*='", "'/='", "'&='", "'|='", "'^='", "'%='", 
                     "'<<='", "'>>='", "'>>>='", 'null', "'@'", "'...'" ];

var symbolicNames = [ 'null', "ABSTRACT", "ASSERT", "BOOLEAN", "BREAK", 
                      "BYTE", "CASE", "CATCH", "CHAR", "CLASS", "CONST", 
                      "CONTINUE", "DEFAULT", "DO", "DOUBLE", "ELSE", "ENUM", 
                      "EXTENDS", "FINAL", "FINALLY", "FLOAT", "FOR", "IF", 
                      "GOTO", "IMPLEMENTS", "IMPORT", "INSTANCEOF", "INT", 
                      "INTERFACE", "LONG", "NATIVE", "NEW", "PACKAGE", "PRIVATE", 
                      "PROTECTED", "PUBLIC", "RETURN", "SHORT", "STATIC", 
                      "STRICTFP", "SUPER", "SWITCH", "SYNCHRONIZED", "THIS", 
                      "THROW", "THROWS", "TRANSIENT", "TRY", "VOID", "VOLATILE", 
                      "WHILE", "IntegerLiteral", "FloatingPointLiteral", 
                      "BooleanLiteral", "CharacterLiteral", "StringLiteral", 
                      "NullLiteral", "LPAREN", "RPAREN", "LBRACE", "RBRACE", 
                      "LBRACK", "RBRACK", "SEMI", "COMMA", "DOT", "ASSIGN", 
                      "GT", "LT", "BANG", "TILDE", "QUESTION", "COLON", 
                      "EQUAL", "LE", "GE", "NOTEQUAL", "AND", "OR", "INC", 
                      "DEC", "ADD", "SUB", "MUL", "DIV", "BITAND", "BITOR", 
                      "CARET", "MOD", "ADD_ASSIGN", "SUB_ASSIGN", "MUL_ASSIGN", 
                      "DIV_ASSIGN", "AND_ASSIGN", "OR_ASSIGN", "XOR_ASSIGN", 
                      "MOD_ASSIGN", "LSHIFT_ASSIGN", "RSHIFT_ASSIGN", "URSHIFT_ASSIGN", 
                      "Identifier", "AT", "ELLIPSIS", "WS", "COMMENT", "LINE_COMMENT" ];

var ruleNames =  [ "compilationUnit", "packageDeclaration", "importDeclaration", 
                   "typeDeclaration", "modifier", "classOrInterfaceModifier", 
                   "variableModifier", "classDeclaration", "typeParameters", 
                   "typeParameter", "typeBound", "enumDeclaration", "enumConstants", 
                   "enumConstant", "enumBodyDeclarations", "interfaceDeclaration", 
                   "typeList", "classBody", "interfaceBody", "classBodyDeclaration", 
                   "memberDeclaration", "methodDeclaration", "genericMethodDeclaration", 
                   "constructorDeclaration", "genericConstructorDeclaration", 
                   "fieldDeclaration", "interfaceBodyDeclaration", "interfaceMemberDeclaration", 
                   "constDeclaration", "constantDeclarator", "interfaceMethodDeclaration", 
                   "genericInterfaceMethodDeclaration", "variableDeclarators", 
                   "variableDeclarator", "variableDeclaratorId", "variableInitializer", 
                   "arrayInitializer", "enumConstantName", "type", "classOrInterfaceType", 
                   "primitiveType", "typeArguments", "typeArgument", "qualifiedNameList", 
                   "formalParameters", "formalParameterList", "formalParameter", 
                   "lastFormalParameter", "methodBody", "constructorBody", 
                   "qualifiedName", "literal", "annotation", "annotationName", 
                   "elementValuePairs", "elementValuePair", "elementValue", 
                   "elementValueArrayInitializer", "annotationTypeDeclaration", 
                   "annotationTypeBody", "annotationTypeElementDeclaration", 
                   "annotationTypeElementRest", "annotationMethodOrConstantRest", 
                   "annotationMethodRest", "annotationConstantRest", "defaultValue", 
                   "block", "blockStatement", "localVariableDeclarationStatement", 
                   "localVariableDeclaration", "statement", "catchClause", 
                   "catchType", "finallyBlock", "resourceSpecification", 
                   "resources", "resource", "switchBlockStatementGroup", 
                   "switchLabel", "forControl", "forInit", "enhancedForControl", 
                   "forUpdate", "parExpression", "expressionList", "statementExpression", 
                   "constantExpression", "expression", "primary", "creator", 
                   "createdName", "innerCreator", "arrayCreatorRest", "classCreatorRest", 
                   "explicitGenericInvocation", "nonWildcardTypeArguments", 
                   "typeArgumentsOrDiamond", "nonWildcardTypeArgumentsOrDiamond", 
                   "superSuffix", "explicitGenericInvocationSuffix", "arguments" ];

function JavaParser (input) {
	antlr4.Parser.call(this, input);
    this._interp = new antlr4.atn.ParserATNSimulator(this, atn, decisionsToDFA, sharedContextCache);
    this.ruleNames = ruleNames;
    this.literalNames = literalNames;
    this.symbolicNames = symbolicNames;
    return this;
}

JavaParser.prototype = Object.create(antlr4.Parser.prototype);
JavaParser.prototype.constructor = JavaParser;

Object.defineProperty(JavaParser.prototype, "atn", {
	get : function() {
		return atn;
	}
});

JavaParser.EOF = antlr4.Token.EOF;
JavaParser.ABSTRACT = 1;
JavaParser.ASSERT = 2;
JavaParser.BOOLEAN = 3;
JavaParser.BREAK = 4;
JavaParser.BYTE = 5;
JavaParser.CASE = 6;
JavaParser.CATCH = 7;
JavaParser.CHAR = 8;
JavaParser.CLASS = 9;
JavaParser.CONST = 10;
JavaParser.CONTINUE = 11;
JavaParser.DEFAULT = 12;
JavaParser.DO = 13;
JavaParser.DOUBLE = 14;
JavaParser.ELSE = 15;
JavaParser.ENUM = 16;
JavaParser.EXTENDS = 17;
JavaParser.FINAL = 18;
JavaParser.FINALLY = 19;
JavaParser.FLOAT = 20;
JavaParser.FOR = 21;
JavaParser.IF = 22;
JavaParser.GOTO = 23;
JavaParser.IMPLEMENTS = 24;
JavaParser.IMPORT = 25;
JavaParser.INSTANCEOF = 26;
JavaParser.INT = 27;
JavaParser.INTERFACE = 28;
JavaParser.LONG = 29;
JavaParser.NATIVE = 30;
JavaParser.NEW = 31;
JavaParser.PACKAGE = 32;
JavaParser.PRIVATE = 33;
JavaParser.PROTECTED = 34;
JavaParser.PUBLIC = 35;
JavaParser.RETURN = 36;
JavaParser.SHORT = 37;
JavaParser.STATIC = 38;
JavaParser.STRICTFP = 39;
JavaParser.SUPER = 40;
JavaParser.SWITCH = 41;
JavaParser.SYNCHRONIZED = 42;
JavaParser.THIS = 43;
JavaParser.THROW = 44;
JavaParser.THROWS = 45;
JavaParser.TRANSIENT = 46;
JavaParser.TRY = 47;
JavaParser.VOID = 48;
JavaParser.VOLATILE = 49;
JavaParser.WHILE = 50;
JavaParser.IntegerLiteral = 51;
JavaParser.FloatingPointLiteral = 52;
JavaParser.BooleanLiteral = 53;
JavaParser.CharacterLiteral = 54;
JavaParser.StringLiteral = 55;
JavaParser.NullLiteral = 56;
JavaParser.LPAREN = 57;
JavaParser.RPAREN = 58;
JavaParser.LBRACE = 59;
JavaParser.RBRACE = 60;
JavaParser.LBRACK = 61;
JavaParser.RBRACK = 62;
JavaParser.SEMI = 63;
JavaParser.COMMA = 64;
JavaParser.DOT = 65;
JavaParser.ASSIGN = 66;
JavaParser.GT = 67;
JavaParser.LT = 68;
JavaParser.BANG = 69;
JavaParser.TILDE = 70;
JavaParser.QUESTION = 71;
JavaParser.COLON = 72;
JavaParser.EQUAL = 73;
JavaParser.LE = 74;
JavaParser.GE = 75;
JavaParser.NOTEQUAL = 76;
JavaParser.AND = 77;
JavaParser.OR = 78;
JavaParser.INC = 79;
JavaParser.DEC = 80;
JavaParser.ADD = 81;
JavaParser.SUB = 82;
JavaParser.MUL = 83;
JavaParser.DIV = 84;
JavaParser.BITAND = 85;
JavaParser.BITOR = 86;
JavaParser.CARET = 87;
JavaParser.MOD = 88;
JavaParser.ADD_ASSIGN = 89;
JavaParser.SUB_ASSIGN = 90;
JavaParser.MUL_ASSIGN = 91;
JavaParser.DIV_ASSIGN = 92;
JavaParser.AND_ASSIGN = 93;
JavaParser.OR_ASSIGN = 94;
JavaParser.XOR_ASSIGN = 95;
JavaParser.MOD_ASSIGN = 96;
JavaParser.LSHIFT_ASSIGN = 97;
JavaParser.RSHIFT_ASSIGN = 98;
JavaParser.URSHIFT_ASSIGN = 99;
JavaParser.Identifier = 100;
JavaParser.AT = 101;
JavaParser.ELLIPSIS = 102;
JavaParser.WS = 103;
JavaParser.COMMENT = 104;
JavaParser.LINE_COMMENT = 105;

JavaParser.RULE_compilationUnit = 0;
JavaParser.RULE_packageDeclaration = 1;
JavaParser.RULE_importDeclaration = 2;
JavaParser.RULE_typeDeclaration = 3;
JavaParser.RULE_modifier = 4;
JavaParser.RULE_classOrInterfaceModifier = 5;
JavaParser.RULE_variableModifier = 6;
JavaParser.RULE_classDeclaration = 7;
JavaParser.RULE_typeParameters = 8;
JavaParser.RULE_typeParameter = 9;
JavaParser.RULE_typeBound = 10;
JavaParser.RULE_enumDeclaration = 11;
JavaParser.RULE_enumConstants = 12;
JavaParser.RULE_enumConstant = 13;
JavaParser.RULE_enumBodyDeclarations = 14;
JavaParser.RULE_interfaceDeclaration = 15;
JavaParser.RULE_typeList = 16;
JavaParser.RULE_classBody = 17;
JavaParser.RULE_interfaceBody = 18;
JavaParser.RULE_classBodyDeclaration = 19;
JavaParser.RULE_memberDeclaration = 20;
JavaParser.RULE_methodDeclaration = 21;
JavaParser.RULE_genericMethodDeclaration = 22;
JavaParser.RULE_constructorDeclaration = 23;
JavaParser.RULE_genericConstructorDeclaration = 24;
JavaParser.RULE_fieldDeclaration = 25;
JavaParser.RULE_interfaceBodyDeclaration = 26;
JavaParser.RULE_interfaceMemberDeclaration = 27;
JavaParser.RULE_constDeclaration = 28;
JavaParser.RULE_constantDeclarator = 29;
JavaParser.RULE_interfaceMethodDeclaration = 30;
JavaParser.RULE_genericInterfaceMethodDeclaration = 31;
JavaParser.RULE_variableDeclarators = 32;
JavaParser.RULE_variableDeclarator = 33;
JavaParser.RULE_variableDeclaratorId = 34;
JavaParser.RULE_variableInitializer = 35;
JavaParser.RULE_arrayInitializer = 36;
JavaParser.RULE_enumConstantName = 37;
JavaParser.RULE_type = 38;
JavaParser.RULE_classOrInterfaceType = 39;
JavaParser.RULE_primitiveType = 40;
JavaParser.RULE_typeArguments = 41;
JavaParser.RULE_typeArgument = 42;
JavaParser.RULE_qualifiedNameList = 43;
JavaParser.RULE_formalParameters = 44;
JavaParser.RULE_formalParameterList = 45;
JavaParser.RULE_formalParameter = 46;
JavaParser.RULE_lastFormalParameter = 47;
JavaParser.RULE_methodBody = 48;
JavaParser.RULE_constructorBody = 49;
JavaParser.RULE_qualifiedName = 50;
JavaParser.RULE_literal = 51;
JavaParser.RULE_annotation = 52;
JavaParser.RULE_annotationName = 53;
JavaParser.RULE_elementValuePairs = 54;
JavaParser.RULE_elementValuePair = 55;
JavaParser.RULE_elementValue = 56;
JavaParser.RULE_elementValueArrayInitializer = 57;
JavaParser.RULE_annotationTypeDeclaration = 58;
JavaParser.RULE_annotationTypeBody = 59;
JavaParser.RULE_annotationTypeElementDeclaration = 60;
JavaParser.RULE_annotationTypeElementRest = 61;
JavaParser.RULE_annotationMethodOrConstantRest = 62;
JavaParser.RULE_annotationMethodRest = 63;
JavaParser.RULE_annotationConstantRest = 64;
JavaParser.RULE_defaultValue = 65;
JavaParser.RULE_block = 66;
JavaParser.RULE_blockStatement = 67;
JavaParser.RULE_localVariableDeclarationStatement = 68;
JavaParser.RULE_localVariableDeclaration = 69;
JavaParser.RULE_statement = 70;
JavaParser.RULE_catchClause = 71;
JavaParser.RULE_catchType = 72;
JavaParser.RULE_finallyBlock = 73;
JavaParser.RULE_resourceSpecification = 74;
JavaParser.RULE_resources = 75;
JavaParser.RULE_resource = 76;
JavaParser.RULE_switchBlockStatementGroup = 77;
JavaParser.RULE_switchLabel = 78;
JavaParser.RULE_forControl = 79;
JavaParser.RULE_forInit = 80;
JavaParser.RULE_enhancedForControl = 81;
JavaParser.RULE_forUpdate = 82;
JavaParser.RULE_parExpression = 83;
JavaParser.RULE_expressionList = 84;
JavaParser.RULE_statementExpression = 85;
JavaParser.RULE_constantExpression = 86;
JavaParser.RULE_expression = 87;
JavaParser.RULE_primary = 88;
JavaParser.RULE_creator = 89;
JavaParser.RULE_createdName = 90;
JavaParser.RULE_innerCreator = 91;
JavaParser.RULE_arrayCreatorRest = 92;
JavaParser.RULE_classCreatorRest = 93;
JavaParser.RULE_explicitGenericInvocation = 94;
JavaParser.RULE_nonWildcardTypeArguments = 95;
JavaParser.RULE_typeArgumentsOrDiamond = 96;
JavaParser.RULE_nonWildcardTypeArgumentsOrDiamond = 97;
JavaParser.RULE_superSuffix = 98;
JavaParser.RULE_explicitGenericInvocationSuffix = 99;
JavaParser.RULE_arguments = 100;

function CompilationUnitContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_compilationUnit;
    return this;
}

CompilationUnitContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
CompilationUnitContext.prototype.constructor = CompilationUnitContext;

CompilationUnitContext.prototype.EOF = function() {
    return this.getToken(JavaParser.EOF, 0);
};

CompilationUnitContext.prototype.packageDeclaration = function() {
    return this.getTypedRuleContext(PackageDeclarationContext,0);
};

CompilationUnitContext.prototype.importDeclaration = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(ImportDeclarationContext);
    } else {
        return this.getTypedRuleContext(ImportDeclarationContext,i);
    }
};

CompilationUnitContext.prototype.typeDeclaration = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(TypeDeclarationContext);
    } else {
        return this.getTypedRuleContext(TypeDeclarationContext,i);
    }
};

CompilationUnitContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterCompilationUnit(this);
	}
};

CompilationUnitContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitCompilationUnit(this);
	}
};




JavaParser.CompilationUnitContext = CompilationUnitContext;

JavaParser.prototype.compilationUnit = function() {

    var localctx = new CompilationUnitContext(this, this._ctx, this.state);
    this.enterRule(localctx, 0, JavaParser.RULE_compilationUnit);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 203;
        var la_ = this._interp.adaptivePredict(this._input,0,this._ctx);
        if(la_===1) {
            this.state = 202;
            this.packageDeclaration();

        }
        this.state = 208;
        this._errHandler.sync(this);
        _la = this._input.LA(1);
        while(_la===JavaParser.IMPORT) {
            this.state = 205;
            this.importDeclaration();
            this.state = 210;
            this._errHandler.sync(this);
            _la = this._input.LA(1);
        }
        this.state = 214;
        this._errHandler.sync(this);
        _la = this._input.LA(1);
        while((((_la) & ~0x1f) == 0 && ((1 << _la) & ((1 << JavaParser.ABSTRACT) | (1 << JavaParser.CLASS) | (1 << JavaParser.ENUM) | (1 << JavaParser.FINAL) | (1 << JavaParser.INTERFACE))) !== 0) || ((((_la - 33)) & ~0x1f) == 0 && ((1 << (_la - 33)) & ((1 << (JavaParser.PRIVATE - 33)) | (1 << (JavaParser.PROTECTED - 33)) | (1 << (JavaParser.PUBLIC - 33)) | (1 << (JavaParser.STATIC - 33)) | (1 << (JavaParser.STRICTFP - 33)) | (1 << (JavaParser.SEMI - 33)))) !== 0) || _la===JavaParser.AT) {
            this.state = 211;
            this.typeDeclaration();
            this.state = 216;
            this._errHandler.sync(this);
            _la = this._input.LA(1);
        }
        this.state = 217;
        this.match(JavaParser.EOF);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function PackageDeclarationContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_packageDeclaration;
    return this;
}

PackageDeclarationContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
PackageDeclarationContext.prototype.constructor = PackageDeclarationContext;

PackageDeclarationContext.prototype.qualifiedName = function() {
    return this.getTypedRuleContext(QualifiedNameContext,0);
};

PackageDeclarationContext.prototype.annotation = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(AnnotationContext);
    } else {
        return this.getTypedRuleContext(AnnotationContext,i);
    }
};

PackageDeclarationContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterPackageDeclaration(this);
	}
};

PackageDeclarationContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitPackageDeclaration(this);
	}
};




JavaParser.PackageDeclarationContext = PackageDeclarationContext;

JavaParser.prototype.packageDeclaration = function() {

    var localctx = new PackageDeclarationContext(this, this._ctx, this.state);
    this.enterRule(localctx, 2, JavaParser.RULE_packageDeclaration);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 222;
        this._errHandler.sync(this);
        _la = this._input.LA(1);
        while(_la===JavaParser.AT) {
            this.state = 219;
            this.annotation();
            this.state = 224;
            this._errHandler.sync(this);
            _la = this._input.LA(1);
        }
        this.state = 225;
        this.match(JavaParser.PACKAGE);
        this.state = 226;
        this.qualifiedName();
        this.state = 227;
        this.match(JavaParser.SEMI);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function ImportDeclarationContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_importDeclaration;
    return this;
}

ImportDeclarationContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
ImportDeclarationContext.prototype.constructor = ImportDeclarationContext;

ImportDeclarationContext.prototype.qualifiedName = function() {
    return this.getTypedRuleContext(QualifiedNameContext,0);
};

ImportDeclarationContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterImportDeclaration(this);
	}
};

ImportDeclarationContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitImportDeclaration(this);
	}
};




JavaParser.ImportDeclarationContext = ImportDeclarationContext;

JavaParser.prototype.importDeclaration = function() {

    var localctx = new ImportDeclarationContext(this, this._ctx, this.state);
    this.enterRule(localctx, 4, JavaParser.RULE_importDeclaration);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 229;
        this.match(JavaParser.IMPORT);
        this.state = 231;
        _la = this._input.LA(1);
        if(_la===JavaParser.STATIC) {
            this.state = 230;
            this.match(JavaParser.STATIC);
        }

        this.state = 233;
        this.qualifiedName();
        this.state = 236;
        _la = this._input.LA(1);
        if(_la===JavaParser.DOT) {
            this.state = 234;
            this.match(JavaParser.DOT);
            this.state = 235;
            this.match(JavaParser.MUL);
        }

        this.state = 238;
        this.match(JavaParser.SEMI);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function TypeDeclarationContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_typeDeclaration;
    return this;
}

TypeDeclarationContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
TypeDeclarationContext.prototype.constructor = TypeDeclarationContext;

TypeDeclarationContext.prototype.classDeclaration = function() {
    return this.getTypedRuleContext(ClassDeclarationContext,0);
};

TypeDeclarationContext.prototype.classOrInterfaceModifier = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(ClassOrInterfaceModifierContext);
    } else {
        return this.getTypedRuleContext(ClassOrInterfaceModifierContext,i);
    }
};

TypeDeclarationContext.prototype.enumDeclaration = function() {
    return this.getTypedRuleContext(EnumDeclarationContext,0);
};

TypeDeclarationContext.prototype.interfaceDeclaration = function() {
    return this.getTypedRuleContext(InterfaceDeclarationContext,0);
};

TypeDeclarationContext.prototype.annotationTypeDeclaration = function() {
    return this.getTypedRuleContext(AnnotationTypeDeclarationContext,0);
};

TypeDeclarationContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterTypeDeclaration(this);
	}
};

TypeDeclarationContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitTypeDeclaration(this);
	}
};




JavaParser.TypeDeclarationContext = TypeDeclarationContext;

JavaParser.prototype.typeDeclaration = function() {

    var localctx = new TypeDeclarationContext(this, this._ctx, this.state);
    this.enterRule(localctx, 6, JavaParser.RULE_typeDeclaration);
    var _la = 0; // Token type
    try {
        this.state = 269;
        var la_ = this._interp.adaptivePredict(this._input,10,this._ctx);
        switch(la_) {
        case 1:
            this.enterOuterAlt(localctx, 1);
            this.state = 243;
            this._errHandler.sync(this);
            _la = this._input.LA(1);
            while(_la===JavaParser.ABSTRACT || _la===JavaParser.FINAL || ((((_la - 33)) & ~0x1f) == 0 && ((1 << (_la - 33)) & ((1 << (JavaParser.PRIVATE - 33)) | (1 << (JavaParser.PROTECTED - 33)) | (1 << (JavaParser.PUBLIC - 33)) | (1 << (JavaParser.STATIC - 33)) | (1 << (JavaParser.STRICTFP - 33)))) !== 0) || _la===JavaParser.AT) {
                this.state = 240;
                this.classOrInterfaceModifier();
                this.state = 245;
                this._errHandler.sync(this);
                _la = this._input.LA(1);
            }
            this.state = 246;
            this.classDeclaration();
            break;

        case 2:
            this.enterOuterAlt(localctx, 2);
            this.state = 250;
            this._errHandler.sync(this);
            _la = this._input.LA(1);
            while(_la===JavaParser.ABSTRACT || _la===JavaParser.FINAL || ((((_la - 33)) & ~0x1f) == 0 && ((1 << (_la - 33)) & ((1 << (JavaParser.PRIVATE - 33)) | (1 << (JavaParser.PROTECTED - 33)) | (1 << (JavaParser.PUBLIC - 33)) | (1 << (JavaParser.STATIC - 33)) | (1 << (JavaParser.STRICTFP - 33)))) !== 0) || _la===JavaParser.AT) {
                this.state = 247;
                this.classOrInterfaceModifier();
                this.state = 252;
                this._errHandler.sync(this);
                _la = this._input.LA(1);
            }
            this.state = 253;
            this.enumDeclaration();
            break;

        case 3:
            this.enterOuterAlt(localctx, 3);
            this.state = 257;
            this._errHandler.sync(this);
            _la = this._input.LA(1);
            while(_la===JavaParser.ABSTRACT || _la===JavaParser.FINAL || ((((_la - 33)) & ~0x1f) == 0 && ((1 << (_la - 33)) & ((1 << (JavaParser.PRIVATE - 33)) | (1 << (JavaParser.PROTECTED - 33)) | (1 << (JavaParser.PUBLIC - 33)) | (1 << (JavaParser.STATIC - 33)) | (1 << (JavaParser.STRICTFP - 33)))) !== 0) || _la===JavaParser.AT) {
                this.state = 254;
                this.classOrInterfaceModifier();
                this.state = 259;
                this._errHandler.sync(this);
                _la = this._input.LA(1);
            }
            this.state = 260;
            this.interfaceDeclaration();
            break;

        case 4:
            this.enterOuterAlt(localctx, 4);
            this.state = 264;
            this._errHandler.sync(this);
            var _alt = this._interp.adaptivePredict(this._input,9,this._ctx)
            while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
                if(_alt===1) {
                    this.state = 261;
                    this.classOrInterfaceModifier(); 
                }
                this.state = 266;
                this._errHandler.sync(this);
                _alt = this._interp.adaptivePredict(this._input,9,this._ctx);
            }

            this.state = 267;
            this.annotationTypeDeclaration();
            break;

        case 5:
            this.enterOuterAlt(localctx, 5);
            this.state = 268;
            this.match(JavaParser.SEMI);
            break;

        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function ModifierContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_modifier;
    return this;
}

ModifierContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
ModifierContext.prototype.constructor = ModifierContext;

ModifierContext.prototype.classOrInterfaceModifier = function() {
    return this.getTypedRuleContext(ClassOrInterfaceModifierContext,0);
};

ModifierContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterModifier(this);
	}
};

ModifierContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitModifier(this);
	}
};




JavaParser.ModifierContext = ModifierContext;

JavaParser.prototype.modifier = function() {

    var localctx = new ModifierContext(this, this._ctx, this.state);
    this.enterRule(localctx, 8, JavaParser.RULE_modifier);
    var _la = 0; // Token type
    try {
        this.state = 273;
        switch(this._input.LA(1)) {
        case JavaParser.ABSTRACT:
        case JavaParser.FINAL:
        case JavaParser.PRIVATE:
        case JavaParser.PROTECTED:
        case JavaParser.PUBLIC:
        case JavaParser.STATIC:
        case JavaParser.STRICTFP:
        case JavaParser.AT:
            this.enterOuterAlt(localctx, 1);
            this.state = 271;
            this.classOrInterfaceModifier();
            break;
        case JavaParser.NATIVE:
        case JavaParser.SYNCHRONIZED:
        case JavaParser.TRANSIENT:
        case JavaParser.VOLATILE:
            this.enterOuterAlt(localctx, 2);
            this.state = 272;
            _la = this._input.LA(1);
            if(!(((((_la - 30)) & ~0x1f) == 0 && ((1 << (_la - 30)) & ((1 << (JavaParser.NATIVE - 30)) | (1 << (JavaParser.SYNCHRONIZED - 30)) | (1 << (JavaParser.TRANSIENT - 30)) | (1 << (JavaParser.VOLATILE - 30)))) !== 0))) {
            this._errHandler.recoverInline(this);
            }
            else {
                this.consume();
            }
            break;
        default:
            throw new antlr4.error.NoViableAltException(this);
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function ClassOrInterfaceModifierContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_classOrInterfaceModifier;
    return this;
}

ClassOrInterfaceModifierContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
ClassOrInterfaceModifierContext.prototype.constructor = ClassOrInterfaceModifierContext;

ClassOrInterfaceModifierContext.prototype.annotation = function() {
    return this.getTypedRuleContext(AnnotationContext,0);
};

ClassOrInterfaceModifierContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterClassOrInterfaceModifier(this);
	}
};

ClassOrInterfaceModifierContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitClassOrInterfaceModifier(this);
	}
};




JavaParser.ClassOrInterfaceModifierContext = ClassOrInterfaceModifierContext;

JavaParser.prototype.classOrInterfaceModifier = function() {

    var localctx = new ClassOrInterfaceModifierContext(this, this._ctx, this.state);
    this.enterRule(localctx, 10, JavaParser.RULE_classOrInterfaceModifier);
    var _la = 0; // Token type
    try {
        this.state = 277;
        switch(this._input.LA(1)) {
        case JavaParser.AT:
            this.enterOuterAlt(localctx, 1);
            this.state = 275;
            this.annotation();
            break;
        case JavaParser.ABSTRACT:
        case JavaParser.FINAL:
        case JavaParser.PRIVATE:
        case JavaParser.PROTECTED:
        case JavaParser.PUBLIC:
        case JavaParser.STATIC:
        case JavaParser.STRICTFP:
            this.enterOuterAlt(localctx, 2);
            this.state = 276;
            _la = this._input.LA(1);
            if(!(_la===JavaParser.ABSTRACT || _la===JavaParser.FINAL || ((((_la - 33)) & ~0x1f) == 0 && ((1 << (_la - 33)) & ((1 << (JavaParser.PRIVATE - 33)) | (1 << (JavaParser.PROTECTED - 33)) | (1 << (JavaParser.PUBLIC - 33)) | (1 << (JavaParser.STATIC - 33)) | (1 << (JavaParser.STRICTFP - 33)))) !== 0))) {
            this._errHandler.recoverInline(this);
            }
            else {
                this.consume();
            }
            break;
        default:
            throw new antlr4.error.NoViableAltException(this);
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function VariableModifierContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_variableModifier;
    return this;
}

VariableModifierContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
VariableModifierContext.prototype.constructor = VariableModifierContext;

VariableModifierContext.prototype.annotation = function() {
    return this.getTypedRuleContext(AnnotationContext,0);
};

VariableModifierContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterVariableModifier(this);
	}
};

VariableModifierContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitVariableModifier(this);
	}
};




JavaParser.VariableModifierContext = VariableModifierContext;

JavaParser.prototype.variableModifier = function() {

    var localctx = new VariableModifierContext(this, this._ctx, this.state);
    this.enterRule(localctx, 12, JavaParser.RULE_variableModifier);
    try {
        this.state = 281;
        switch(this._input.LA(1)) {
        case JavaParser.FINAL:
            this.enterOuterAlt(localctx, 1);
            this.state = 279;
            this.match(JavaParser.FINAL);
            break;
        case JavaParser.AT:
            this.enterOuterAlt(localctx, 2);
            this.state = 280;
            this.annotation();
            break;
        default:
            throw new antlr4.error.NoViableAltException(this);
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function ClassDeclarationContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_classDeclaration;
    return this;
}

ClassDeclarationContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
ClassDeclarationContext.prototype.constructor = ClassDeclarationContext;

ClassDeclarationContext.prototype.Identifier = function() {
    return this.getToken(JavaParser.Identifier, 0);
};

ClassDeclarationContext.prototype.classBody = function() {
    return this.getTypedRuleContext(ClassBodyContext,0);
};

ClassDeclarationContext.prototype.typeParameters = function() {
    return this.getTypedRuleContext(TypeParametersContext,0);
};

ClassDeclarationContext.prototype.type = function() {
    return this.getTypedRuleContext(TypeContext,0);
};

ClassDeclarationContext.prototype.typeList = function() {
    return this.getTypedRuleContext(TypeListContext,0);
};

ClassDeclarationContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterClassDeclaration(this);
	}
};

ClassDeclarationContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitClassDeclaration(this);
	}
};




JavaParser.ClassDeclarationContext = ClassDeclarationContext;

JavaParser.prototype.classDeclaration = function() {

    var localctx = new ClassDeclarationContext(this, this._ctx, this.state);
    this.enterRule(localctx, 14, JavaParser.RULE_classDeclaration);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 283;
        this.match(JavaParser.CLASS);
        this.state = 284;
        this.match(JavaParser.Identifier);
        this.state = 286;
        _la = this._input.LA(1);
        if(_la===JavaParser.LT) {
            this.state = 285;
            this.typeParameters();
        }

        this.state = 290;
        _la = this._input.LA(1);
        if(_la===JavaParser.EXTENDS) {
            this.state = 288;
            this.match(JavaParser.EXTENDS);
            this.state = 289;
            this.type();
        }

        this.state = 294;
        _la = this._input.LA(1);
        if(_la===JavaParser.IMPLEMENTS) {
            this.state = 292;
            this.match(JavaParser.IMPLEMENTS);
            this.state = 293;
            this.typeList();
        }

        this.state = 296;
        this.classBody();
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function TypeParametersContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_typeParameters;
    return this;
}

TypeParametersContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
TypeParametersContext.prototype.constructor = TypeParametersContext;

TypeParametersContext.prototype.typeParameter = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(TypeParameterContext);
    } else {
        return this.getTypedRuleContext(TypeParameterContext,i);
    }
};

TypeParametersContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterTypeParameters(this);
	}
};

TypeParametersContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitTypeParameters(this);
	}
};




JavaParser.TypeParametersContext = TypeParametersContext;

JavaParser.prototype.typeParameters = function() {

    var localctx = new TypeParametersContext(this, this._ctx, this.state);
    this.enterRule(localctx, 16, JavaParser.RULE_typeParameters);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 298;
        this.match(JavaParser.LT);
        this.state = 299;
        this.typeParameter();
        this.state = 304;
        this._errHandler.sync(this);
        _la = this._input.LA(1);
        while(_la===JavaParser.COMMA) {
            this.state = 300;
            this.match(JavaParser.COMMA);
            this.state = 301;
            this.typeParameter();
            this.state = 306;
            this._errHandler.sync(this);
            _la = this._input.LA(1);
        }
        this.state = 307;
        this.match(JavaParser.GT);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function TypeParameterContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_typeParameter;
    return this;
}

TypeParameterContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
TypeParameterContext.prototype.constructor = TypeParameterContext;

TypeParameterContext.prototype.Identifier = function() {
    return this.getToken(JavaParser.Identifier, 0);
};

TypeParameterContext.prototype.typeBound = function() {
    return this.getTypedRuleContext(TypeBoundContext,0);
};

TypeParameterContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterTypeParameter(this);
	}
};

TypeParameterContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitTypeParameter(this);
	}
};




JavaParser.TypeParameterContext = TypeParameterContext;

JavaParser.prototype.typeParameter = function() {

    var localctx = new TypeParameterContext(this, this._ctx, this.state);
    this.enterRule(localctx, 18, JavaParser.RULE_typeParameter);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 309;
        this.match(JavaParser.Identifier);
        this.state = 312;
        _la = this._input.LA(1);
        if(_la===JavaParser.EXTENDS) {
            this.state = 310;
            this.match(JavaParser.EXTENDS);
            this.state = 311;
            this.typeBound();
        }

    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function TypeBoundContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_typeBound;
    return this;
}

TypeBoundContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
TypeBoundContext.prototype.constructor = TypeBoundContext;

TypeBoundContext.prototype.type = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(TypeContext);
    } else {
        return this.getTypedRuleContext(TypeContext,i);
    }
};

TypeBoundContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterTypeBound(this);
	}
};

TypeBoundContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitTypeBound(this);
	}
};




JavaParser.TypeBoundContext = TypeBoundContext;

JavaParser.prototype.typeBound = function() {

    var localctx = new TypeBoundContext(this, this._ctx, this.state);
    this.enterRule(localctx, 20, JavaParser.RULE_typeBound);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 314;
        this.type();
        this.state = 319;
        this._errHandler.sync(this);
        _la = this._input.LA(1);
        while(_la===JavaParser.BITAND) {
            this.state = 315;
            this.match(JavaParser.BITAND);
            this.state = 316;
            this.type();
            this.state = 321;
            this._errHandler.sync(this);
            _la = this._input.LA(1);
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function EnumDeclarationContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_enumDeclaration;
    return this;
}

EnumDeclarationContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
EnumDeclarationContext.prototype.constructor = EnumDeclarationContext;

EnumDeclarationContext.prototype.ENUM = function() {
    return this.getToken(JavaParser.ENUM, 0);
};

EnumDeclarationContext.prototype.Identifier = function() {
    return this.getToken(JavaParser.Identifier, 0);
};

EnumDeclarationContext.prototype.typeList = function() {
    return this.getTypedRuleContext(TypeListContext,0);
};

EnumDeclarationContext.prototype.enumConstants = function() {
    return this.getTypedRuleContext(EnumConstantsContext,0);
};

EnumDeclarationContext.prototype.enumBodyDeclarations = function() {
    return this.getTypedRuleContext(EnumBodyDeclarationsContext,0);
};

EnumDeclarationContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterEnumDeclaration(this);
	}
};

EnumDeclarationContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitEnumDeclaration(this);
	}
};




JavaParser.EnumDeclarationContext = EnumDeclarationContext;

JavaParser.prototype.enumDeclaration = function() {

    var localctx = new EnumDeclarationContext(this, this._ctx, this.state);
    this.enterRule(localctx, 22, JavaParser.RULE_enumDeclaration);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 322;
        this.match(JavaParser.ENUM);
        this.state = 323;
        this.match(JavaParser.Identifier);
        this.state = 326;
        _la = this._input.LA(1);
        if(_la===JavaParser.IMPLEMENTS) {
            this.state = 324;
            this.match(JavaParser.IMPLEMENTS);
            this.state = 325;
            this.typeList();
        }

        this.state = 328;
        this.match(JavaParser.LBRACE);
        this.state = 330;
        _la = this._input.LA(1);
        if(_la===JavaParser.Identifier || _la===JavaParser.AT) {
            this.state = 329;
            this.enumConstants();
        }

        this.state = 333;
        _la = this._input.LA(1);
        if(_la===JavaParser.COMMA) {
            this.state = 332;
            this.match(JavaParser.COMMA);
        }

        this.state = 336;
        _la = this._input.LA(1);
        if(_la===JavaParser.SEMI) {
            this.state = 335;
            this.enumBodyDeclarations();
        }

        this.state = 338;
        this.match(JavaParser.RBRACE);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function EnumConstantsContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_enumConstants;
    return this;
}

EnumConstantsContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
EnumConstantsContext.prototype.constructor = EnumConstantsContext;

EnumConstantsContext.prototype.enumConstant = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(EnumConstantContext);
    } else {
        return this.getTypedRuleContext(EnumConstantContext,i);
    }
};

EnumConstantsContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterEnumConstants(this);
	}
};

EnumConstantsContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitEnumConstants(this);
	}
};




JavaParser.EnumConstantsContext = EnumConstantsContext;

JavaParser.prototype.enumConstants = function() {

    var localctx = new EnumConstantsContext(this, this._ctx, this.state);
    this.enterRule(localctx, 24, JavaParser.RULE_enumConstants);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 340;
        this.enumConstant();
        this.state = 345;
        this._errHandler.sync(this);
        var _alt = this._interp.adaptivePredict(this._input,24,this._ctx)
        while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
            if(_alt===1) {
                this.state = 341;
                this.match(JavaParser.COMMA);
                this.state = 342;
                this.enumConstant(); 
            }
            this.state = 347;
            this._errHandler.sync(this);
            _alt = this._interp.adaptivePredict(this._input,24,this._ctx);
        }

    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function EnumConstantContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_enumConstant;
    return this;
}

EnumConstantContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
EnumConstantContext.prototype.constructor = EnumConstantContext;

EnumConstantContext.prototype.Identifier = function() {
    return this.getToken(JavaParser.Identifier, 0);
};

EnumConstantContext.prototype.annotation = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(AnnotationContext);
    } else {
        return this.getTypedRuleContext(AnnotationContext,i);
    }
};

EnumConstantContext.prototype.arguments = function() {
    return this.getTypedRuleContext(ArgumentsContext,0);
};

EnumConstantContext.prototype.classBody = function() {
    return this.getTypedRuleContext(ClassBodyContext,0);
};

EnumConstantContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterEnumConstant(this);
	}
};

EnumConstantContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitEnumConstant(this);
	}
};




JavaParser.EnumConstantContext = EnumConstantContext;

JavaParser.prototype.enumConstant = function() {

    var localctx = new EnumConstantContext(this, this._ctx, this.state);
    this.enterRule(localctx, 26, JavaParser.RULE_enumConstant);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 351;
        this._errHandler.sync(this);
        _la = this._input.LA(1);
        while(_la===JavaParser.AT) {
            this.state = 348;
            this.annotation();
            this.state = 353;
            this._errHandler.sync(this);
            _la = this._input.LA(1);
        }
        this.state = 354;
        this.match(JavaParser.Identifier);
        this.state = 356;
        _la = this._input.LA(1);
        if(_la===JavaParser.LPAREN) {
            this.state = 355;
            this.arguments();
        }

        this.state = 359;
        _la = this._input.LA(1);
        if(_la===JavaParser.LBRACE) {
            this.state = 358;
            this.classBody();
        }

    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function EnumBodyDeclarationsContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_enumBodyDeclarations;
    return this;
}

EnumBodyDeclarationsContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
EnumBodyDeclarationsContext.prototype.constructor = EnumBodyDeclarationsContext;

EnumBodyDeclarationsContext.prototype.classBodyDeclaration = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(ClassBodyDeclarationContext);
    } else {
        return this.getTypedRuleContext(ClassBodyDeclarationContext,i);
    }
};

EnumBodyDeclarationsContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterEnumBodyDeclarations(this);
	}
};

EnumBodyDeclarationsContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitEnumBodyDeclarations(this);
	}
};




JavaParser.EnumBodyDeclarationsContext = EnumBodyDeclarationsContext;

JavaParser.prototype.enumBodyDeclarations = function() {

    var localctx = new EnumBodyDeclarationsContext(this, this._ctx, this.state);
    this.enterRule(localctx, 28, JavaParser.RULE_enumBodyDeclarations);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 361;
        this.match(JavaParser.SEMI);
        this.state = 365;
        this._errHandler.sync(this);
        _la = this._input.LA(1);
        while((((_la) & ~0x1f) == 0 && ((1 << _la) & ((1 << JavaParser.ABSTRACT) | (1 << JavaParser.BOOLEAN) | (1 << JavaParser.BYTE) | (1 << JavaParser.CHAR) | (1 << JavaParser.CLASS) | (1 << JavaParser.DOUBLE) | (1 << JavaParser.ENUM) | (1 << JavaParser.FINAL) | (1 << JavaParser.FLOAT) | (1 << JavaParser.INT) | (1 << JavaParser.INTERFACE) | (1 << JavaParser.LONG) | (1 << JavaParser.NATIVE))) !== 0) || ((((_la - 33)) & ~0x1f) == 0 && ((1 << (_la - 33)) & ((1 << (JavaParser.PRIVATE - 33)) | (1 << (JavaParser.PROTECTED - 33)) | (1 << (JavaParser.PUBLIC - 33)) | (1 << (JavaParser.SHORT - 33)) | (1 << (JavaParser.STATIC - 33)) | (1 << (JavaParser.STRICTFP - 33)) | (1 << (JavaParser.SYNCHRONIZED - 33)) | (1 << (JavaParser.TRANSIENT - 33)) | (1 << (JavaParser.VOID - 33)) | (1 << (JavaParser.VOLATILE - 33)) | (1 << (JavaParser.LBRACE - 33)) | (1 << (JavaParser.SEMI - 33)))) !== 0) || _la===JavaParser.LT || _la===JavaParser.Identifier || _la===JavaParser.AT) {
            this.state = 362;
            this.classBodyDeclaration();
            this.state = 367;
            this._errHandler.sync(this);
            _la = this._input.LA(1);
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function InterfaceDeclarationContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_interfaceDeclaration;
    return this;
}

InterfaceDeclarationContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
InterfaceDeclarationContext.prototype.constructor = InterfaceDeclarationContext;

InterfaceDeclarationContext.prototype.Identifier = function() {
    return this.getToken(JavaParser.Identifier, 0);
};

InterfaceDeclarationContext.prototype.interfaceBody = function() {
    return this.getTypedRuleContext(InterfaceBodyContext,0);
};

InterfaceDeclarationContext.prototype.typeParameters = function() {
    return this.getTypedRuleContext(TypeParametersContext,0);
};

InterfaceDeclarationContext.prototype.typeList = function() {
    return this.getTypedRuleContext(TypeListContext,0);
};

InterfaceDeclarationContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterInterfaceDeclaration(this);
	}
};

InterfaceDeclarationContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitInterfaceDeclaration(this);
	}
};




JavaParser.InterfaceDeclarationContext = InterfaceDeclarationContext;

JavaParser.prototype.interfaceDeclaration = function() {

    var localctx = new InterfaceDeclarationContext(this, this._ctx, this.state);
    this.enterRule(localctx, 30, JavaParser.RULE_interfaceDeclaration);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 368;
        this.match(JavaParser.INTERFACE);
        this.state = 369;
        this.match(JavaParser.Identifier);
        this.state = 371;
        _la = this._input.LA(1);
        if(_la===JavaParser.LT) {
            this.state = 370;
            this.typeParameters();
        }

        this.state = 375;
        _la = this._input.LA(1);
        if(_la===JavaParser.EXTENDS) {
            this.state = 373;
            this.match(JavaParser.EXTENDS);
            this.state = 374;
            this.typeList();
        }

        this.state = 377;
        this.interfaceBody();
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function TypeListContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_typeList;
    return this;
}

TypeListContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
TypeListContext.prototype.constructor = TypeListContext;

TypeListContext.prototype.type = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(TypeContext);
    } else {
        return this.getTypedRuleContext(TypeContext,i);
    }
};

TypeListContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterTypeList(this);
	}
};

TypeListContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitTypeList(this);
	}
};




JavaParser.TypeListContext = TypeListContext;

JavaParser.prototype.typeList = function() {

    var localctx = new TypeListContext(this, this._ctx, this.state);
    this.enterRule(localctx, 32, JavaParser.RULE_typeList);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 379;
        this.type();
        this.state = 384;
        this._errHandler.sync(this);
        _la = this._input.LA(1);
        while(_la===JavaParser.COMMA) {
            this.state = 380;
            this.match(JavaParser.COMMA);
            this.state = 381;
            this.type();
            this.state = 386;
            this._errHandler.sync(this);
            _la = this._input.LA(1);
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function ClassBodyContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_classBody;
    return this;
}

ClassBodyContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
ClassBodyContext.prototype.constructor = ClassBodyContext;

ClassBodyContext.prototype.classBodyDeclaration = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(ClassBodyDeclarationContext);
    } else {
        return this.getTypedRuleContext(ClassBodyDeclarationContext,i);
    }
};

ClassBodyContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterClassBody(this);
	}
};

ClassBodyContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitClassBody(this);
	}
};




JavaParser.ClassBodyContext = ClassBodyContext;

JavaParser.prototype.classBody = function() {

    var localctx = new ClassBodyContext(this, this._ctx, this.state);
    this.enterRule(localctx, 34, JavaParser.RULE_classBody);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 387;
        this.match(JavaParser.LBRACE);
        this.state = 391;
        this._errHandler.sync(this);
        _la = this._input.LA(1);
        while((((_la) & ~0x1f) == 0 && ((1 << _la) & ((1 << JavaParser.ABSTRACT) | (1 << JavaParser.BOOLEAN) | (1 << JavaParser.BYTE) | (1 << JavaParser.CHAR) | (1 << JavaParser.CLASS) | (1 << JavaParser.DOUBLE) | (1 << JavaParser.ENUM) | (1 << JavaParser.FINAL) | (1 << JavaParser.FLOAT) | (1 << JavaParser.INT) | (1 << JavaParser.INTERFACE) | (1 << JavaParser.LONG) | (1 << JavaParser.NATIVE))) !== 0) || ((((_la - 33)) & ~0x1f) == 0 && ((1 << (_la - 33)) & ((1 << (JavaParser.PRIVATE - 33)) | (1 << (JavaParser.PROTECTED - 33)) | (1 << (JavaParser.PUBLIC - 33)) | (1 << (JavaParser.SHORT - 33)) | (1 << (JavaParser.STATIC - 33)) | (1 << (JavaParser.STRICTFP - 33)) | (1 << (JavaParser.SYNCHRONIZED - 33)) | (1 << (JavaParser.TRANSIENT - 33)) | (1 << (JavaParser.VOID - 33)) | (1 << (JavaParser.VOLATILE - 33)) | (1 << (JavaParser.LBRACE - 33)) | (1 << (JavaParser.SEMI - 33)))) !== 0) || _la===JavaParser.LT || _la===JavaParser.Identifier || _la===JavaParser.AT) {
            this.state = 388;
            this.classBodyDeclaration();
            this.state = 393;
            this._errHandler.sync(this);
            _la = this._input.LA(1);
        }
        this.state = 394;
        this.match(JavaParser.RBRACE);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function InterfaceBodyContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_interfaceBody;
    return this;
}

InterfaceBodyContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
InterfaceBodyContext.prototype.constructor = InterfaceBodyContext;

InterfaceBodyContext.prototype.interfaceBodyDeclaration = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(InterfaceBodyDeclarationContext);
    } else {
        return this.getTypedRuleContext(InterfaceBodyDeclarationContext,i);
    }
};

InterfaceBodyContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterInterfaceBody(this);
	}
};

InterfaceBodyContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitInterfaceBody(this);
	}
};




JavaParser.InterfaceBodyContext = InterfaceBodyContext;

JavaParser.prototype.interfaceBody = function() {

    var localctx = new InterfaceBodyContext(this, this._ctx, this.state);
    this.enterRule(localctx, 36, JavaParser.RULE_interfaceBody);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 396;
        this.match(JavaParser.LBRACE);
        this.state = 400;
        this._errHandler.sync(this);
        _la = this._input.LA(1);
        while((((_la) & ~0x1f) == 0 && ((1 << _la) & ((1 << JavaParser.ABSTRACT) | (1 << JavaParser.BOOLEAN) | (1 << JavaParser.BYTE) | (1 << JavaParser.CHAR) | (1 << JavaParser.CLASS) | (1 << JavaParser.DOUBLE) | (1 << JavaParser.ENUM) | (1 << JavaParser.FINAL) | (1 << JavaParser.FLOAT) | (1 << JavaParser.INT) | (1 << JavaParser.INTERFACE) | (1 << JavaParser.LONG) | (1 << JavaParser.NATIVE))) !== 0) || ((((_la - 33)) & ~0x1f) == 0 && ((1 << (_la - 33)) & ((1 << (JavaParser.PRIVATE - 33)) | (1 << (JavaParser.PROTECTED - 33)) | (1 << (JavaParser.PUBLIC - 33)) | (1 << (JavaParser.SHORT - 33)) | (1 << (JavaParser.STATIC - 33)) | (1 << (JavaParser.STRICTFP - 33)) | (1 << (JavaParser.SYNCHRONIZED - 33)) | (1 << (JavaParser.TRANSIENT - 33)) | (1 << (JavaParser.VOID - 33)) | (1 << (JavaParser.VOLATILE - 33)) | (1 << (JavaParser.SEMI - 33)))) !== 0) || _la===JavaParser.LT || _la===JavaParser.Identifier || _la===JavaParser.AT) {
            this.state = 397;
            this.interfaceBodyDeclaration();
            this.state = 402;
            this._errHandler.sync(this);
            _la = this._input.LA(1);
        }
        this.state = 403;
        this.match(JavaParser.RBRACE);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function ClassBodyDeclarationContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_classBodyDeclaration;
    return this;
}

ClassBodyDeclarationContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
ClassBodyDeclarationContext.prototype.constructor = ClassBodyDeclarationContext;

ClassBodyDeclarationContext.prototype.block = function() {
    return this.getTypedRuleContext(BlockContext,0);
};

ClassBodyDeclarationContext.prototype.memberDeclaration = function() {
    return this.getTypedRuleContext(MemberDeclarationContext,0);
};

ClassBodyDeclarationContext.prototype.modifier = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(ModifierContext);
    } else {
        return this.getTypedRuleContext(ModifierContext,i);
    }
};

ClassBodyDeclarationContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterClassBodyDeclaration(this);
	}
};

ClassBodyDeclarationContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitClassBodyDeclaration(this);
	}
};




JavaParser.ClassBodyDeclarationContext = ClassBodyDeclarationContext;

JavaParser.prototype.classBodyDeclaration = function() {

    var localctx = new ClassBodyDeclarationContext(this, this._ctx, this.state);
    this.enterRule(localctx, 38, JavaParser.RULE_classBodyDeclaration);
    var _la = 0; // Token type
    try {
        this.state = 417;
        var la_ = this._interp.adaptivePredict(this._input,36,this._ctx);
        switch(la_) {
        case 1:
            this.enterOuterAlt(localctx, 1);
            this.state = 405;
            this.match(JavaParser.SEMI);
            break;

        case 2:
            this.enterOuterAlt(localctx, 2);
            this.state = 407;
            _la = this._input.LA(1);
            if(_la===JavaParser.STATIC) {
                this.state = 406;
                this.match(JavaParser.STATIC);
            }

            this.state = 409;
            this.block();
            break;

        case 3:
            this.enterOuterAlt(localctx, 3);
            this.state = 413;
            this._errHandler.sync(this);
            var _alt = this._interp.adaptivePredict(this._input,35,this._ctx)
            while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
                if(_alt===1) {
                    this.state = 410;
                    this.modifier(); 
                }
                this.state = 415;
                this._errHandler.sync(this);
                _alt = this._interp.adaptivePredict(this._input,35,this._ctx);
            }

            this.state = 416;
            this.memberDeclaration();
            break;

        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function MemberDeclarationContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_memberDeclaration;
    return this;
}

MemberDeclarationContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
MemberDeclarationContext.prototype.constructor = MemberDeclarationContext;

MemberDeclarationContext.prototype.methodDeclaration = function() {
    return this.getTypedRuleContext(MethodDeclarationContext,0);
};

MemberDeclarationContext.prototype.genericMethodDeclaration = function() {
    return this.getTypedRuleContext(GenericMethodDeclarationContext,0);
};

MemberDeclarationContext.prototype.fieldDeclaration = function() {
    return this.getTypedRuleContext(FieldDeclarationContext,0);
};

MemberDeclarationContext.prototype.constructorDeclaration = function() {
    return this.getTypedRuleContext(ConstructorDeclarationContext,0);
};

MemberDeclarationContext.prototype.genericConstructorDeclaration = function() {
    return this.getTypedRuleContext(GenericConstructorDeclarationContext,0);
};

MemberDeclarationContext.prototype.interfaceDeclaration = function() {
    return this.getTypedRuleContext(InterfaceDeclarationContext,0);
};

MemberDeclarationContext.prototype.annotationTypeDeclaration = function() {
    return this.getTypedRuleContext(AnnotationTypeDeclarationContext,0);
};

MemberDeclarationContext.prototype.classDeclaration = function() {
    return this.getTypedRuleContext(ClassDeclarationContext,0);
};

MemberDeclarationContext.prototype.enumDeclaration = function() {
    return this.getTypedRuleContext(EnumDeclarationContext,0);
};

MemberDeclarationContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterMemberDeclaration(this);
	}
};

MemberDeclarationContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitMemberDeclaration(this);
	}
};




JavaParser.MemberDeclarationContext = MemberDeclarationContext;

JavaParser.prototype.memberDeclaration = function() {

    var localctx = new MemberDeclarationContext(this, this._ctx, this.state);
    this.enterRule(localctx, 40, JavaParser.RULE_memberDeclaration);
    try {
        this.state = 428;
        var la_ = this._interp.adaptivePredict(this._input,37,this._ctx);
        switch(la_) {
        case 1:
            this.enterOuterAlt(localctx, 1);
            this.state = 419;
            this.methodDeclaration();
            break;

        case 2:
            this.enterOuterAlt(localctx, 2);
            this.state = 420;
            this.genericMethodDeclaration();
            break;

        case 3:
            this.enterOuterAlt(localctx, 3);
            this.state = 421;
            this.fieldDeclaration();
            break;

        case 4:
            this.enterOuterAlt(localctx, 4);
            this.state = 422;
            this.constructorDeclaration();
            break;

        case 5:
            this.enterOuterAlt(localctx, 5);
            this.state = 423;
            this.genericConstructorDeclaration();
            break;

        case 6:
            this.enterOuterAlt(localctx, 6);
            this.state = 424;
            this.interfaceDeclaration();
            break;

        case 7:
            this.enterOuterAlt(localctx, 7);
            this.state = 425;
            this.annotationTypeDeclaration();
            break;

        case 8:
            this.enterOuterAlt(localctx, 8);
            this.state = 426;
            this.classDeclaration();
            break;

        case 9:
            this.enterOuterAlt(localctx, 9);
            this.state = 427;
            this.enumDeclaration();
            break;

        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function MethodDeclarationContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_methodDeclaration;
    return this;
}

MethodDeclarationContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
MethodDeclarationContext.prototype.constructor = MethodDeclarationContext;

MethodDeclarationContext.prototype.Identifier = function() {
    return this.getToken(JavaParser.Identifier, 0);
};

MethodDeclarationContext.prototype.formalParameters = function() {
    return this.getTypedRuleContext(FormalParametersContext,0);
};

MethodDeclarationContext.prototype.type = function() {
    return this.getTypedRuleContext(TypeContext,0);
};

MethodDeclarationContext.prototype.methodBody = function() {
    return this.getTypedRuleContext(MethodBodyContext,0);
};

MethodDeclarationContext.prototype.qualifiedNameList = function() {
    return this.getTypedRuleContext(QualifiedNameListContext,0);
};

MethodDeclarationContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterMethodDeclaration(this);
	}
};

MethodDeclarationContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitMethodDeclaration(this);
	}
};




JavaParser.MethodDeclarationContext = MethodDeclarationContext;

JavaParser.prototype.methodDeclaration = function() {

    var localctx = new MethodDeclarationContext(this, this._ctx, this.state);
    this.enterRule(localctx, 42, JavaParser.RULE_methodDeclaration);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 432;
        switch(this._input.LA(1)) {
        case JavaParser.BOOLEAN:
        case JavaParser.BYTE:
        case JavaParser.CHAR:
        case JavaParser.DOUBLE:
        case JavaParser.FLOAT:
        case JavaParser.INT:
        case JavaParser.LONG:
        case JavaParser.SHORT:
        case JavaParser.Identifier:
            this.state = 430;
            this.type();
            break;
        case JavaParser.VOID:
            this.state = 431;
            this.match(JavaParser.VOID);
            break;
        default:
            throw new antlr4.error.NoViableAltException(this);
        }
        this.state = 434;
        this.match(JavaParser.Identifier);
        this.state = 435;
        this.formalParameters();
        this.state = 440;
        this._errHandler.sync(this);
        _la = this._input.LA(1);
        while(_la===JavaParser.LBRACK) {
            this.state = 436;
            this.match(JavaParser.LBRACK);
            this.state = 437;
            this.match(JavaParser.RBRACK);
            this.state = 442;
            this._errHandler.sync(this);
            _la = this._input.LA(1);
        }
        this.state = 445;
        _la = this._input.LA(1);
        if(_la===JavaParser.THROWS) {
            this.state = 443;
            this.match(JavaParser.THROWS);
            this.state = 444;
            this.qualifiedNameList();
        }

        this.state = 449;
        switch(this._input.LA(1)) {
        case JavaParser.LBRACE:
            this.state = 447;
            this.methodBody();
            break;
        case JavaParser.SEMI:
            this.state = 448;
            this.match(JavaParser.SEMI);
            break;
        default:
            throw new antlr4.error.NoViableAltException(this);
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function GenericMethodDeclarationContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_genericMethodDeclaration;
    return this;
}

GenericMethodDeclarationContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
GenericMethodDeclarationContext.prototype.constructor = GenericMethodDeclarationContext;

GenericMethodDeclarationContext.prototype.typeParameters = function() {
    return this.getTypedRuleContext(TypeParametersContext,0);
};

GenericMethodDeclarationContext.prototype.methodDeclaration = function() {
    return this.getTypedRuleContext(MethodDeclarationContext,0);
};

GenericMethodDeclarationContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterGenericMethodDeclaration(this);
	}
};

GenericMethodDeclarationContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitGenericMethodDeclaration(this);
	}
};




JavaParser.GenericMethodDeclarationContext = GenericMethodDeclarationContext;

JavaParser.prototype.genericMethodDeclaration = function() {

    var localctx = new GenericMethodDeclarationContext(this, this._ctx, this.state);
    this.enterRule(localctx, 44, JavaParser.RULE_genericMethodDeclaration);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 451;
        this.typeParameters();
        this.state = 452;
        this.methodDeclaration();
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function ConstructorDeclarationContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_constructorDeclaration;
    return this;
}

ConstructorDeclarationContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
ConstructorDeclarationContext.prototype.constructor = ConstructorDeclarationContext;

ConstructorDeclarationContext.prototype.Identifier = function() {
    return this.getToken(JavaParser.Identifier, 0);
};

ConstructorDeclarationContext.prototype.formalParameters = function() {
    return this.getTypedRuleContext(FormalParametersContext,0);
};

ConstructorDeclarationContext.prototype.constructorBody = function() {
    return this.getTypedRuleContext(ConstructorBodyContext,0);
};

ConstructorDeclarationContext.prototype.qualifiedNameList = function() {
    return this.getTypedRuleContext(QualifiedNameListContext,0);
};

ConstructorDeclarationContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterConstructorDeclaration(this);
	}
};

ConstructorDeclarationContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitConstructorDeclaration(this);
	}
};




JavaParser.ConstructorDeclarationContext = ConstructorDeclarationContext;

JavaParser.prototype.constructorDeclaration = function() {

    var localctx = new ConstructorDeclarationContext(this, this._ctx, this.state);
    this.enterRule(localctx, 46, JavaParser.RULE_constructorDeclaration);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 454;
        this.match(JavaParser.Identifier);
        this.state = 455;
        this.formalParameters();
        this.state = 458;
        _la = this._input.LA(1);
        if(_la===JavaParser.THROWS) {
            this.state = 456;
            this.match(JavaParser.THROWS);
            this.state = 457;
            this.qualifiedNameList();
        }

        this.state = 460;
        this.constructorBody();
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function GenericConstructorDeclarationContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_genericConstructorDeclaration;
    return this;
}

GenericConstructorDeclarationContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
GenericConstructorDeclarationContext.prototype.constructor = GenericConstructorDeclarationContext;

GenericConstructorDeclarationContext.prototype.typeParameters = function() {
    return this.getTypedRuleContext(TypeParametersContext,0);
};

GenericConstructorDeclarationContext.prototype.constructorDeclaration = function() {
    return this.getTypedRuleContext(ConstructorDeclarationContext,0);
};

GenericConstructorDeclarationContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterGenericConstructorDeclaration(this);
	}
};

GenericConstructorDeclarationContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitGenericConstructorDeclaration(this);
	}
};




JavaParser.GenericConstructorDeclarationContext = GenericConstructorDeclarationContext;

JavaParser.prototype.genericConstructorDeclaration = function() {

    var localctx = new GenericConstructorDeclarationContext(this, this._ctx, this.state);
    this.enterRule(localctx, 48, JavaParser.RULE_genericConstructorDeclaration);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 462;
        this.typeParameters();
        this.state = 463;
        this.constructorDeclaration();
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function FieldDeclarationContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_fieldDeclaration;
    return this;
}

FieldDeclarationContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
FieldDeclarationContext.prototype.constructor = FieldDeclarationContext;

FieldDeclarationContext.prototype.type = function() {
    return this.getTypedRuleContext(TypeContext,0);
};

FieldDeclarationContext.prototype.variableDeclarators = function() {
    return this.getTypedRuleContext(VariableDeclaratorsContext,0);
};

FieldDeclarationContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterFieldDeclaration(this);
	}
};

FieldDeclarationContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitFieldDeclaration(this);
	}
};




JavaParser.FieldDeclarationContext = FieldDeclarationContext;

JavaParser.prototype.fieldDeclaration = function() {

    var localctx = new FieldDeclarationContext(this, this._ctx, this.state);
    this.enterRule(localctx, 50, JavaParser.RULE_fieldDeclaration);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 465;
        this.type();
        this.state = 466;
        this.variableDeclarators();
        this.state = 467;
        this.match(JavaParser.SEMI);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function InterfaceBodyDeclarationContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_interfaceBodyDeclaration;
    return this;
}

InterfaceBodyDeclarationContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
InterfaceBodyDeclarationContext.prototype.constructor = InterfaceBodyDeclarationContext;

InterfaceBodyDeclarationContext.prototype.interfaceMemberDeclaration = function() {
    return this.getTypedRuleContext(InterfaceMemberDeclarationContext,0);
};

InterfaceBodyDeclarationContext.prototype.modifier = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(ModifierContext);
    } else {
        return this.getTypedRuleContext(ModifierContext,i);
    }
};

InterfaceBodyDeclarationContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterInterfaceBodyDeclaration(this);
	}
};

InterfaceBodyDeclarationContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitInterfaceBodyDeclaration(this);
	}
};




JavaParser.InterfaceBodyDeclarationContext = InterfaceBodyDeclarationContext;

JavaParser.prototype.interfaceBodyDeclaration = function() {

    var localctx = new InterfaceBodyDeclarationContext(this, this._ctx, this.state);
    this.enterRule(localctx, 52, JavaParser.RULE_interfaceBodyDeclaration);
    try {
        this.state = 477;
        switch(this._input.LA(1)) {
        case JavaParser.ABSTRACT:
        case JavaParser.BOOLEAN:
        case JavaParser.BYTE:
        case JavaParser.CHAR:
        case JavaParser.CLASS:
        case JavaParser.DOUBLE:
        case JavaParser.ENUM:
        case JavaParser.FINAL:
        case JavaParser.FLOAT:
        case JavaParser.INT:
        case JavaParser.INTERFACE:
        case JavaParser.LONG:
        case JavaParser.NATIVE:
        case JavaParser.PRIVATE:
        case JavaParser.PROTECTED:
        case JavaParser.PUBLIC:
        case JavaParser.SHORT:
        case JavaParser.STATIC:
        case JavaParser.STRICTFP:
        case JavaParser.SYNCHRONIZED:
        case JavaParser.TRANSIENT:
        case JavaParser.VOID:
        case JavaParser.VOLATILE:
        case JavaParser.LT:
        case JavaParser.Identifier:
        case JavaParser.AT:
            this.enterOuterAlt(localctx, 1);
            this.state = 472;
            this._errHandler.sync(this);
            var _alt = this._interp.adaptivePredict(this._input,43,this._ctx)
            while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
                if(_alt===1) {
                    this.state = 469;
                    this.modifier(); 
                }
                this.state = 474;
                this._errHandler.sync(this);
                _alt = this._interp.adaptivePredict(this._input,43,this._ctx);
            }

            this.state = 475;
            this.interfaceMemberDeclaration();
            break;
        case JavaParser.SEMI:
            this.enterOuterAlt(localctx, 2);
            this.state = 476;
            this.match(JavaParser.SEMI);
            break;
        default:
            throw new antlr4.error.NoViableAltException(this);
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function InterfaceMemberDeclarationContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_interfaceMemberDeclaration;
    return this;
}

InterfaceMemberDeclarationContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
InterfaceMemberDeclarationContext.prototype.constructor = InterfaceMemberDeclarationContext;

InterfaceMemberDeclarationContext.prototype.constDeclaration = function() {
    return this.getTypedRuleContext(ConstDeclarationContext,0);
};

InterfaceMemberDeclarationContext.prototype.interfaceMethodDeclaration = function() {
    return this.getTypedRuleContext(InterfaceMethodDeclarationContext,0);
};

InterfaceMemberDeclarationContext.prototype.genericInterfaceMethodDeclaration = function() {
    return this.getTypedRuleContext(GenericInterfaceMethodDeclarationContext,0);
};

InterfaceMemberDeclarationContext.prototype.interfaceDeclaration = function() {
    return this.getTypedRuleContext(InterfaceDeclarationContext,0);
};

InterfaceMemberDeclarationContext.prototype.annotationTypeDeclaration = function() {
    return this.getTypedRuleContext(AnnotationTypeDeclarationContext,0);
};

InterfaceMemberDeclarationContext.prototype.classDeclaration = function() {
    return this.getTypedRuleContext(ClassDeclarationContext,0);
};

InterfaceMemberDeclarationContext.prototype.enumDeclaration = function() {
    return this.getTypedRuleContext(EnumDeclarationContext,0);
};

InterfaceMemberDeclarationContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterInterfaceMemberDeclaration(this);
	}
};

InterfaceMemberDeclarationContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitInterfaceMemberDeclaration(this);
	}
};




JavaParser.InterfaceMemberDeclarationContext = InterfaceMemberDeclarationContext;

JavaParser.prototype.interfaceMemberDeclaration = function() {

    var localctx = new InterfaceMemberDeclarationContext(this, this._ctx, this.state);
    this.enterRule(localctx, 54, JavaParser.RULE_interfaceMemberDeclaration);
    try {
        this.state = 486;
        var la_ = this._interp.adaptivePredict(this._input,45,this._ctx);
        switch(la_) {
        case 1:
            this.enterOuterAlt(localctx, 1);
            this.state = 479;
            this.constDeclaration();
            break;

        case 2:
            this.enterOuterAlt(localctx, 2);
            this.state = 480;
            this.interfaceMethodDeclaration();
            break;

        case 3:
            this.enterOuterAlt(localctx, 3);
            this.state = 481;
            this.genericInterfaceMethodDeclaration();
            break;

        case 4:
            this.enterOuterAlt(localctx, 4);
            this.state = 482;
            this.interfaceDeclaration();
            break;

        case 5:
            this.enterOuterAlt(localctx, 5);
            this.state = 483;
            this.annotationTypeDeclaration();
            break;

        case 6:
            this.enterOuterAlt(localctx, 6);
            this.state = 484;
            this.classDeclaration();
            break;

        case 7:
            this.enterOuterAlt(localctx, 7);
            this.state = 485;
            this.enumDeclaration();
            break;

        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function ConstDeclarationContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_constDeclaration;
    return this;
}

ConstDeclarationContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
ConstDeclarationContext.prototype.constructor = ConstDeclarationContext;

ConstDeclarationContext.prototype.type = function() {
    return this.getTypedRuleContext(TypeContext,0);
};

ConstDeclarationContext.prototype.constantDeclarator = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(ConstantDeclaratorContext);
    } else {
        return this.getTypedRuleContext(ConstantDeclaratorContext,i);
    }
};

ConstDeclarationContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterConstDeclaration(this);
	}
};

ConstDeclarationContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitConstDeclaration(this);
	}
};




JavaParser.ConstDeclarationContext = ConstDeclarationContext;

JavaParser.prototype.constDeclaration = function() {

    var localctx = new ConstDeclarationContext(this, this._ctx, this.state);
    this.enterRule(localctx, 56, JavaParser.RULE_constDeclaration);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 488;
        this.type();
        this.state = 489;
        this.constantDeclarator();
        this.state = 494;
        this._errHandler.sync(this);
        _la = this._input.LA(1);
        while(_la===JavaParser.COMMA) {
            this.state = 490;
            this.match(JavaParser.COMMA);
            this.state = 491;
            this.constantDeclarator();
            this.state = 496;
            this._errHandler.sync(this);
            _la = this._input.LA(1);
        }
        this.state = 497;
        this.match(JavaParser.SEMI);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function ConstantDeclaratorContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_constantDeclarator;
    return this;
}

ConstantDeclaratorContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
ConstantDeclaratorContext.prototype.constructor = ConstantDeclaratorContext;

ConstantDeclaratorContext.prototype.Identifier = function() {
    return this.getToken(JavaParser.Identifier, 0);
};

ConstantDeclaratorContext.prototype.variableInitializer = function() {
    return this.getTypedRuleContext(VariableInitializerContext,0);
};

ConstantDeclaratorContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterConstantDeclarator(this);
	}
};

ConstantDeclaratorContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitConstantDeclarator(this);
	}
};




JavaParser.ConstantDeclaratorContext = ConstantDeclaratorContext;

JavaParser.prototype.constantDeclarator = function() {

    var localctx = new ConstantDeclaratorContext(this, this._ctx, this.state);
    this.enterRule(localctx, 58, JavaParser.RULE_constantDeclarator);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 499;
        this.match(JavaParser.Identifier);
        this.state = 504;
        this._errHandler.sync(this);
        _la = this._input.LA(1);
        while(_la===JavaParser.LBRACK) {
            this.state = 500;
            this.match(JavaParser.LBRACK);
            this.state = 501;
            this.match(JavaParser.RBRACK);
            this.state = 506;
            this._errHandler.sync(this);
            _la = this._input.LA(1);
        }
        this.state = 507;
        this.match(JavaParser.ASSIGN);
        this.state = 508;
        this.variableInitializer();
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function InterfaceMethodDeclarationContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_interfaceMethodDeclaration;
    return this;
}

InterfaceMethodDeclarationContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
InterfaceMethodDeclarationContext.prototype.constructor = InterfaceMethodDeclarationContext;

InterfaceMethodDeclarationContext.prototype.Identifier = function() {
    return this.getToken(JavaParser.Identifier, 0);
};

InterfaceMethodDeclarationContext.prototype.formalParameters = function() {
    return this.getTypedRuleContext(FormalParametersContext,0);
};

InterfaceMethodDeclarationContext.prototype.type = function() {
    return this.getTypedRuleContext(TypeContext,0);
};

InterfaceMethodDeclarationContext.prototype.qualifiedNameList = function() {
    return this.getTypedRuleContext(QualifiedNameListContext,0);
};

InterfaceMethodDeclarationContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterInterfaceMethodDeclaration(this);
	}
};

InterfaceMethodDeclarationContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitInterfaceMethodDeclaration(this);
	}
};




JavaParser.InterfaceMethodDeclarationContext = InterfaceMethodDeclarationContext;

JavaParser.prototype.interfaceMethodDeclaration = function() {

    var localctx = new InterfaceMethodDeclarationContext(this, this._ctx, this.state);
    this.enterRule(localctx, 60, JavaParser.RULE_interfaceMethodDeclaration);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 512;
        switch(this._input.LA(1)) {
        case JavaParser.BOOLEAN:
        case JavaParser.BYTE:
        case JavaParser.CHAR:
        case JavaParser.DOUBLE:
        case JavaParser.FLOAT:
        case JavaParser.INT:
        case JavaParser.LONG:
        case JavaParser.SHORT:
        case JavaParser.Identifier:
            this.state = 510;
            this.type();
            break;
        case JavaParser.VOID:
            this.state = 511;
            this.match(JavaParser.VOID);
            break;
        default:
            throw new antlr4.error.NoViableAltException(this);
        }
        this.state = 514;
        this.match(JavaParser.Identifier);
        this.state = 515;
        this.formalParameters();
        this.state = 520;
        this._errHandler.sync(this);
        _la = this._input.LA(1);
        while(_la===JavaParser.LBRACK) {
            this.state = 516;
            this.match(JavaParser.LBRACK);
            this.state = 517;
            this.match(JavaParser.RBRACK);
            this.state = 522;
            this._errHandler.sync(this);
            _la = this._input.LA(1);
        }
        this.state = 525;
        _la = this._input.LA(1);
        if(_la===JavaParser.THROWS) {
            this.state = 523;
            this.match(JavaParser.THROWS);
            this.state = 524;
            this.qualifiedNameList();
        }

        this.state = 527;
        this.match(JavaParser.SEMI);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function GenericInterfaceMethodDeclarationContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_genericInterfaceMethodDeclaration;
    return this;
}

GenericInterfaceMethodDeclarationContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
GenericInterfaceMethodDeclarationContext.prototype.constructor = GenericInterfaceMethodDeclarationContext;

GenericInterfaceMethodDeclarationContext.prototype.typeParameters = function() {
    return this.getTypedRuleContext(TypeParametersContext,0);
};

GenericInterfaceMethodDeclarationContext.prototype.interfaceMethodDeclaration = function() {
    return this.getTypedRuleContext(InterfaceMethodDeclarationContext,0);
};

GenericInterfaceMethodDeclarationContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterGenericInterfaceMethodDeclaration(this);
	}
};

GenericInterfaceMethodDeclarationContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitGenericInterfaceMethodDeclaration(this);
	}
};




JavaParser.GenericInterfaceMethodDeclarationContext = GenericInterfaceMethodDeclarationContext;

JavaParser.prototype.genericInterfaceMethodDeclaration = function() {

    var localctx = new GenericInterfaceMethodDeclarationContext(this, this._ctx, this.state);
    this.enterRule(localctx, 62, JavaParser.RULE_genericInterfaceMethodDeclaration);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 529;
        this.typeParameters();
        this.state = 530;
        this.interfaceMethodDeclaration();
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function VariableDeclaratorsContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_variableDeclarators;
    return this;
}

VariableDeclaratorsContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
VariableDeclaratorsContext.prototype.constructor = VariableDeclaratorsContext;

VariableDeclaratorsContext.prototype.variableDeclarator = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(VariableDeclaratorContext);
    } else {
        return this.getTypedRuleContext(VariableDeclaratorContext,i);
    }
};

VariableDeclaratorsContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterVariableDeclarators(this);
	}
};

VariableDeclaratorsContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitVariableDeclarators(this);
	}
};




JavaParser.VariableDeclaratorsContext = VariableDeclaratorsContext;

JavaParser.prototype.variableDeclarators = function() {

    var localctx = new VariableDeclaratorsContext(this, this._ctx, this.state);
    this.enterRule(localctx, 64, JavaParser.RULE_variableDeclarators);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 532;
        this.variableDeclarator();
        this.state = 537;
        this._errHandler.sync(this);
        _la = this._input.LA(1);
        while(_la===JavaParser.COMMA) {
            this.state = 533;
            this.match(JavaParser.COMMA);
            this.state = 534;
            this.variableDeclarator();
            this.state = 539;
            this._errHandler.sync(this);
            _la = this._input.LA(1);
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function VariableDeclaratorContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_variableDeclarator;
    return this;
}

VariableDeclaratorContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
VariableDeclaratorContext.prototype.constructor = VariableDeclaratorContext;

VariableDeclaratorContext.prototype.variableDeclaratorId = function() {
    return this.getTypedRuleContext(VariableDeclaratorIdContext,0);
};

VariableDeclaratorContext.prototype.variableInitializer = function() {
    return this.getTypedRuleContext(VariableInitializerContext,0);
};

VariableDeclaratorContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterVariableDeclarator(this);
	}
};

VariableDeclaratorContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitVariableDeclarator(this);
	}
};




JavaParser.VariableDeclaratorContext = VariableDeclaratorContext;

JavaParser.prototype.variableDeclarator = function() {

    var localctx = new VariableDeclaratorContext(this, this._ctx, this.state);
    this.enterRule(localctx, 66, JavaParser.RULE_variableDeclarator);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 540;
        this.variableDeclaratorId();
        this.state = 543;
        _la = this._input.LA(1);
        if(_la===JavaParser.ASSIGN) {
            this.state = 541;
            this.match(JavaParser.ASSIGN);
            this.state = 542;
            this.variableInitializer();
        }

    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function VariableDeclaratorIdContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_variableDeclaratorId;
    return this;
}

VariableDeclaratorIdContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
VariableDeclaratorIdContext.prototype.constructor = VariableDeclaratorIdContext;

VariableDeclaratorIdContext.prototype.Identifier = function() {
    return this.getToken(JavaParser.Identifier, 0);
};

VariableDeclaratorIdContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterVariableDeclaratorId(this);
	}
};

VariableDeclaratorIdContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitVariableDeclaratorId(this);
	}
};




JavaParser.VariableDeclaratorIdContext = VariableDeclaratorIdContext;

JavaParser.prototype.variableDeclaratorId = function() {

    var localctx = new VariableDeclaratorIdContext(this, this._ctx, this.state);
    this.enterRule(localctx, 68, JavaParser.RULE_variableDeclaratorId);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 545;
        this.match(JavaParser.Identifier);
        this.state = 550;
        this._errHandler.sync(this);
        _la = this._input.LA(1);
        while(_la===JavaParser.LBRACK) {
            this.state = 546;
            this.match(JavaParser.LBRACK);
            this.state = 547;
            this.match(JavaParser.RBRACK);
            this.state = 552;
            this._errHandler.sync(this);
            _la = this._input.LA(1);
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function VariableInitializerContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_variableInitializer;
    return this;
}

VariableInitializerContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
VariableInitializerContext.prototype.constructor = VariableInitializerContext;

VariableInitializerContext.prototype.arrayInitializer = function() {
    return this.getTypedRuleContext(ArrayInitializerContext,0);
};

VariableInitializerContext.prototype.expression = function() {
    return this.getTypedRuleContext(ExpressionContext,0);
};

VariableInitializerContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterVariableInitializer(this);
	}
};

VariableInitializerContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitVariableInitializer(this);
	}
};




JavaParser.VariableInitializerContext = VariableInitializerContext;

JavaParser.prototype.variableInitializer = function() {

    var localctx = new VariableInitializerContext(this, this._ctx, this.state);
    this.enterRule(localctx, 70, JavaParser.RULE_variableInitializer);
    try {
        this.state = 555;
        switch(this._input.LA(1)) {
        case JavaParser.LBRACE:
            this.enterOuterAlt(localctx, 1);
            this.state = 553;
            this.arrayInitializer();
            break;
        case JavaParser.BOOLEAN:
        case JavaParser.BYTE:
        case JavaParser.CHAR:
        case JavaParser.DOUBLE:
        case JavaParser.FLOAT:
        case JavaParser.INT:
        case JavaParser.LONG:
        case JavaParser.NEW:
        case JavaParser.SHORT:
        case JavaParser.SUPER:
        case JavaParser.THIS:
        case JavaParser.VOID:
        case JavaParser.IntegerLiteral:
        case JavaParser.FloatingPointLiteral:
        case JavaParser.BooleanLiteral:
        case JavaParser.CharacterLiteral:
        case JavaParser.StringLiteral:
        case JavaParser.NullLiteral:
        case JavaParser.LPAREN:
        case JavaParser.LT:
        case JavaParser.BANG:
        case JavaParser.TILDE:
        case JavaParser.INC:
        case JavaParser.DEC:
        case JavaParser.ADD:
        case JavaParser.SUB:
        case JavaParser.Identifier:
            this.enterOuterAlt(localctx, 2);
            this.state = 554;
            this.expression(0);
            break;
        default:
            throw new antlr4.error.NoViableAltException(this);
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function ArrayInitializerContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_arrayInitializer;
    return this;
}

ArrayInitializerContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
ArrayInitializerContext.prototype.constructor = ArrayInitializerContext;

ArrayInitializerContext.prototype.variableInitializer = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(VariableInitializerContext);
    } else {
        return this.getTypedRuleContext(VariableInitializerContext,i);
    }
};

ArrayInitializerContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterArrayInitializer(this);
	}
};

ArrayInitializerContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitArrayInitializer(this);
	}
};




JavaParser.ArrayInitializerContext = ArrayInitializerContext;

JavaParser.prototype.arrayInitializer = function() {

    var localctx = new ArrayInitializerContext(this, this._ctx, this.state);
    this.enterRule(localctx, 72, JavaParser.RULE_arrayInitializer);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 557;
        this.match(JavaParser.LBRACE);
        this.state = 569;
        _la = this._input.LA(1);
        if((((_la) & ~0x1f) == 0 && ((1 << _la) & ((1 << JavaParser.BOOLEAN) | (1 << JavaParser.BYTE) | (1 << JavaParser.CHAR) | (1 << JavaParser.DOUBLE) | (1 << JavaParser.FLOAT) | (1 << JavaParser.INT) | (1 << JavaParser.LONG) | (1 << JavaParser.NEW))) !== 0) || ((((_la - 37)) & ~0x1f) == 0 && ((1 << (_la - 37)) & ((1 << (JavaParser.SHORT - 37)) | (1 << (JavaParser.SUPER - 37)) | (1 << (JavaParser.THIS - 37)) | (1 << (JavaParser.VOID - 37)) | (1 << (JavaParser.IntegerLiteral - 37)) | (1 << (JavaParser.FloatingPointLiteral - 37)) | (1 << (JavaParser.BooleanLiteral - 37)) | (1 << (JavaParser.CharacterLiteral - 37)) | (1 << (JavaParser.StringLiteral - 37)) | (1 << (JavaParser.NullLiteral - 37)) | (1 << (JavaParser.LPAREN - 37)) | (1 << (JavaParser.LBRACE - 37)) | (1 << (JavaParser.LT - 37)))) !== 0) || ((((_la - 69)) & ~0x1f) == 0 && ((1 << (_la - 69)) & ((1 << (JavaParser.BANG - 69)) | (1 << (JavaParser.TILDE - 69)) | (1 << (JavaParser.INC - 69)) | (1 << (JavaParser.DEC - 69)) | (1 << (JavaParser.ADD - 69)) | (1 << (JavaParser.SUB - 69)) | (1 << (JavaParser.Identifier - 69)))) !== 0)) {
            this.state = 558;
            this.variableInitializer();
            this.state = 563;
            this._errHandler.sync(this);
            var _alt = this._interp.adaptivePredict(this._input,55,this._ctx)
            while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
                if(_alt===1) {
                    this.state = 559;
                    this.match(JavaParser.COMMA);
                    this.state = 560;
                    this.variableInitializer(); 
                }
                this.state = 565;
                this._errHandler.sync(this);
                _alt = this._interp.adaptivePredict(this._input,55,this._ctx);
            }

            this.state = 567;
            _la = this._input.LA(1);
            if(_la===JavaParser.COMMA) {
                this.state = 566;
                this.match(JavaParser.COMMA);
            }

        }

        this.state = 571;
        this.match(JavaParser.RBRACE);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function EnumConstantNameContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_enumConstantName;
    return this;
}

EnumConstantNameContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
EnumConstantNameContext.prototype.constructor = EnumConstantNameContext;

EnumConstantNameContext.prototype.Identifier = function() {
    return this.getToken(JavaParser.Identifier, 0);
};

EnumConstantNameContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterEnumConstantName(this);
	}
};

EnumConstantNameContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitEnumConstantName(this);
	}
};




JavaParser.EnumConstantNameContext = EnumConstantNameContext;

JavaParser.prototype.enumConstantName = function() {

    var localctx = new EnumConstantNameContext(this, this._ctx, this.state);
    this.enterRule(localctx, 74, JavaParser.RULE_enumConstantName);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 573;
        this.match(JavaParser.Identifier);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function TypeContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_type;
    return this;
}

TypeContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
TypeContext.prototype.constructor = TypeContext;

TypeContext.prototype.classOrInterfaceType = function() {
    return this.getTypedRuleContext(ClassOrInterfaceTypeContext,0);
};

TypeContext.prototype.primitiveType = function() {
    return this.getTypedRuleContext(PrimitiveTypeContext,0);
};

TypeContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterType(this);
	}
};

TypeContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitType(this);
	}
};




JavaParser.TypeContext = TypeContext;

JavaParser.prototype.type = function() {

    var localctx = new TypeContext(this, this._ctx, this.state);
    this.enterRule(localctx, 76, JavaParser.RULE_type);
    try {
        this.state = 591;
        switch(this._input.LA(1)) {
        case JavaParser.Identifier:
            this.enterOuterAlt(localctx, 1);
            this.state = 575;
            this.classOrInterfaceType();
            this.state = 580;
            this._errHandler.sync(this);
            var _alt = this._interp.adaptivePredict(this._input,58,this._ctx)
            while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
                if(_alt===1) {
                    this.state = 576;
                    this.match(JavaParser.LBRACK);
                    this.state = 577;
                    this.match(JavaParser.RBRACK); 
                }
                this.state = 582;
                this._errHandler.sync(this);
                _alt = this._interp.adaptivePredict(this._input,58,this._ctx);
            }

            break;
        case JavaParser.BOOLEAN:
        case JavaParser.BYTE:
        case JavaParser.CHAR:
        case JavaParser.DOUBLE:
        case JavaParser.FLOAT:
        case JavaParser.INT:
        case JavaParser.LONG:
        case JavaParser.SHORT:
            this.enterOuterAlt(localctx, 2);
            this.state = 583;
            this.primitiveType();
            this.state = 588;
            this._errHandler.sync(this);
            var _alt = this._interp.adaptivePredict(this._input,59,this._ctx)
            while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
                if(_alt===1) {
                    this.state = 584;
                    this.match(JavaParser.LBRACK);
                    this.state = 585;
                    this.match(JavaParser.RBRACK); 
                }
                this.state = 590;
                this._errHandler.sync(this);
                _alt = this._interp.adaptivePredict(this._input,59,this._ctx);
            }

            break;
        default:
            throw new antlr4.error.NoViableAltException(this);
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function ClassOrInterfaceTypeContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_classOrInterfaceType;
    return this;
}

ClassOrInterfaceTypeContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
ClassOrInterfaceTypeContext.prototype.constructor = ClassOrInterfaceTypeContext;

ClassOrInterfaceTypeContext.prototype.Identifier = function(i) {
	if(i===undefined) {
		i = null;
	}
    if(i===null) {
        return this.getTokens(JavaParser.Identifier);
    } else {
        return this.getToken(JavaParser.Identifier, i);
    }
};


ClassOrInterfaceTypeContext.prototype.typeArguments = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(TypeArgumentsContext);
    } else {
        return this.getTypedRuleContext(TypeArgumentsContext,i);
    }
};

ClassOrInterfaceTypeContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterClassOrInterfaceType(this);
	}
};

ClassOrInterfaceTypeContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitClassOrInterfaceType(this);
	}
};




JavaParser.ClassOrInterfaceTypeContext = ClassOrInterfaceTypeContext;

JavaParser.prototype.classOrInterfaceType = function() {

    var localctx = new ClassOrInterfaceTypeContext(this, this._ctx, this.state);
    this.enterRule(localctx, 78, JavaParser.RULE_classOrInterfaceType);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 593;
        this.match(JavaParser.Identifier);
        this.state = 595;
        var la_ = this._interp.adaptivePredict(this._input,61,this._ctx);
        if(la_===1) {
            this.state = 594;
            this.typeArguments();

        }
        this.state = 604;
        this._errHandler.sync(this);
        var _alt = this._interp.adaptivePredict(this._input,63,this._ctx)
        while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
            if(_alt===1) {
                this.state = 597;
                this.match(JavaParser.DOT);
                this.state = 598;
                this.match(JavaParser.Identifier);
                this.state = 600;
                var la_ = this._interp.adaptivePredict(this._input,62,this._ctx);
                if(la_===1) {
                    this.state = 599;
                    this.typeArguments();

                } 
            }
            this.state = 606;
            this._errHandler.sync(this);
            _alt = this._interp.adaptivePredict(this._input,63,this._ctx);
        }

    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function PrimitiveTypeContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_primitiveType;
    return this;
}

PrimitiveTypeContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
PrimitiveTypeContext.prototype.constructor = PrimitiveTypeContext;


PrimitiveTypeContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterPrimitiveType(this);
	}
};

PrimitiveTypeContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitPrimitiveType(this);
	}
};




JavaParser.PrimitiveTypeContext = PrimitiveTypeContext;

JavaParser.prototype.primitiveType = function() {

    var localctx = new PrimitiveTypeContext(this, this._ctx, this.state);
    this.enterRule(localctx, 80, JavaParser.RULE_primitiveType);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 607;
        _la = this._input.LA(1);
        if(!((((_la) & ~0x1f) == 0 && ((1 << _la) & ((1 << JavaParser.BOOLEAN) | (1 << JavaParser.BYTE) | (1 << JavaParser.CHAR) | (1 << JavaParser.DOUBLE) | (1 << JavaParser.FLOAT) | (1 << JavaParser.INT) | (1 << JavaParser.LONG))) !== 0) || _la===JavaParser.SHORT)) {
        this._errHandler.recoverInline(this);
        }
        else {
            this.consume();
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function TypeArgumentsContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_typeArguments;
    return this;
}

TypeArgumentsContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
TypeArgumentsContext.prototype.constructor = TypeArgumentsContext;

TypeArgumentsContext.prototype.typeArgument = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(TypeArgumentContext);
    } else {
        return this.getTypedRuleContext(TypeArgumentContext,i);
    }
};

TypeArgumentsContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterTypeArguments(this);
	}
};

TypeArgumentsContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitTypeArguments(this);
	}
};




JavaParser.TypeArgumentsContext = TypeArgumentsContext;

JavaParser.prototype.typeArguments = function() {

    var localctx = new TypeArgumentsContext(this, this._ctx, this.state);
    this.enterRule(localctx, 82, JavaParser.RULE_typeArguments);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 609;
        this.match(JavaParser.LT);
        this.state = 610;
        this.typeArgument();
        this.state = 615;
        this._errHandler.sync(this);
        _la = this._input.LA(1);
        while(_la===JavaParser.COMMA) {
            this.state = 611;
            this.match(JavaParser.COMMA);
            this.state = 612;
            this.typeArgument();
            this.state = 617;
            this._errHandler.sync(this);
            _la = this._input.LA(1);
        }
        this.state = 618;
        this.match(JavaParser.GT);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function TypeArgumentContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_typeArgument;
    return this;
}

TypeArgumentContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
TypeArgumentContext.prototype.constructor = TypeArgumentContext;

TypeArgumentContext.prototype.type = function() {
    return this.getTypedRuleContext(TypeContext,0);
};

TypeArgumentContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterTypeArgument(this);
	}
};

TypeArgumentContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitTypeArgument(this);
	}
};




JavaParser.TypeArgumentContext = TypeArgumentContext;

JavaParser.prototype.typeArgument = function() {

    var localctx = new TypeArgumentContext(this, this._ctx, this.state);
    this.enterRule(localctx, 84, JavaParser.RULE_typeArgument);
    var _la = 0; // Token type
    try {
        this.state = 626;
        switch(this._input.LA(1)) {
        case JavaParser.BOOLEAN:
        case JavaParser.BYTE:
        case JavaParser.CHAR:
        case JavaParser.DOUBLE:
        case JavaParser.FLOAT:
        case JavaParser.INT:
        case JavaParser.LONG:
        case JavaParser.SHORT:
        case JavaParser.Identifier:
            this.enterOuterAlt(localctx, 1);
            this.state = 620;
            this.type();
            break;
        case JavaParser.QUESTION:
            this.enterOuterAlt(localctx, 2);
            this.state = 621;
            this.match(JavaParser.QUESTION);
            this.state = 624;
            _la = this._input.LA(1);
            if(_la===JavaParser.EXTENDS || _la===JavaParser.SUPER) {
                this.state = 622;
                _la = this._input.LA(1);
                if(!(_la===JavaParser.EXTENDS || _la===JavaParser.SUPER)) {
                this._errHandler.recoverInline(this);
                }
                else {
                    this.consume();
                }
                this.state = 623;
                this.type();
            }

            break;
        default:
            throw new antlr4.error.NoViableAltException(this);
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function QualifiedNameListContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_qualifiedNameList;
    return this;
}

QualifiedNameListContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
QualifiedNameListContext.prototype.constructor = QualifiedNameListContext;

QualifiedNameListContext.prototype.qualifiedName = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(QualifiedNameContext);
    } else {
        return this.getTypedRuleContext(QualifiedNameContext,i);
    }
};

QualifiedNameListContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterQualifiedNameList(this);
	}
};

QualifiedNameListContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitQualifiedNameList(this);
	}
};




JavaParser.QualifiedNameListContext = QualifiedNameListContext;

JavaParser.prototype.qualifiedNameList = function() {

    var localctx = new QualifiedNameListContext(this, this._ctx, this.state);
    this.enterRule(localctx, 86, JavaParser.RULE_qualifiedNameList);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 628;
        this.qualifiedName();
        this.state = 633;
        this._errHandler.sync(this);
        _la = this._input.LA(1);
        while(_la===JavaParser.COMMA) {
            this.state = 629;
            this.match(JavaParser.COMMA);
            this.state = 630;
            this.qualifiedName();
            this.state = 635;
            this._errHandler.sync(this);
            _la = this._input.LA(1);
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function FormalParametersContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_formalParameters;
    return this;
}

FormalParametersContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
FormalParametersContext.prototype.constructor = FormalParametersContext;

FormalParametersContext.prototype.formalParameterList = function() {
    return this.getTypedRuleContext(FormalParameterListContext,0);
};

FormalParametersContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterFormalParameters(this);
	}
};

FormalParametersContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitFormalParameters(this);
	}
};




JavaParser.FormalParametersContext = FormalParametersContext;

JavaParser.prototype.formalParameters = function() {

    var localctx = new FormalParametersContext(this, this._ctx, this.state);
    this.enterRule(localctx, 88, JavaParser.RULE_formalParameters);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 636;
        this.match(JavaParser.LPAREN);
        this.state = 638;
        _la = this._input.LA(1);
        if((((_la) & ~0x1f) == 0 && ((1 << _la) & ((1 << JavaParser.BOOLEAN) | (1 << JavaParser.BYTE) | (1 << JavaParser.CHAR) | (1 << JavaParser.DOUBLE) | (1 << JavaParser.FINAL) | (1 << JavaParser.FLOAT) | (1 << JavaParser.INT) | (1 << JavaParser.LONG))) !== 0) || _la===JavaParser.SHORT || _la===JavaParser.Identifier || _la===JavaParser.AT) {
            this.state = 637;
            this.formalParameterList();
        }

        this.state = 640;
        this.match(JavaParser.RPAREN);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function FormalParameterListContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_formalParameterList;
    return this;
}

FormalParameterListContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
FormalParameterListContext.prototype.constructor = FormalParameterListContext;

FormalParameterListContext.prototype.formalParameter = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(FormalParameterContext);
    } else {
        return this.getTypedRuleContext(FormalParameterContext,i);
    }
};

FormalParameterListContext.prototype.lastFormalParameter = function() {
    return this.getTypedRuleContext(LastFormalParameterContext,0);
};

FormalParameterListContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterFormalParameterList(this);
	}
};

FormalParameterListContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitFormalParameterList(this);
	}
};




JavaParser.FormalParameterListContext = FormalParameterListContext;

JavaParser.prototype.formalParameterList = function() {

    var localctx = new FormalParameterListContext(this, this._ctx, this.state);
    this.enterRule(localctx, 90, JavaParser.RULE_formalParameterList);
    var _la = 0; // Token type
    try {
        this.state = 655;
        var la_ = this._interp.adaptivePredict(this._input,71,this._ctx);
        switch(la_) {
        case 1:
            this.enterOuterAlt(localctx, 1);
            this.state = 642;
            this.formalParameter();
            this.state = 647;
            this._errHandler.sync(this);
            var _alt = this._interp.adaptivePredict(this._input,69,this._ctx)
            while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
                if(_alt===1) {
                    this.state = 643;
                    this.match(JavaParser.COMMA);
                    this.state = 644;
                    this.formalParameter(); 
                }
                this.state = 649;
                this._errHandler.sync(this);
                _alt = this._interp.adaptivePredict(this._input,69,this._ctx);
            }

            this.state = 652;
            _la = this._input.LA(1);
            if(_la===JavaParser.COMMA) {
                this.state = 650;
                this.match(JavaParser.COMMA);
                this.state = 651;
                this.lastFormalParameter();
            }

            break;

        case 2:
            this.enterOuterAlt(localctx, 2);
            this.state = 654;
            this.lastFormalParameter();
            break;

        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function FormalParameterContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_formalParameter;
    return this;
}

FormalParameterContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
FormalParameterContext.prototype.constructor = FormalParameterContext;

FormalParameterContext.prototype.type = function() {
    return this.getTypedRuleContext(TypeContext,0);
};

FormalParameterContext.prototype.variableDeclaratorId = function() {
    return this.getTypedRuleContext(VariableDeclaratorIdContext,0);
};

FormalParameterContext.prototype.variableModifier = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(VariableModifierContext);
    } else {
        return this.getTypedRuleContext(VariableModifierContext,i);
    }
};

FormalParameterContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterFormalParameter(this);
	}
};

FormalParameterContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitFormalParameter(this);
	}
};




JavaParser.FormalParameterContext = FormalParameterContext;

JavaParser.prototype.formalParameter = function() {

    var localctx = new FormalParameterContext(this, this._ctx, this.state);
    this.enterRule(localctx, 92, JavaParser.RULE_formalParameter);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 660;
        this._errHandler.sync(this);
        _la = this._input.LA(1);
        while(_la===JavaParser.FINAL || _la===JavaParser.AT) {
            this.state = 657;
            this.variableModifier();
            this.state = 662;
            this._errHandler.sync(this);
            _la = this._input.LA(1);
        }
        this.state = 663;
        this.type();
        this.state = 664;
        this.variableDeclaratorId();
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function LastFormalParameterContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_lastFormalParameter;
    return this;
}

LastFormalParameterContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
LastFormalParameterContext.prototype.constructor = LastFormalParameterContext;

LastFormalParameterContext.prototype.type = function() {
    return this.getTypedRuleContext(TypeContext,0);
};

LastFormalParameterContext.prototype.variableDeclaratorId = function() {
    return this.getTypedRuleContext(VariableDeclaratorIdContext,0);
};

LastFormalParameterContext.prototype.variableModifier = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(VariableModifierContext);
    } else {
        return this.getTypedRuleContext(VariableModifierContext,i);
    }
};

LastFormalParameterContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterLastFormalParameter(this);
	}
};

LastFormalParameterContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitLastFormalParameter(this);
	}
};




JavaParser.LastFormalParameterContext = LastFormalParameterContext;

JavaParser.prototype.lastFormalParameter = function() {

    var localctx = new LastFormalParameterContext(this, this._ctx, this.state);
    this.enterRule(localctx, 94, JavaParser.RULE_lastFormalParameter);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 669;
        this._errHandler.sync(this);
        _la = this._input.LA(1);
        while(_la===JavaParser.FINAL || _la===JavaParser.AT) {
            this.state = 666;
            this.variableModifier();
            this.state = 671;
            this._errHandler.sync(this);
            _la = this._input.LA(1);
        }
        this.state = 672;
        this.type();
        this.state = 673;
        this.match(JavaParser.ELLIPSIS);
        this.state = 674;
        this.variableDeclaratorId();
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function MethodBodyContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_methodBody;
    return this;
}

MethodBodyContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
MethodBodyContext.prototype.constructor = MethodBodyContext;

MethodBodyContext.prototype.block = function() {
    return this.getTypedRuleContext(BlockContext,0);
};

MethodBodyContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterMethodBody(this);
	}
};

MethodBodyContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitMethodBody(this);
	}
};




JavaParser.MethodBodyContext = MethodBodyContext;

JavaParser.prototype.methodBody = function() {

    var localctx = new MethodBodyContext(this, this._ctx, this.state);
    this.enterRule(localctx, 96, JavaParser.RULE_methodBody);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 676;
        this.block();
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function ConstructorBodyContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_constructorBody;
    return this;
}

ConstructorBodyContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
ConstructorBodyContext.prototype.constructor = ConstructorBodyContext;

ConstructorBodyContext.prototype.block = function() {
    return this.getTypedRuleContext(BlockContext,0);
};

ConstructorBodyContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterConstructorBody(this);
	}
};

ConstructorBodyContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitConstructorBody(this);
	}
};




JavaParser.ConstructorBodyContext = ConstructorBodyContext;

JavaParser.prototype.constructorBody = function() {

    var localctx = new ConstructorBodyContext(this, this._ctx, this.state);
    this.enterRule(localctx, 98, JavaParser.RULE_constructorBody);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 678;
        this.block();
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function QualifiedNameContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_qualifiedName;
    return this;
}

QualifiedNameContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
QualifiedNameContext.prototype.constructor = QualifiedNameContext;

QualifiedNameContext.prototype.Identifier = function(i) {
	if(i===undefined) {
		i = null;
	}
    if(i===null) {
        return this.getTokens(JavaParser.Identifier);
    } else {
        return this.getToken(JavaParser.Identifier, i);
    }
};


QualifiedNameContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterQualifiedName(this);
	}
};

QualifiedNameContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitQualifiedName(this);
	}
};




JavaParser.QualifiedNameContext = QualifiedNameContext;

JavaParser.prototype.qualifiedName = function() {

    var localctx = new QualifiedNameContext(this, this._ctx, this.state);
    this.enterRule(localctx, 100, JavaParser.RULE_qualifiedName);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 680;
        this.match(JavaParser.Identifier);
        this.state = 685;
        this._errHandler.sync(this);
        var _alt = this._interp.adaptivePredict(this._input,74,this._ctx)
        while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
            if(_alt===1) {
                this.state = 681;
                this.match(JavaParser.DOT);
                this.state = 682;
                this.match(JavaParser.Identifier); 
            }
            this.state = 687;
            this._errHandler.sync(this);
            _alt = this._interp.adaptivePredict(this._input,74,this._ctx);
        }

    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function LiteralContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_literal;
    return this;
}

LiteralContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
LiteralContext.prototype.constructor = LiteralContext;

LiteralContext.prototype.IntegerLiteral = function() {
    return this.getToken(JavaParser.IntegerLiteral, 0);
};

LiteralContext.prototype.FloatingPointLiteral = function() {
    return this.getToken(JavaParser.FloatingPointLiteral, 0);
};

LiteralContext.prototype.CharacterLiteral = function() {
    return this.getToken(JavaParser.CharacterLiteral, 0);
};

LiteralContext.prototype.StringLiteral = function() {
    return this.getToken(JavaParser.StringLiteral, 0);
};

LiteralContext.prototype.BooleanLiteral = function() {
    return this.getToken(JavaParser.BooleanLiteral, 0);
};

LiteralContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterLiteral(this);
	}
};

LiteralContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitLiteral(this);
	}
};




JavaParser.LiteralContext = LiteralContext;

JavaParser.prototype.literal = function() {

    var localctx = new LiteralContext(this, this._ctx, this.state);
    this.enterRule(localctx, 102, JavaParser.RULE_literal);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 688;
        _la = this._input.LA(1);
        if(!(((((_la - 51)) & ~0x1f) == 0 && ((1 << (_la - 51)) & ((1 << (JavaParser.IntegerLiteral - 51)) | (1 << (JavaParser.FloatingPointLiteral - 51)) | (1 << (JavaParser.BooleanLiteral - 51)) | (1 << (JavaParser.CharacterLiteral - 51)) | (1 << (JavaParser.StringLiteral - 51)) | (1 << (JavaParser.NullLiteral - 51)))) !== 0))) {
        this._errHandler.recoverInline(this);
        }
        else {
            this.consume();
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function AnnotationContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_annotation;
    return this;
}

AnnotationContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
AnnotationContext.prototype.constructor = AnnotationContext;

AnnotationContext.prototype.annotationName = function() {
    return this.getTypedRuleContext(AnnotationNameContext,0);
};

AnnotationContext.prototype.elementValuePairs = function() {
    return this.getTypedRuleContext(ElementValuePairsContext,0);
};

AnnotationContext.prototype.elementValue = function() {
    return this.getTypedRuleContext(ElementValueContext,0);
};

AnnotationContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterAnnotation(this);
	}
};

AnnotationContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitAnnotation(this);
	}
};




JavaParser.AnnotationContext = AnnotationContext;

JavaParser.prototype.annotation = function() {

    var localctx = new AnnotationContext(this, this._ctx, this.state);
    this.enterRule(localctx, 104, JavaParser.RULE_annotation);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 690;
        this.match(JavaParser.AT);
        this.state = 691;
        this.annotationName();
        this.state = 698;
        _la = this._input.LA(1);
        if(_la===JavaParser.LPAREN) {
            this.state = 692;
            this.match(JavaParser.LPAREN);
            this.state = 695;
            var la_ = this._interp.adaptivePredict(this._input,75,this._ctx);
            if(la_===1) {
                this.state = 693;
                this.elementValuePairs();

            } else if(la_===2) {
                this.state = 694;
                this.elementValue();

            }
            this.state = 697;
            this.match(JavaParser.RPAREN);
        }

    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function AnnotationNameContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_annotationName;
    return this;
}

AnnotationNameContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
AnnotationNameContext.prototype.constructor = AnnotationNameContext;

AnnotationNameContext.prototype.qualifiedName = function() {
    return this.getTypedRuleContext(QualifiedNameContext,0);
};

AnnotationNameContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterAnnotationName(this);
	}
};

AnnotationNameContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitAnnotationName(this);
	}
};




JavaParser.AnnotationNameContext = AnnotationNameContext;

JavaParser.prototype.annotationName = function() {

    var localctx = new AnnotationNameContext(this, this._ctx, this.state);
    this.enterRule(localctx, 106, JavaParser.RULE_annotationName);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 700;
        this.qualifiedName();
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function ElementValuePairsContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_elementValuePairs;
    return this;
}

ElementValuePairsContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
ElementValuePairsContext.prototype.constructor = ElementValuePairsContext;

ElementValuePairsContext.prototype.elementValuePair = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(ElementValuePairContext);
    } else {
        return this.getTypedRuleContext(ElementValuePairContext,i);
    }
};

ElementValuePairsContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterElementValuePairs(this);
	}
};

ElementValuePairsContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitElementValuePairs(this);
	}
};




JavaParser.ElementValuePairsContext = ElementValuePairsContext;

JavaParser.prototype.elementValuePairs = function() {

    var localctx = new ElementValuePairsContext(this, this._ctx, this.state);
    this.enterRule(localctx, 108, JavaParser.RULE_elementValuePairs);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 702;
        this.elementValuePair();
        this.state = 707;
        this._errHandler.sync(this);
        _la = this._input.LA(1);
        while(_la===JavaParser.COMMA) {
            this.state = 703;
            this.match(JavaParser.COMMA);
            this.state = 704;
            this.elementValuePair();
            this.state = 709;
            this._errHandler.sync(this);
            _la = this._input.LA(1);
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function ElementValuePairContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_elementValuePair;
    return this;
}

ElementValuePairContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
ElementValuePairContext.prototype.constructor = ElementValuePairContext;

ElementValuePairContext.prototype.Identifier = function() {
    return this.getToken(JavaParser.Identifier, 0);
};

ElementValuePairContext.prototype.elementValue = function() {
    return this.getTypedRuleContext(ElementValueContext,0);
};

ElementValuePairContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterElementValuePair(this);
	}
};

ElementValuePairContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitElementValuePair(this);
	}
};




JavaParser.ElementValuePairContext = ElementValuePairContext;

JavaParser.prototype.elementValuePair = function() {

    var localctx = new ElementValuePairContext(this, this._ctx, this.state);
    this.enterRule(localctx, 110, JavaParser.RULE_elementValuePair);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 710;
        this.match(JavaParser.Identifier);
        this.state = 711;
        this.match(JavaParser.ASSIGN);
        this.state = 712;
        this.elementValue();
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function ElementValueContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_elementValue;
    return this;
}

ElementValueContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
ElementValueContext.prototype.constructor = ElementValueContext;

ElementValueContext.prototype.expression = function() {
    return this.getTypedRuleContext(ExpressionContext,0);
};

ElementValueContext.prototype.annotation = function() {
    return this.getTypedRuleContext(AnnotationContext,0);
};

ElementValueContext.prototype.elementValueArrayInitializer = function() {
    return this.getTypedRuleContext(ElementValueArrayInitializerContext,0);
};

ElementValueContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterElementValue(this);
	}
};

ElementValueContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitElementValue(this);
	}
};




JavaParser.ElementValueContext = ElementValueContext;

JavaParser.prototype.elementValue = function() {

    var localctx = new ElementValueContext(this, this._ctx, this.state);
    this.enterRule(localctx, 112, JavaParser.RULE_elementValue);
    try {
        this.state = 717;
        switch(this._input.LA(1)) {
        case JavaParser.BOOLEAN:
        case JavaParser.BYTE:
        case JavaParser.CHAR:
        case JavaParser.DOUBLE:
        case JavaParser.FLOAT:
        case JavaParser.INT:
        case JavaParser.LONG:
        case JavaParser.NEW:
        case JavaParser.SHORT:
        case JavaParser.SUPER:
        case JavaParser.THIS:
        case JavaParser.VOID:
        case JavaParser.IntegerLiteral:
        case JavaParser.FloatingPointLiteral:
        case JavaParser.BooleanLiteral:
        case JavaParser.CharacterLiteral:
        case JavaParser.StringLiteral:
        case JavaParser.NullLiteral:
        case JavaParser.LPAREN:
        case JavaParser.LT:
        case JavaParser.BANG:
        case JavaParser.TILDE:
        case JavaParser.INC:
        case JavaParser.DEC:
        case JavaParser.ADD:
        case JavaParser.SUB:
        case JavaParser.Identifier:
            this.enterOuterAlt(localctx, 1);
            this.state = 714;
            this.expression(0);
            break;
        case JavaParser.AT:
            this.enterOuterAlt(localctx, 2);
            this.state = 715;
            this.annotation();
            break;
        case JavaParser.LBRACE:
            this.enterOuterAlt(localctx, 3);
            this.state = 716;
            this.elementValueArrayInitializer();
            break;
        default:
            throw new antlr4.error.NoViableAltException(this);
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function ElementValueArrayInitializerContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_elementValueArrayInitializer;
    return this;
}

ElementValueArrayInitializerContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
ElementValueArrayInitializerContext.prototype.constructor = ElementValueArrayInitializerContext;

ElementValueArrayInitializerContext.prototype.elementValue = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(ElementValueContext);
    } else {
        return this.getTypedRuleContext(ElementValueContext,i);
    }
};

ElementValueArrayInitializerContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterElementValueArrayInitializer(this);
	}
};

ElementValueArrayInitializerContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitElementValueArrayInitializer(this);
	}
};




JavaParser.ElementValueArrayInitializerContext = ElementValueArrayInitializerContext;

JavaParser.prototype.elementValueArrayInitializer = function() {

    var localctx = new ElementValueArrayInitializerContext(this, this._ctx, this.state);
    this.enterRule(localctx, 114, JavaParser.RULE_elementValueArrayInitializer);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 719;
        this.match(JavaParser.LBRACE);
        this.state = 728;
        _la = this._input.LA(1);
        if((((_la) & ~0x1f) == 0 && ((1 << _la) & ((1 << JavaParser.BOOLEAN) | (1 << JavaParser.BYTE) | (1 << JavaParser.CHAR) | (1 << JavaParser.DOUBLE) | (1 << JavaParser.FLOAT) | (1 << JavaParser.INT) | (1 << JavaParser.LONG) | (1 << JavaParser.NEW))) !== 0) || ((((_la - 37)) & ~0x1f) == 0 && ((1 << (_la - 37)) & ((1 << (JavaParser.SHORT - 37)) | (1 << (JavaParser.SUPER - 37)) | (1 << (JavaParser.THIS - 37)) | (1 << (JavaParser.VOID - 37)) | (1 << (JavaParser.IntegerLiteral - 37)) | (1 << (JavaParser.FloatingPointLiteral - 37)) | (1 << (JavaParser.BooleanLiteral - 37)) | (1 << (JavaParser.CharacterLiteral - 37)) | (1 << (JavaParser.StringLiteral - 37)) | (1 << (JavaParser.NullLiteral - 37)) | (1 << (JavaParser.LPAREN - 37)) | (1 << (JavaParser.LBRACE - 37)) | (1 << (JavaParser.LT - 37)))) !== 0) || ((((_la - 69)) & ~0x1f) == 0 && ((1 << (_la - 69)) & ((1 << (JavaParser.BANG - 69)) | (1 << (JavaParser.TILDE - 69)) | (1 << (JavaParser.INC - 69)) | (1 << (JavaParser.DEC - 69)) | (1 << (JavaParser.ADD - 69)) | (1 << (JavaParser.SUB - 69)) | (1 << (JavaParser.Identifier - 69)))) !== 0) || _la===JavaParser.AT) {
            this.state = 720;
            this.elementValue();
            this.state = 725;
            this._errHandler.sync(this);
            var _alt = this._interp.adaptivePredict(this._input,79,this._ctx)
            while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
                if(_alt===1) {
                    this.state = 721;
                    this.match(JavaParser.COMMA);
                    this.state = 722;
                    this.elementValue(); 
                }
                this.state = 727;
                this._errHandler.sync(this);
                _alt = this._interp.adaptivePredict(this._input,79,this._ctx);
            }

        }

        this.state = 731;
        _la = this._input.LA(1);
        if(_la===JavaParser.COMMA) {
            this.state = 730;
            this.match(JavaParser.COMMA);
        }

        this.state = 733;
        this.match(JavaParser.RBRACE);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function AnnotationTypeDeclarationContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_annotationTypeDeclaration;
    return this;
}

AnnotationTypeDeclarationContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
AnnotationTypeDeclarationContext.prototype.constructor = AnnotationTypeDeclarationContext;

AnnotationTypeDeclarationContext.prototype.Identifier = function() {
    return this.getToken(JavaParser.Identifier, 0);
};

AnnotationTypeDeclarationContext.prototype.annotationTypeBody = function() {
    return this.getTypedRuleContext(AnnotationTypeBodyContext,0);
};

AnnotationTypeDeclarationContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterAnnotationTypeDeclaration(this);
	}
};

AnnotationTypeDeclarationContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitAnnotationTypeDeclaration(this);
	}
};




JavaParser.AnnotationTypeDeclarationContext = AnnotationTypeDeclarationContext;

JavaParser.prototype.annotationTypeDeclaration = function() {

    var localctx = new AnnotationTypeDeclarationContext(this, this._ctx, this.state);
    this.enterRule(localctx, 116, JavaParser.RULE_annotationTypeDeclaration);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 735;
        this.match(JavaParser.AT);
        this.state = 736;
        this.match(JavaParser.INTERFACE);
        this.state = 737;
        this.match(JavaParser.Identifier);
        this.state = 738;
        this.annotationTypeBody();
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function AnnotationTypeBodyContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_annotationTypeBody;
    return this;
}

AnnotationTypeBodyContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
AnnotationTypeBodyContext.prototype.constructor = AnnotationTypeBodyContext;

AnnotationTypeBodyContext.prototype.annotationTypeElementDeclaration = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(AnnotationTypeElementDeclarationContext);
    } else {
        return this.getTypedRuleContext(AnnotationTypeElementDeclarationContext,i);
    }
};

AnnotationTypeBodyContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterAnnotationTypeBody(this);
	}
};

AnnotationTypeBodyContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitAnnotationTypeBody(this);
	}
};




JavaParser.AnnotationTypeBodyContext = AnnotationTypeBodyContext;

JavaParser.prototype.annotationTypeBody = function() {

    var localctx = new AnnotationTypeBodyContext(this, this._ctx, this.state);
    this.enterRule(localctx, 118, JavaParser.RULE_annotationTypeBody);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 740;
        this.match(JavaParser.LBRACE);
        this.state = 744;
        this._errHandler.sync(this);
        _la = this._input.LA(1);
        while((((_la) & ~0x1f) == 0 && ((1 << _la) & ((1 << JavaParser.ABSTRACT) | (1 << JavaParser.BOOLEAN) | (1 << JavaParser.BYTE) | (1 << JavaParser.CHAR) | (1 << JavaParser.CLASS) | (1 << JavaParser.DOUBLE) | (1 << JavaParser.ENUM) | (1 << JavaParser.FINAL) | (1 << JavaParser.FLOAT) | (1 << JavaParser.INT) | (1 << JavaParser.INTERFACE) | (1 << JavaParser.LONG) | (1 << JavaParser.NATIVE))) !== 0) || ((((_la - 33)) & ~0x1f) == 0 && ((1 << (_la - 33)) & ((1 << (JavaParser.PRIVATE - 33)) | (1 << (JavaParser.PROTECTED - 33)) | (1 << (JavaParser.PUBLIC - 33)) | (1 << (JavaParser.SHORT - 33)) | (1 << (JavaParser.STATIC - 33)) | (1 << (JavaParser.STRICTFP - 33)) | (1 << (JavaParser.SYNCHRONIZED - 33)) | (1 << (JavaParser.TRANSIENT - 33)) | (1 << (JavaParser.VOLATILE - 33)) | (1 << (JavaParser.SEMI - 33)))) !== 0) || _la===JavaParser.Identifier || _la===JavaParser.AT) {
            this.state = 741;
            this.annotationTypeElementDeclaration();
            this.state = 746;
            this._errHandler.sync(this);
            _la = this._input.LA(1);
        }
        this.state = 747;
        this.match(JavaParser.RBRACE);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function AnnotationTypeElementDeclarationContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_annotationTypeElementDeclaration;
    return this;
}

AnnotationTypeElementDeclarationContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
AnnotationTypeElementDeclarationContext.prototype.constructor = AnnotationTypeElementDeclarationContext;

AnnotationTypeElementDeclarationContext.prototype.annotationTypeElementRest = function() {
    return this.getTypedRuleContext(AnnotationTypeElementRestContext,0);
};

AnnotationTypeElementDeclarationContext.prototype.modifier = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(ModifierContext);
    } else {
        return this.getTypedRuleContext(ModifierContext,i);
    }
};

AnnotationTypeElementDeclarationContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterAnnotationTypeElementDeclaration(this);
	}
};

AnnotationTypeElementDeclarationContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitAnnotationTypeElementDeclaration(this);
	}
};




JavaParser.AnnotationTypeElementDeclarationContext = AnnotationTypeElementDeclarationContext;

JavaParser.prototype.annotationTypeElementDeclaration = function() {

    var localctx = new AnnotationTypeElementDeclarationContext(this, this._ctx, this.state);
    this.enterRule(localctx, 120, JavaParser.RULE_annotationTypeElementDeclaration);
    try {
        this.state = 757;
        switch(this._input.LA(1)) {
        case JavaParser.ABSTRACT:
        case JavaParser.BOOLEAN:
        case JavaParser.BYTE:
        case JavaParser.CHAR:
        case JavaParser.CLASS:
        case JavaParser.DOUBLE:
        case JavaParser.ENUM:
        case JavaParser.FINAL:
        case JavaParser.FLOAT:
        case JavaParser.INT:
        case JavaParser.INTERFACE:
        case JavaParser.LONG:
        case JavaParser.NATIVE:
        case JavaParser.PRIVATE:
        case JavaParser.PROTECTED:
        case JavaParser.PUBLIC:
        case JavaParser.SHORT:
        case JavaParser.STATIC:
        case JavaParser.STRICTFP:
        case JavaParser.SYNCHRONIZED:
        case JavaParser.TRANSIENT:
        case JavaParser.VOLATILE:
        case JavaParser.Identifier:
        case JavaParser.AT:
            this.enterOuterAlt(localctx, 1);
            this.state = 752;
            this._errHandler.sync(this);
            var _alt = this._interp.adaptivePredict(this._input,83,this._ctx)
            while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
                if(_alt===1) {
                    this.state = 749;
                    this.modifier(); 
                }
                this.state = 754;
                this._errHandler.sync(this);
                _alt = this._interp.adaptivePredict(this._input,83,this._ctx);
            }

            this.state = 755;
            this.annotationTypeElementRest();
            break;
        case JavaParser.SEMI:
            this.enterOuterAlt(localctx, 2);
            this.state = 756;
            this.match(JavaParser.SEMI);
            break;
        default:
            throw new antlr4.error.NoViableAltException(this);
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function AnnotationTypeElementRestContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_annotationTypeElementRest;
    return this;
}

AnnotationTypeElementRestContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
AnnotationTypeElementRestContext.prototype.constructor = AnnotationTypeElementRestContext;

AnnotationTypeElementRestContext.prototype.type = function() {
    return this.getTypedRuleContext(TypeContext,0);
};

AnnotationTypeElementRestContext.prototype.annotationMethodOrConstantRest = function() {
    return this.getTypedRuleContext(AnnotationMethodOrConstantRestContext,0);
};

AnnotationTypeElementRestContext.prototype.classDeclaration = function() {
    return this.getTypedRuleContext(ClassDeclarationContext,0);
};

AnnotationTypeElementRestContext.prototype.interfaceDeclaration = function() {
    return this.getTypedRuleContext(InterfaceDeclarationContext,0);
};

AnnotationTypeElementRestContext.prototype.enumDeclaration = function() {
    return this.getTypedRuleContext(EnumDeclarationContext,0);
};

AnnotationTypeElementRestContext.prototype.annotationTypeDeclaration = function() {
    return this.getTypedRuleContext(AnnotationTypeDeclarationContext,0);
};

AnnotationTypeElementRestContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterAnnotationTypeElementRest(this);
	}
};

AnnotationTypeElementRestContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitAnnotationTypeElementRest(this);
	}
};




JavaParser.AnnotationTypeElementRestContext = AnnotationTypeElementRestContext;

JavaParser.prototype.annotationTypeElementRest = function() {

    var localctx = new AnnotationTypeElementRestContext(this, this._ctx, this.state);
    this.enterRule(localctx, 122, JavaParser.RULE_annotationTypeElementRest);
    try {
        this.state = 779;
        switch(this._input.LA(1)) {
        case JavaParser.BOOLEAN:
        case JavaParser.BYTE:
        case JavaParser.CHAR:
        case JavaParser.DOUBLE:
        case JavaParser.FLOAT:
        case JavaParser.INT:
        case JavaParser.LONG:
        case JavaParser.SHORT:
        case JavaParser.Identifier:
            this.enterOuterAlt(localctx, 1);
            this.state = 759;
            this.type();
            this.state = 760;
            this.annotationMethodOrConstantRest();
            this.state = 761;
            this.match(JavaParser.SEMI);
            break;
        case JavaParser.CLASS:
            this.enterOuterAlt(localctx, 2);
            this.state = 763;
            this.classDeclaration();
            this.state = 765;
            var la_ = this._interp.adaptivePredict(this._input,85,this._ctx);
            if(la_===1) {
                this.state = 764;
                this.match(JavaParser.SEMI);

            }
            break;
        case JavaParser.INTERFACE:
            this.enterOuterAlt(localctx, 3);
            this.state = 767;
            this.interfaceDeclaration();
            this.state = 769;
            var la_ = this._interp.adaptivePredict(this._input,86,this._ctx);
            if(la_===1) {
                this.state = 768;
                this.match(JavaParser.SEMI);

            }
            break;
        case JavaParser.ENUM:
            this.enterOuterAlt(localctx, 4);
            this.state = 771;
            this.enumDeclaration();
            this.state = 773;
            var la_ = this._interp.adaptivePredict(this._input,87,this._ctx);
            if(la_===1) {
                this.state = 772;
                this.match(JavaParser.SEMI);

            }
            break;
        case JavaParser.AT:
            this.enterOuterAlt(localctx, 5);
            this.state = 775;
            this.annotationTypeDeclaration();
            this.state = 777;
            var la_ = this._interp.adaptivePredict(this._input,88,this._ctx);
            if(la_===1) {
                this.state = 776;
                this.match(JavaParser.SEMI);

            }
            break;
        default:
            throw new antlr4.error.NoViableAltException(this);
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function AnnotationMethodOrConstantRestContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_annotationMethodOrConstantRest;
    return this;
}

AnnotationMethodOrConstantRestContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
AnnotationMethodOrConstantRestContext.prototype.constructor = AnnotationMethodOrConstantRestContext;

AnnotationMethodOrConstantRestContext.prototype.annotationMethodRest = function() {
    return this.getTypedRuleContext(AnnotationMethodRestContext,0);
};

AnnotationMethodOrConstantRestContext.prototype.annotationConstantRest = function() {
    return this.getTypedRuleContext(AnnotationConstantRestContext,0);
};

AnnotationMethodOrConstantRestContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterAnnotationMethodOrConstantRest(this);
	}
};

AnnotationMethodOrConstantRestContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitAnnotationMethodOrConstantRest(this);
	}
};




JavaParser.AnnotationMethodOrConstantRestContext = AnnotationMethodOrConstantRestContext;

JavaParser.prototype.annotationMethodOrConstantRest = function() {

    var localctx = new AnnotationMethodOrConstantRestContext(this, this._ctx, this.state);
    this.enterRule(localctx, 124, JavaParser.RULE_annotationMethodOrConstantRest);
    try {
        this.state = 783;
        var la_ = this._interp.adaptivePredict(this._input,90,this._ctx);
        switch(la_) {
        case 1:
            this.enterOuterAlt(localctx, 1);
            this.state = 781;
            this.annotationMethodRest();
            break;

        case 2:
            this.enterOuterAlt(localctx, 2);
            this.state = 782;
            this.annotationConstantRest();
            break;

        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function AnnotationMethodRestContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_annotationMethodRest;
    return this;
}

AnnotationMethodRestContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
AnnotationMethodRestContext.prototype.constructor = AnnotationMethodRestContext;

AnnotationMethodRestContext.prototype.Identifier = function() {
    return this.getToken(JavaParser.Identifier, 0);
};

AnnotationMethodRestContext.prototype.defaultValue = function() {
    return this.getTypedRuleContext(DefaultValueContext,0);
};

AnnotationMethodRestContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterAnnotationMethodRest(this);
	}
};

AnnotationMethodRestContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitAnnotationMethodRest(this);
	}
};




JavaParser.AnnotationMethodRestContext = AnnotationMethodRestContext;

JavaParser.prototype.annotationMethodRest = function() {

    var localctx = new AnnotationMethodRestContext(this, this._ctx, this.state);
    this.enterRule(localctx, 126, JavaParser.RULE_annotationMethodRest);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 785;
        this.match(JavaParser.Identifier);
        this.state = 786;
        this.match(JavaParser.LPAREN);
        this.state = 787;
        this.match(JavaParser.RPAREN);
        this.state = 789;
        _la = this._input.LA(1);
        if(_la===JavaParser.DEFAULT) {
            this.state = 788;
            this.defaultValue();
        }

    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function AnnotationConstantRestContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_annotationConstantRest;
    return this;
}

AnnotationConstantRestContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
AnnotationConstantRestContext.prototype.constructor = AnnotationConstantRestContext;

AnnotationConstantRestContext.prototype.variableDeclarators = function() {
    return this.getTypedRuleContext(VariableDeclaratorsContext,0);
};

AnnotationConstantRestContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterAnnotationConstantRest(this);
	}
};

AnnotationConstantRestContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitAnnotationConstantRest(this);
	}
};




JavaParser.AnnotationConstantRestContext = AnnotationConstantRestContext;

JavaParser.prototype.annotationConstantRest = function() {

    var localctx = new AnnotationConstantRestContext(this, this._ctx, this.state);
    this.enterRule(localctx, 128, JavaParser.RULE_annotationConstantRest);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 791;
        this.variableDeclarators();
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function DefaultValueContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_defaultValue;
    return this;
}

DefaultValueContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
DefaultValueContext.prototype.constructor = DefaultValueContext;

DefaultValueContext.prototype.elementValue = function() {
    return this.getTypedRuleContext(ElementValueContext,0);
};

DefaultValueContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterDefaultValue(this);
	}
};

DefaultValueContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitDefaultValue(this);
	}
};




JavaParser.DefaultValueContext = DefaultValueContext;

JavaParser.prototype.defaultValue = function() {

    var localctx = new DefaultValueContext(this, this._ctx, this.state);
    this.enterRule(localctx, 130, JavaParser.RULE_defaultValue);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 793;
        this.match(JavaParser.DEFAULT);
        this.state = 794;
        this.elementValue();
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function BlockContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_block;
    return this;
}

BlockContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
BlockContext.prototype.constructor = BlockContext;

BlockContext.prototype.blockStatement = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(BlockStatementContext);
    } else {
        return this.getTypedRuleContext(BlockStatementContext,i);
    }
};

BlockContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterBlock(this);
	}
};

BlockContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitBlock(this);
	}
};




JavaParser.BlockContext = BlockContext;

JavaParser.prototype.block = function() {

    var localctx = new BlockContext(this, this._ctx, this.state);
    this.enterRule(localctx, 132, JavaParser.RULE_block);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 796;
        this.match(JavaParser.LBRACE);
        this.state = 800;
        this._errHandler.sync(this);
        _la = this._input.LA(1);
        while((((_la) & ~0x1f) == 0 && ((1 << _la) & ((1 << JavaParser.ABSTRACT) | (1 << JavaParser.ASSERT) | (1 << JavaParser.BOOLEAN) | (1 << JavaParser.BREAK) | (1 << JavaParser.BYTE) | (1 << JavaParser.CHAR) | (1 << JavaParser.CLASS) | (1 << JavaParser.CONTINUE) | (1 << JavaParser.DO) | (1 << JavaParser.DOUBLE) | (1 << JavaParser.ENUM) | (1 << JavaParser.FINAL) | (1 << JavaParser.FLOAT) | (1 << JavaParser.FOR) | (1 << JavaParser.IF) | (1 << JavaParser.INT) | (1 << JavaParser.INTERFACE) | (1 << JavaParser.LONG) | (1 << JavaParser.NEW))) !== 0) || ((((_la - 33)) & ~0x1f) == 0 && ((1 << (_la - 33)) & ((1 << (JavaParser.PRIVATE - 33)) | (1 << (JavaParser.PROTECTED - 33)) | (1 << (JavaParser.PUBLIC - 33)) | (1 << (JavaParser.RETURN - 33)) | (1 << (JavaParser.SHORT - 33)) | (1 << (JavaParser.STATIC - 33)) | (1 << (JavaParser.STRICTFP - 33)) | (1 << (JavaParser.SUPER - 33)) | (1 << (JavaParser.SWITCH - 33)) | (1 << (JavaParser.SYNCHRONIZED - 33)) | (1 << (JavaParser.THIS - 33)) | (1 << (JavaParser.THROW - 33)) | (1 << (JavaParser.TRY - 33)) | (1 << (JavaParser.VOID - 33)) | (1 << (JavaParser.WHILE - 33)) | (1 << (JavaParser.IntegerLiteral - 33)) | (1 << (JavaParser.FloatingPointLiteral - 33)) | (1 << (JavaParser.BooleanLiteral - 33)) | (1 << (JavaParser.CharacterLiteral - 33)) | (1 << (JavaParser.StringLiteral - 33)) | (1 << (JavaParser.NullLiteral - 33)) | (1 << (JavaParser.LPAREN - 33)) | (1 << (JavaParser.LBRACE - 33)) | (1 << (JavaParser.SEMI - 33)))) !== 0) || ((((_la - 68)) & ~0x1f) == 0 && ((1 << (_la - 68)) & ((1 << (JavaParser.LT - 68)) | (1 << (JavaParser.BANG - 68)) | (1 << (JavaParser.TILDE - 68)) | (1 << (JavaParser.INC - 68)) | (1 << (JavaParser.DEC - 68)) | (1 << (JavaParser.ADD - 68)) | (1 << (JavaParser.SUB - 68)))) !== 0) || _la===JavaParser.Identifier || _la===JavaParser.AT) {
            this.state = 797;
            this.blockStatement();
            this.state = 802;
            this._errHandler.sync(this);
            _la = this._input.LA(1);
        }
        this.state = 803;
        this.match(JavaParser.RBRACE);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function BlockStatementContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_blockStatement;
    return this;
}

BlockStatementContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
BlockStatementContext.prototype.constructor = BlockStatementContext;

BlockStatementContext.prototype.localVariableDeclarationStatement = function() {
    return this.getTypedRuleContext(LocalVariableDeclarationStatementContext,0);
};

BlockStatementContext.prototype.statement = function() {
    return this.getTypedRuleContext(StatementContext,0);
};

BlockStatementContext.prototype.typeDeclaration = function() {
    return this.getTypedRuleContext(TypeDeclarationContext,0);
};

BlockStatementContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterBlockStatement(this);
	}
};

BlockStatementContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitBlockStatement(this);
	}
};




JavaParser.BlockStatementContext = BlockStatementContext;

JavaParser.prototype.blockStatement = function() {

    var localctx = new BlockStatementContext(this, this._ctx, this.state);
    this.enterRule(localctx, 134, JavaParser.RULE_blockStatement);
    try {
        this.state = 808;
        var la_ = this._interp.adaptivePredict(this._input,93,this._ctx);
        switch(la_) {
        case 1:
            this.enterOuterAlt(localctx, 1);
            this.state = 805;
            this.localVariableDeclarationStatement();
            break;

        case 2:
            this.enterOuterAlt(localctx, 2);
            this.state = 806;
            this.statement();
            break;

        case 3:
            this.enterOuterAlt(localctx, 3);
            this.state = 807;
            this.typeDeclaration();
            break;

        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function LocalVariableDeclarationStatementContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_localVariableDeclarationStatement;
    return this;
}

LocalVariableDeclarationStatementContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
LocalVariableDeclarationStatementContext.prototype.constructor = LocalVariableDeclarationStatementContext;

LocalVariableDeclarationStatementContext.prototype.localVariableDeclaration = function() {
    return this.getTypedRuleContext(LocalVariableDeclarationContext,0);
};

LocalVariableDeclarationStatementContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterLocalVariableDeclarationStatement(this);
	}
};

LocalVariableDeclarationStatementContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitLocalVariableDeclarationStatement(this);
	}
};




JavaParser.LocalVariableDeclarationStatementContext = LocalVariableDeclarationStatementContext;

JavaParser.prototype.localVariableDeclarationStatement = function() {

    var localctx = new LocalVariableDeclarationStatementContext(this, this._ctx, this.state);
    this.enterRule(localctx, 136, JavaParser.RULE_localVariableDeclarationStatement);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 810;
        this.localVariableDeclaration();
        this.state = 811;
        this.match(JavaParser.SEMI);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function LocalVariableDeclarationContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_localVariableDeclaration;
    return this;
}

LocalVariableDeclarationContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
LocalVariableDeclarationContext.prototype.constructor = LocalVariableDeclarationContext;

LocalVariableDeclarationContext.prototype.type = function() {
    return this.getTypedRuleContext(TypeContext,0);
};

LocalVariableDeclarationContext.prototype.variableDeclarators = function() {
    return this.getTypedRuleContext(VariableDeclaratorsContext,0);
};

LocalVariableDeclarationContext.prototype.variableModifier = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(VariableModifierContext);
    } else {
        return this.getTypedRuleContext(VariableModifierContext,i);
    }
};

LocalVariableDeclarationContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterLocalVariableDeclaration(this);
	}
};

LocalVariableDeclarationContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitLocalVariableDeclaration(this);
	}
};




JavaParser.LocalVariableDeclarationContext = LocalVariableDeclarationContext;

JavaParser.prototype.localVariableDeclaration = function() {

    var localctx = new LocalVariableDeclarationContext(this, this._ctx, this.state);
    this.enterRule(localctx, 138, JavaParser.RULE_localVariableDeclaration);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 816;
        this._errHandler.sync(this);
        _la = this._input.LA(1);
        while(_la===JavaParser.FINAL || _la===JavaParser.AT) {
            this.state = 813;
            this.variableModifier();
            this.state = 818;
            this._errHandler.sync(this);
            _la = this._input.LA(1);
        }
        this.state = 819;
        this.type();
        this.state = 820;
        this.variableDeclarators();
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function StatementContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_statement;
    return this;
}

StatementContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
StatementContext.prototype.constructor = StatementContext;

StatementContext.prototype.block = function() {
    return this.getTypedRuleContext(BlockContext,0);
};

StatementContext.prototype.ASSERT = function() {
    return this.getToken(JavaParser.ASSERT, 0);
};

StatementContext.prototype.expression = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(ExpressionContext);
    } else {
        return this.getTypedRuleContext(ExpressionContext,i);
    }
};

StatementContext.prototype.parExpression = function() {
    return this.getTypedRuleContext(ParExpressionContext,0);
};

StatementContext.prototype.statement = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(StatementContext);
    } else {
        return this.getTypedRuleContext(StatementContext,i);
    }
};

StatementContext.prototype.forControl = function() {
    return this.getTypedRuleContext(ForControlContext,0);
};

StatementContext.prototype.finallyBlock = function() {
    return this.getTypedRuleContext(FinallyBlockContext,0);
};

StatementContext.prototype.catchClause = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(CatchClauseContext);
    } else {
        return this.getTypedRuleContext(CatchClauseContext,i);
    }
};

StatementContext.prototype.resourceSpecification = function() {
    return this.getTypedRuleContext(ResourceSpecificationContext,0);
};

StatementContext.prototype.switchBlockStatementGroup = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(SwitchBlockStatementGroupContext);
    } else {
        return this.getTypedRuleContext(SwitchBlockStatementGroupContext,i);
    }
};

StatementContext.prototype.switchLabel = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(SwitchLabelContext);
    } else {
        return this.getTypedRuleContext(SwitchLabelContext,i);
    }
};

StatementContext.prototype.Identifier = function() {
    return this.getToken(JavaParser.Identifier, 0);
};

StatementContext.prototype.statementExpression = function() {
    return this.getTypedRuleContext(StatementExpressionContext,0);
};

StatementContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterStatement(this);
	}
};

StatementContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitStatement(this);
	}
};




JavaParser.StatementContext = StatementContext;

JavaParser.prototype.statement = function() {

    var localctx = new StatementContext(this, this._ctx, this.state);
    this.enterRule(localctx, 140, JavaParser.RULE_statement);
    var _la = 0; // Token type
    try {
        this.state = 926;
        var la_ = this._interp.adaptivePredict(this._input,107,this._ctx);
        switch(la_) {
        case 1:
            this.enterOuterAlt(localctx, 1);
            this.state = 822;
            this.block();
            break;

        case 2:
            this.enterOuterAlt(localctx, 2);
            this.state = 823;
            this.match(JavaParser.ASSERT);
            this.state = 824;
            this.expression(0);
            this.state = 827;
            _la = this._input.LA(1);
            if(_la===JavaParser.COLON) {
                this.state = 825;
                this.match(JavaParser.COLON);
                this.state = 826;
                this.expression(0);
            }

            this.state = 829;
            this.match(JavaParser.SEMI);
            break;

        case 3:
            this.enterOuterAlt(localctx, 3);
            this.state = 831;
            this.match(JavaParser.IF);
            this.state = 832;
            this.parExpression();
            this.state = 833;
            this.statement();
            this.state = 836;
            var la_ = this._interp.adaptivePredict(this._input,96,this._ctx);
            if(la_===1) {
                this.state = 834;
                this.match(JavaParser.ELSE);
                this.state = 835;
                this.statement();

            }
            break;

        case 4:
            this.enterOuterAlt(localctx, 4);
            this.state = 838;
            this.match(JavaParser.FOR);
            this.state = 839;
            this.match(JavaParser.LPAREN);
            this.state = 840;
            this.forControl();
            this.state = 841;
            this.match(JavaParser.RPAREN);
            this.state = 842;
            this.statement();
            break;

        case 5:
            this.enterOuterAlt(localctx, 5);
            this.state = 844;
            this.match(JavaParser.WHILE);
            this.state = 845;
            this.parExpression();
            this.state = 846;
            this.statement();
            break;

        case 6:
            this.enterOuterAlt(localctx, 6);
            this.state = 848;
            this.match(JavaParser.DO);
            this.state = 849;
            this.statement();
            this.state = 850;
            this.match(JavaParser.WHILE);
            this.state = 851;
            this.parExpression();
            this.state = 852;
            this.match(JavaParser.SEMI);
            break;

        case 7:
            this.enterOuterAlt(localctx, 7);
            this.state = 854;
            this.match(JavaParser.TRY);
            this.state = 855;
            this.block();
            this.state = 865;
            switch(this._input.LA(1)) {
            case JavaParser.CATCH:
                this.state = 857; 
                this._errHandler.sync(this);
                _la = this._input.LA(1);
                do {
                    this.state = 856;
                    this.catchClause();
                    this.state = 859; 
                    this._errHandler.sync(this);
                    _la = this._input.LA(1);
                } while(_la===JavaParser.CATCH);
                this.state = 862;
                _la = this._input.LA(1);
                if(_la===JavaParser.FINALLY) {
                    this.state = 861;
                    this.finallyBlock();
                }

                break;
            case JavaParser.FINALLY:
                this.state = 864;
                this.finallyBlock();
                break;
            default:
                throw new antlr4.error.NoViableAltException(this);
            }
            break;

        case 8:
            this.enterOuterAlt(localctx, 8);
            this.state = 867;
            this.match(JavaParser.TRY);
            this.state = 868;
            this.resourceSpecification();
            this.state = 869;
            this.block();
            this.state = 873;
            this._errHandler.sync(this);
            _la = this._input.LA(1);
            while(_la===JavaParser.CATCH) {
                this.state = 870;
                this.catchClause();
                this.state = 875;
                this._errHandler.sync(this);
                _la = this._input.LA(1);
            }
            this.state = 877;
            _la = this._input.LA(1);
            if(_la===JavaParser.FINALLY) {
                this.state = 876;
                this.finallyBlock();
            }

            break;

        case 9:
            this.enterOuterAlt(localctx, 9);
            this.state = 879;
            this.match(JavaParser.SWITCH);
            this.state = 880;
            this.parExpression();
            this.state = 881;
            this.match(JavaParser.LBRACE);
            this.state = 885;
            this._errHandler.sync(this);
            var _alt = this._interp.adaptivePredict(this._input,102,this._ctx)
            while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
                if(_alt===1) {
                    this.state = 882;
                    this.switchBlockStatementGroup(); 
                }
                this.state = 887;
                this._errHandler.sync(this);
                _alt = this._interp.adaptivePredict(this._input,102,this._ctx);
            }

            this.state = 891;
            this._errHandler.sync(this);
            _la = this._input.LA(1);
            while(_la===JavaParser.CASE || _la===JavaParser.DEFAULT) {
                this.state = 888;
                this.switchLabel();
                this.state = 893;
                this._errHandler.sync(this);
                _la = this._input.LA(1);
            }
            this.state = 894;
            this.match(JavaParser.RBRACE);
            break;

        case 10:
            this.enterOuterAlt(localctx, 10);
            this.state = 896;
            this.match(JavaParser.SYNCHRONIZED);
            this.state = 897;
            this.parExpression();
            this.state = 898;
            this.block();
            break;

        case 11:
            this.enterOuterAlt(localctx, 11);
            this.state = 900;
            this.match(JavaParser.RETURN);
            this.state = 902;
            _la = this._input.LA(1);
            if((((_la) & ~0x1f) == 0 && ((1 << _la) & ((1 << JavaParser.BOOLEAN) | (1 << JavaParser.BYTE) | (1 << JavaParser.CHAR) | (1 << JavaParser.DOUBLE) | (1 << JavaParser.FLOAT) | (1 << JavaParser.INT) | (1 << JavaParser.LONG) | (1 << JavaParser.NEW))) !== 0) || ((((_la - 37)) & ~0x1f) == 0 && ((1 << (_la - 37)) & ((1 << (JavaParser.SHORT - 37)) | (1 << (JavaParser.SUPER - 37)) | (1 << (JavaParser.THIS - 37)) | (1 << (JavaParser.VOID - 37)) | (1 << (JavaParser.IntegerLiteral - 37)) | (1 << (JavaParser.FloatingPointLiteral - 37)) | (1 << (JavaParser.BooleanLiteral - 37)) | (1 << (JavaParser.CharacterLiteral - 37)) | (1 << (JavaParser.StringLiteral - 37)) | (1 << (JavaParser.NullLiteral - 37)) | (1 << (JavaParser.LPAREN - 37)) | (1 << (JavaParser.LT - 37)))) !== 0) || ((((_la - 69)) & ~0x1f) == 0 && ((1 << (_la - 69)) & ((1 << (JavaParser.BANG - 69)) | (1 << (JavaParser.TILDE - 69)) | (1 << (JavaParser.INC - 69)) | (1 << (JavaParser.DEC - 69)) | (1 << (JavaParser.ADD - 69)) | (1 << (JavaParser.SUB - 69)) | (1 << (JavaParser.Identifier - 69)))) !== 0)) {
                this.state = 901;
                this.expression(0);
            }

            this.state = 904;
            this.match(JavaParser.SEMI);
            break;

        case 12:
            this.enterOuterAlt(localctx, 12);
            this.state = 905;
            this.match(JavaParser.THROW);
            this.state = 906;
            this.expression(0);
            this.state = 907;
            this.match(JavaParser.SEMI);
            break;

        case 13:
            this.enterOuterAlt(localctx, 13);
            this.state = 909;
            this.match(JavaParser.BREAK);
            this.state = 911;
            _la = this._input.LA(1);
            if(_la===JavaParser.Identifier) {
                this.state = 910;
                this.match(JavaParser.Identifier);
            }

            this.state = 913;
            this.match(JavaParser.SEMI);
            break;

        case 14:
            this.enterOuterAlt(localctx, 14);
            this.state = 914;
            this.match(JavaParser.CONTINUE);
            this.state = 916;
            _la = this._input.LA(1);
            if(_la===JavaParser.Identifier) {
                this.state = 915;
                this.match(JavaParser.Identifier);
            }

            this.state = 918;
            this.match(JavaParser.SEMI);
            break;

        case 15:
            this.enterOuterAlt(localctx, 15);
            this.state = 919;
            this.match(JavaParser.SEMI);
            break;

        case 16:
            this.enterOuterAlt(localctx, 16);
            this.state = 920;
            this.statementExpression();
            this.state = 921;
            this.match(JavaParser.SEMI);
            break;

        case 17:
            this.enterOuterAlt(localctx, 17);
            this.state = 923;
            this.match(JavaParser.Identifier);
            this.state = 924;
            this.match(JavaParser.COLON);
            this.state = 925;
            this.statement();
            break;

        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function CatchClauseContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_catchClause;
    return this;
}

CatchClauseContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
CatchClauseContext.prototype.constructor = CatchClauseContext;

CatchClauseContext.prototype.catchType = function() {
    return this.getTypedRuleContext(CatchTypeContext,0);
};

CatchClauseContext.prototype.Identifier = function() {
    return this.getToken(JavaParser.Identifier, 0);
};

CatchClauseContext.prototype.block = function() {
    return this.getTypedRuleContext(BlockContext,0);
};

CatchClauseContext.prototype.variableModifier = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(VariableModifierContext);
    } else {
        return this.getTypedRuleContext(VariableModifierContext,i);
    }
};

CatchClauseContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterCatchClause(this);
	}
};

CatchClauseContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitCatchClause(this);
	}
};




JavaParser.CatchClauseContext = CatchClauseContext;

JavaParser.prototype.catchClause = function() {

    var localctx = new CatchClauseContext(this, this._ctx, this.state);
    this.enterRule(localctx, 142, JavaParser.RULE_catchClause);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 928;
        this.match(JavaParser.CATCH);
        this.state = 929;
        this.match(JavaParser.LPAREN);
        this.state = 933;
        this._errHandler.sync(this);
        _la = this._input.LA(1);
        while(_la===JavaParser.FINAL || _la===JavaParser.AT) {
            this.state = 930;
            this.variableModifier();
            this.state = 935;
            this._errHandler.sync(this);
            _la = this._input.LA(1);
        }
        this.state = 936;
        this.catchType();
        this.state = 937;
        this.match(JavaParser.Identifier);
        this.state = 938;
        this.match(JavaParser.RPAREN);
        this.state = 939;
        this.block();
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function CatchTypeContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_catchType;
    return this;
}

CatchTypeContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
CatchTypeContext.prototype.constructor = CatchTypeContext;

CatchTypeContext.prototype.qualifiedName = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(QualifiedNameContext);
    } else {
        return this.getTypedRuleContext(QualifiedNameContext,i);
    }
};

CatchTypeContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterCatchType(this);
	}
};

CatchTypeContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitCatchType(this);
	}
};




JavaParser.CatchTypeContext = CatchTypeContext;

JavaParser.prototype.catchType = function() {

    var localctx = new CatchTypeContext(this, this._ctx, this.state);
    this.enterRule(localctx, 144, JavaParser.RULE_catchType);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 941;
        this.qualifiedName();
        this.state = 946;
        this._errHandler.sync(this);
        _la = this._input.LA(1);
        while(_la===JavaParser.BITOR) {
            this.state = 942;
            this.match(JavaParser.BITOR);
            this.state = 943;
            this.qualifiedName();
            this.state = 948;
            this._errHandler.sync(this);
            _la = this._input.LA(1);
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function FinallyBlockContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_finallyBlock;
    return this;
}

FinallyBlockContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
FinallyBlockContext.prototype.constructor = FinallyBlockContext;

FinallyBlockContext.prototype.block = function() {
    return this.getTypedRuleContext(BlockContext,0);
};

FinallyBlockContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterFinallyBlock(this);
	}
};

FinallyBlockContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitFinallyBlock(this);
	}
};




JavaParser.FinallyBlockContext = FinallyBlockContext;

JavaParser.prototype.finallyBlock = function() {

    var localctx = new FinallyBlockContext(this, this._ctx, this.state);
    this.enterRule(localctx, 146, JavaParser.RULE_finallyBlock);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 949;
        this.match(JavaParser.FINALLY);
        this.state = 950;
        this.block();
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function ResourceSpecificationContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_resourceSpecification;
    return this;
}

ResourceSpecificationContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
ResourceSpecificationContext.prototype.constructor = ResourceSpecificationContext;

ResourceSpecificationContext.prototype.resources = function() {
    return this.getTypedRuleContext(ResourcesContext,0);
};

ResourceSpecificationContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterResourceSpecification(this);
	}
};

ResourceSpecificationContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitResourceSpecification(this);
	}
};




JavaParser.ResourceSpecificationContext = ResourceSpecificationContext;

JavaParser.prototype.resourceSpecification = function() {

    var localctx = new ResourceSpecificationContext(this, this._ctx, this.state);
    this.enterRule(localctx, 148, JavaParser.RULE_resourceSpecification);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 952;
        this.match(JavaParser.LPAREN);
        this.state = 953;
        this.resources();
        this.state = 955;
        _la = this._input.LA(1);
        if(_la===JavaParser.SEMI) {
            this.state = 954;
            this.match(JavaParser.SEMI);
        }

        this.state = 957;
        this.match(JavaParser.RPAREN);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function ResourcesContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_resources;
    return this;
}

ResourcesContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
ResourcesContext.prototype.constructor = ResourcesContext;

ResourcesContext.prototype.resource = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(ResourceContext);
    } else {
        return this.getTypedRuleContext(ResourceContext,i);
    }
};

ResourcesContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterResources(this);
	}
};

ResourcesContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitResources(this);
	}
};




JavaParser.ResourcesContext = ResourcesContext;

JavaParser.prototype.resources = function() {

    var localctx = new ResourcesContext(this, this._ctx, this.state);
    this.enterRule(localctx, 150, JavaParser.RULE_resources);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 959;
        this.resource();
        this.state = 964;
        this._errHandler.sync(this);
        var _alt = this._interp.adaptivePredict(this._input,111,this._ctx)
        while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
            if(_alt===1) {
                this.state = 960;
                this.match(JavaParser.SEMI);
                this.state = 961;
                this.resource(); 
            }
            this.state = 966;
            this._errHandler.sync(this);
            _alt = this._interp.adaptivePredict(this._input,111,this._ctx);
        }

    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function ResourceContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_resource;
    return this;
}

ResourceContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
ResourceContext.prototype.constructor = ResourceContext;

ResourceContext.prototype.classOrInterfaceType = function() {
    return this.getTypedRuleContext(ClassOrInterfaceTypeContext,0);
};

ResourceContext.prototype.variableDeclaratorId = function() {
    return this.getTypedRuleContext(VariableDeclaratorIdContext,0);
};

ResourceContext.prototype.expression = function() {
    return this.getTypedRuleContext(ExpressionContext,0);
};

ResourceContext.prototype.variableModifier = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(VariableModifierContext);
    } else {
        return this.getTypedRuleContext(VariableModifierContext,i);
    }
};

ResourceContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterResource(this);
	}
};

ResourceContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitResource(this);
	}
};




JavaParser.ResourceContext = ResourceContext;

JavaParser.prototype.resource = function() {

    var localctx = new ResourceContext(this, this._ctx, this.state);
    this.enterRule(localctx, 152, JavaParser.RULE_resource);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 970;
        this._errHandler.sync(this);
        _la = this._input.LA(1);
        while(_la===JavaParser.FINAL || _la===JavaParser.AT) {
            this.state = 967;
            this.variableModifier();
            this.state = 972;
            this._errHandler.sync(this);
            _la = this._input.LA(1);
        }
        this.state = 973;
        this.classOrInterfaceType();
        this.state = 974;
        this.variableDeclaratorId();
        this.state = 975;
        this.match(JavaParser.ASSIGN);
        this.state = 976;
        this.expression(0);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function SwitchBlockStatementGroupContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_switchBlockStatementGroup;
    return this;
}

SwitchBlockStatementGroupContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
SwitchBlockStatementGroupContext.prototype.constructor = SwitchBlockStatementGroupContext;

SwitchBlockStatementGroupContext.prototype.switchLabel = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(SwitchLabelContext);
    } else {
        return this.getTypedRuleContext(SwitchLabelContext,i);
    }
};

SwitchBlockStatementGroupContext.prototype.blockStatement = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(BlockStatementContext);
    } else {
        return this.getTypedRuleContext(BlockStatementContext,i);
    }
};

SwitchBlockStatementGroupContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterSwitchBlockStatementGroup(this);
	}
};

SwitchBlockStatementGroupContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitSwitchBlockStatementGroup(this);
	}
};




JavaParser.SwitchBlockStatementGroupContext = SwitchBlockStatementGroupContext;

JavaParser.prototype.switchBlockStatementGroup = function() {

    var localctx = new SwitchBlockStatementGroupContext(this, this._ctx, this.state);
    this.enterRule(localctx, 154, JavaParser.RULE_switchBlockStatementGroup);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 979; 
        this._errHandler.sync(this);
        _la = this._input.LA(1);
        do {
            this.state = 978;
            this.switchLabel();
            this.state = 981; 
            this._errHandler.sync(this);
            _la = this._input.LA(1);
        } while(_la===JavaParser.CASE || _la===JavaParser.DEFAULT);
        this.state = 984; 
        this._errHandler.sync(this);
        _la = this._input.LA(1);
        do {
            this.state = 983;
            this.blockStatement();
            this.state = 986; 
            this._errHandler.sync(this);
            _la = this._input.LA(1);
        } while((((_la) & ~0x1f) == 0 && ((1 << _la) & ((1 << JavaParser.ABSTRACT) | (1 << JavaParser.ASSERT) | (1 << JavaParser.BOOLEAN) | (1 << JavaParser.BREAK) | (1 << JavaParser.BYTE) | (1 << JavaParser.CHAR) | (1 << JavaParser.CLASS) | (1 << JavaParser.CONTINUE) | (1 << JavaParser.DO) | (1 << JavaParser.DOUBLE) | (1 << JavaParser.ENUM) | (1 << JavaParser.FINAL) | (1 << JavaParser.FLOAT) | (1 << JavaParser.FOR) | (1 << JavaParser.IF) | (1 << JavaParser.INT) | (1 << JavaParser.INTERFACE) | (1 << JavaParser.LONG) | (1 << JavaParser.NEW))) !== 0) || ((((_la - 33)) & ~0x1f) == 0 && ((1 << (_la - 33)) & ((1 << (JavaParser.PRIVATE - 33)) | (1 << (JavaParser.PROTECTED - 33)) | (1 << (JavaParser.PUBLIC - 33)) | (1 << (JavaParser.RETURN - 33)) | (1 << (JavaParser.SHORT - 33)) | (1 << (JavaParser.STATIC - 33)) | (1 << (JavaParser.STRICTFP - 33)) | (1 << (JavaParser.SUPER - 33)) | (1 << (JavaParser.SWITCH - 33)) | (1 << (JavaParser.SYNCHRONIZED - 33)) | (1 << (JavaParser.THIS - 33)) | (1 << (JavaParser.THROW - 33)) | (1 << (JavaParser.TRY - 33)) | (1 << (JavaParser.VOID - 33)) | (1 << (JavaParser.WHILE - 33)) | (1 << (JavaParser.IntegerLiteral - 33)) | (1 << (JavaParser.FloatingPointLiteral - 33)) | (1 << (JavaParser.BooleanLiteral - 33)) | (1 << (JavaParser.CharacterLiteral - 33)) | (1 << (JavaParser.StringLiteral - 33)) | (1 << (JavaParser.NullLiteral - 33)) | (1 << (JavaParser.LPAREN - 33)) | (1 << (JavaParser.LBRACE - 33)) | (1 << (JavaParser.SEMI - 33)))) !== 0) || ((((_la - 68)) & ~0x1f) == 0 && ((1 << (_la - 68)) & ((1 << (JavaParser.LT - 68)) | (1 << (JavaParser.BANG - 68)) | (1 << (JavaParser.TILDE - 68)) | (1 << (JavaParser.INC - 68)) | (1 << (JavaParser.DEC - 68)) | (1 << (JavaParser.ADD - 68)) | (1 << (JavaParser.SUB - 68)))) !== 0) || _la===JavaParser.Identifier || _la===JavaParser.AT);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function SwitchLabelContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_switchLabel;
    return this;
}

SwitchLabelContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
SwitchLabelContext.prototype.constructor = SwitchLabelContext;

SwitchLabelContext.prototype.constantExpression = function() {
    return this.getTypedRuleContext(ConstantExpressionContext,0);
};

SwitchLabelContext.prototype.enumConstantName = function() {
    return this.getTypedRuleContext(EnumConstantNameContext,0);
};

SwitchLabelContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterSwitchLabel(this);
	}
};

SwitchLabelContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitSwitchLabel(this);
	}
};




JavaParser.SwitchLabelContext = SwitchLabelContext;

JavaParser.prototype.switchLabel = function() {

    var localctx = new SwitchLabelContext(this, this._ctx, this.state);
    this.enterRule(localctx, 156, JavaParser.RULE_switchLabel);
    try {
        this.state = 998;
        var la_ = this._interp.adaptivePredict(this._input,115,this._ctx);
        switch(la_) {
        case 1:
            this.enterOuterAlt(localctx, 1);
            this.state = 988;
            this.match(JavaParser.CASE);
            this.state = 989;
            this.constantExpression();
            this.state = 990;
            this.match(JavaParser.COLON);
            break;

        case 2:
            this.enterOuterAlt(localctx, 2);
            this.state = 992;
            this.match(JavaParser.CASE);
            this.state = 993;
            this.enumConstantName();
            this.state = 994;
            this.match(JavaParser.COLON);
            break;

        case 3:
            this.enterOuterAlt(localctx, 3);
            this.state = 996;
            this.match(JavaParser.DEFAULT);
            this.state = 997;
            this.match(JavaParser.COLON);
            break;

        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function ForControlContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_forControl;
    return this;
}

ForControlContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
ForControlContext.prototype.constructor = ForControlContext;

ForControlContext.prototype.enhancedForControl = function() {
    return this.getTypedRuleContext(EnhancedForControlContext,0);
};

ForControlContext.prototype.forInit = function() {
    return this.getTypedRuleContext(ForInitContext,0);
};

ForControlContext.prototype.expression = function() {
    return this.getTypedRuleContext(ExpressionContext,0);
};

ForControlContext.prototype.forUpdate = function() {
    return this.getTypedRuleContext(ForUpdateContext,0);
};

ForControlContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterForControl(this);
	}
};

ForControlContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitForControl(this);
	}
};




JavaParser.ForControlContext = ForControlContext;

JavaParser.prototype.forControl = function() {

    var localctx = new ForControlContext(this, this._ctx, this.state);
    this.enterRule(localctx, 158, JavaParser.RULE_forControl);
    var _la = 0; // Token type
    try {
        this.state = 1012;
        var la_ = this._interp.adaptivePredict(this._input,119,this._ctx);
        switch(la_) {
        case 1:
            this.enterOuterAlt(localctx, 1);
            this.state = 1000;
            this.enhancedForControl();
            break;

        case 2:
            this.enterOuterAlt(localctx, 2);
            this.state = 1002;
            _la = this._input.LA(1);
            if((((_la) & ~0x1f) == 0 && ((1 << _la) & ((1 << JavaParser.BOOLEAN) | (1 << JavaParser.BYTE) | (1 << JavaParser.CHAR) | (1 << JavaParser.DOUBLE) | (1 << JavaParser.FINAL) | (1 << JavaParser.FLOAT) | (1 << JavaParser.INT) | (1 << JavaParser.LONG) | (1 << JavaParser.NEW))) !== 0) || ((((_la - 37)) & ~0x1f) == 0 && ((1 << (_la - 37)) & ((1 << (JavaParser.SHORT - 37)) | (1 << (JavaParser.SUPER - 37)) | (1 << (JavaParser.THIS - 37)) | (1 << (JavaParser.VOID - 37)) | (1 << (JavaParser.IntegerLiteral - 37)) | (1 << (JavaParser.FloatingPointLiteral - 37)) | (1 << (JavaParser.BooleanLiteral - 37)) | (1 << (JavaParser.CharacterLiteral - 37)) | (1 << (JavaParser.StringLiteral - 37)) | (1 << (JavaParser.NullLiteral - 37)) | (1 << (JavaParser.LPAREN - 37)) | (1 << (JavaParser.LT - 37)))) !== 0) || ((((_la - 69)) & ~0x1f) == 0 && ((1 << (_la - 69)) & ((1 << (JavaParser.BANG - 69)) | (1 << (JavaParser.TILDE - 69)) | (1 << (JavaParser.INC - 69)) | (1 << (JavaParser.DEC - 69)) | (1 << (JavaParser.ADD - 69)) | (1 << (JavaParser.SUB - 69)) | (1 << (JavaParser.Identifier - 69)))) !== 0) || _la===JavaParser.AT) {
                this.state = 1001;
                this.forInit();
            }

            this.state = 1004;
            this.match(JavaParser.SEMI);
            this.state = 1006;
            _la = this._input.LA(1);
            if((((_la) & ~0x1f) == 0 && ((1 << _la) & ((1 << JavaParser.BOOLEAN) | (1 << JavaParser.BYTE) | (1 << JavaParser.CHAR) | (1 << JavaParser.DOUBLE) | (1 << JavaParser.FLOAT) | (1 << JavaParser.INT) | (1 << JavaParser.LONG) | (1 << JavaParser.NEW))) !== 0) || ((((_la - 37)) & ~0x1f) == 0 && ((1 << (_la - 37)) & ((1 << (JavaParser.SHORT - 37)) | (1 << (JavaParser.SUPER - 37)) | (1 << (JavaParser.THIS - 37)) | (1 << (JavaParser.VOID - 37)) | (1 << (JavaParser.IntegerLiteral - 37)) | (1 << (JavaParser.FloatingPointLiteral - 37)) | (1 << (JavaParser.BooleanLiteral - 37)) | (1 << (JavaParser.CharacterLiteral - 37)) | (1 << (JavaParser.StringLiteral - 37)) | (1 << (JavaParser.NullLiteral - 37)) | (1 << (JavaParser.LPAREN - 37)) | (1 << (JavaParser.LT - 37)))) !== 0) || ((((_la - 69)) & ~0x1f) == 0 && ((1 << (_la - 69)) & ((1 << (JavaParser.BANG - 69)) | (1 << (JavaParser.TILDE - 69)) | (1 << (JavaParser.INC - 69)) | (1 << (JavaParser.DEC - 69)) | (1 << (JavaParser.ADD - 69)) | (1 << (JavaParser.SUB - 69)) | (1 << (JavaParser.Identifier - 69)))) !== 0)) {
                this.state = 1005;
                this.expression(0);
            }

            this.state = 1008;
            this.match(JavaParser.SEMI);
            this.state = 1010;
            _la = this._input.LA(1);
            if((((_la) & ~0x1f) == 0 && ((1 << _la) & ((1 << JavaParser.BOOLEAN) | (1 << JavaParser.BYTE) | (1 << JavaParser.CHAR) | (1 << JavaParser.DOUBLE) | (1 << JavaParser.FLOAT) | (1 << JavaParser.INT) | (1 << JavaParser.LONG) | (1 << JavaParser.NEW))) !== 0) || ((((_la - 37)) & ~0x1f) == 0 && ((1 << (_la - 37)) & ((1 << (JavaParser.SHORT - 37)) | (1 << (JavaParser.SUPER - 37)) | (1 << (JavaParser.THIS - 37)) | (1 << (JavaParser.VOID - 37)) | (1 << (JavaParser.IntegerLiteral - 37)) | (1 << (JavaParser.FloatingPointLiteral - 37)) | (1 << (JavaParser.BooleanLiteral - 37)) | (1 << (JavaParser.CharacterLiteral - 37)) | (1 << (JavaParser.StringLiteral - 37)) | (1 << (JavaParser.NullLiteral - 37)) | (1 << (JavaParser.LPAREN - 37)) | (1 << (JavaParser.LT - 37)))) !== 0) || ((((_la - 69)) & ~0x1f) == 0 && ((1 << (_la - 69)) & ((1 << (JavaParser.BANG - 69)) | (1 << (JavaParser.TILDE - 69)) | (1 << (JavaParser.INC - 69)) | (1 << (JavaParser.DEC - 69)) | (1 << (JavaParser.ADD - 69)) | (1 << (JavaParser.SUB - 69)) | (1 << (JavaParser.Identifier - 69)))) !== 0)) {
                this.state = 1009;
                this.forUpdate();
            }

            break;

        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function ForInitContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_forInit;
    return this;
}

ForInitContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
ForInitContext.prototype.constructor = ForInitContext;

ForInitContext.prototype.localVariableDeclaration = function() {
    return this.getTypedRuleContext(LocalVariableDeclarationContext,0);
};

ForInitContext.prototype.expressionList = function() {
    return this.getTypedRuleContext(ExpressionListContext,0);
};

ForInitContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterForInit(this);
	}
};

ForInitContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitForInit(this);
	}
};




JavaParser.ForInitContext = ForInitContext;

JavaParser.prototype.forInit = function() {

    var localctx = new ForInitContext(this, this._ctx, this.state);
    this.enterRule(localctx, 160, JavaParser.RULE_forInit);
    try {
        this.state = 1016;
        var la_ = this._interp.adaptivePredict(this._input,120,this._ctx);
        switch(la_) {
        case 1:
            this.enterOuterAlt(localctx, 1);
            this.state = 1014;
            this.localVariableDeclaration();
            break;

        case 2:
            this.enterOuterAlt(localctx, 2);
            this.state = 1015;
            this.expressionList();
            break;

        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function EnhancedForControlContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_enhancedForControl;
    return this;
}

EnhancedForControlContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
EnhancedForControlContext.prototype.constructor = EnhancedForControlContext;

EnhancedForControlContext.prototype.type = function() {
    return this.getTypedRuleContext(TypeContext,0);
};

EnhancedForControlContext.prototype.variableDeclaratorId = function() {
    return this.getTypedRuleContext(VariableDeclaratorIdContext,0);
};

EnhancedForControlContext.prototype.expression = function() {
    return this.getTypedRuleContext(ExpressionContext,0);
};

EnhancedForControlContext.prototype.variableModifier = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(VariableModifierContext);
    } else {
        return this.getTypedRuleContext(VariableModifierContext,i);
    }
};

EnhancedForControlContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterEnhancedForControl(this);
	}
};

EnhancedForControlContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitEnhancedForControl(this);
	}
};




JavaParser.EnhancedForControlContext = EnhancedForControlContext;

JavaParser.prototype.enhancedForControl = function() {

    var localctx = new EnhancedForControlContext(this, this._ctx, this.state);
    this.enterRule(localctx, 162, JavaParser.RULE_enhancedForControl);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 1021;
        this._errHandler.sync(this);
        _la = this._input.LA(1);
        while(_la===JavaParser.FINAL || _la===JavaParser.AT) {
            this.state = 1018;
            this.variableModifier();
            this.state = 1023;
            this._errHandler.sync(this);
            _la = this._input.LA(1);
        }
        this.state = 1024;
        this.type();
        this.state = 1025;
        this.variableDeclaratorId();
        this.state = 1026;
        this.match(JavaParser.COLON);
        this.state = 1027;
        this.expression(0);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function ForUpdateContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_forUpdate;
    return this;
}

ForUpdateContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
ForUpdateContext.prototype.constructor = ForUpdateContext;

ForUpdateContext.prototype.expressionList = function() {
    return this.getTypedRuleContext(ExpressionListContext,0);
};

ForUpdateContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterForUpdate(this);
	}
};

ForUpdateContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitForUpdate(this);
	}
};




JavaParser.ForUpdateContext = ForUpdateContext;

JavaParser.prototype.forUpdate = function() {

    var localctx = new ForUpdateContext(this, this._ctx, this.state);
    this.enterRule(localctx, 164, JavaParser.RULE_forUpdate);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 1029;
        this.expressionList();
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function ParExpressionContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_parExpression;
    return this;
}

ParExpressionContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
ParExpressionContext.prototype.constructor = ParExpressionContext;

ParExpressionContext.prototype.expression = function() {
    return this.getTypedRuleContext(ExpressionContext,0);
};

ParExpressionContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterParExpression(this);
	}
};

ParExpressionContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitParExpression(this);
	}
};




JavaParser.ParExpressionContext = ParExpressionContext;

JavaParser.prototype.parExpression = function() {

    var localctx = new ParExpressionContext(this, this._ctx, this.state);
    this.enterRule(localctx, 166, JavaParser.RULE_parExpression);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 1031;
        this.match(JavaParser.LPAREN);
        this.state = 1032;
        this.expression(0);
        this.state = 1033;
        this.match(JavaParser.RPAREN);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function ExpressionListContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_expressionList;
    return this;
}

ExpressionListContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
ExpressionListContext.prototype.constructor = ExpressionListContext;

ExpressionListContext.prototype.expression = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(ExpressionContext);
    } else {
        return this.getTypedRuleContext(ExpressionContext,i);
    }
};

ExpressionListContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterExpressionList(this);
	}
};

ExpressionListContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitExpressionList(this);
	}
};




JavaParser.ExpressionListContext = ExpressionListContext;

JavaParser.prototype.expressionList = function() {

    var localctx = new ExpressionListContext(this, this._ctx, this.state);
    this.enterRule(localctx, 168, JavaParser.RULE_expressionList);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 1035;
        this.expression(0);
        this.state = 1040;
        this._errHandler.sync(this);
        _la = this._input.LA(1);
        while(_la===JavaParser.COMMA) {
            this.state = 1036;
            this.match(JavaParser.COMMA);
            this.state = 1037;
            this.expression(0);
            this.state = 1042;
            this._errHandler.sync(this);
            _la = this._input.LA(1);
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function StatementExpressionContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_statementExpression;
    return this;
}

StatementExpressionContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
StatementExpressionContext.prototype.constructor = StatementExpressionContext;

StatementExpressionContext.prototype.expression = function() {
    return this.getTypedRuleContext(ExpressionContext,0);
};

StatementExpressionContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterStatementExpression(this);
	}
};

StatementExpressionContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitStatementExpression(this);
	}
};




JavaParser.StatementExpressionContext = StatementExpressionContext;

JavaParser.prototype.statementExpression = function() {

    var localctx = new StatementExpressionContext(this, this._ctx, this.state);
    this.enterRule(localctx, 170, JavaParser.RULE_statementExpression);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 1043;
        this.expression(0);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function ConstantExpressionContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_constantExpression;
    return this;
}

ConstantExpressionContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
ConstantExpressionContext.prototype.constructor = ConstantExpressionContext;

ConstantExpressionContext.prototype.expression = function() {
    return this.getTypedRuleContext(ExpressionContext,0);
};

ConstantExpressionContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterConstantExpression(this);
	}
};

ConstantExpressionContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitConstantExpression(this);
	}
};




JavaParser.ConstantExpressionContext = ConstantExpressionContext;

JavaParser.prototype.constantExpression = function() {

    var localctx = new ConstantExpressionContext(this, this._ctx, this.state);
    this.enterRule(localctx, 172, JavaParser.RULE_constantExpression);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 1045;
        this.expression(0);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function ExpressionContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_expression;
    return this;
}

ExpressionContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
ExpressionContext.prototype.constructor = ExpressionContext;

ExpressionContext.prototype.type = function() {
    return this.getTypedRuleContext(TypeContext,0);
};

ExpressionContext.prototype.expression = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(ExpressionContext);
    } else {
        return this.getTypedRuleContext(ExpressionContext,i);
    }
};

ExpressionContext.prototype.primary = function() {
    return this.getTypedRuleContext(PrimaryContext,0);
};

ExpressionContext.prototype.creator = function() {
    return this.getTypedRuleContext(CreatorContext,0);
};

ExpressionContext.prototype.Identifier = function() {
    return this.getToken(JavaParser.Identifier, 0);
};

ExpressionContext.prototype.innerCreator = function() {
    return this.getTypedRuleContext(InnerCreatorContext,0);
};

ExpressionContext.prototype.nonWildcardTypeArguments = function() {
    return this.getTypedRuleContext(NonWildcardTypeArgumentsContext,0);
};

ExpressionContext.prototype.superSuffix = function() {
    return this.getTypedRuleContext(SuperSuffixContext,0);
};

ExpressionContext.prototype.explicitGenericInvocation = function() {
    return this.getTypedRuleContext(ExplicitGenericInvocationContext,0);
};

ExpressionContext.prototype.expressionList = function() {
    return this.getTypedRuleContext(ExpressionListContext,0);
};

ExpressionContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterExpression(this);
	}
};

ExpressionContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitExpression(this);
	}
};



JavaParser.prototype.expression = function(_p) {
	if(_p===undefined) {
	    _p = 0;
	}
    var _parentctx = this._ctx;
    var _parentState = this.state;
    var localctx = new ExpressionContext(this, this._ctx, _parentState);
    var _prevctx = localctx;
    var _startState = 174;
    this.enterRecursionRule(localctx, 174, JavaParser.RULE_expression, _p);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 1060;
        var la_ = this._interp.adaptivePredict(this._input,123,this._ctx);
        switch(la_) {
        case 1:
            this.state = 1048;
            this.match(JavaParser.LPAREN);
            this.state = 1049;
            this.type();
            this.state = 1050;
            this.match(JavaParser.RPAREN);
            this.state = 1051;
            this.expression(17);
            break;

        case 2:
            this.state = 1053;
            _la = this._input.LA(1);
            if(!(((((_la - 79)) & ~0x1f) == 0 && ((1 << (_la - 79)) & ((1 << (JavaParser.INC - 79)) | (1 << (JavaParser.DEC - 79)) | (1 << (JavaParser.ADD - 79)) | (1 << (JavaParser.SUB - 79)))) !== 0))) {
            this._errHandler.recoverInline(this);
            }
            else {
                this.consume();
            }
            this.state = 1054;
            this.expression(15);
            break;

        case 3:
            this.state = 1055;
            _la = this._input.LA(1);
            if(!(_la===JavaParser.BANG || _la===JavaParser.TILDE)) {
            this._errHandler.recoverInline(this);
            }
            else {
                this.consume();
            }
            this.state = 1056;
            this.expression(14);
            break;

        case 4:
            this.state = 1057;
            this.primary();
            break;

        case 5:
            this.state = 1058;
            this.match(JavaParser.NEW);
            this.state = 1059;
            this.creator();
            break;

        }
        this._ctx.stop = this._input.LT(-1);
        this.state = 1147;
        this._errHandler.sync(this);
        var _alt = this._interp.adaptivePredict(this._input,128,this._ctx)
        while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
            if(_alt===1) {
                if(this._parseListeners!==null) {
                    this.triggerExitRuleEvent();
                }
                _prevctx = localctx;
                this.state = 1145;
                var la_ = this._interp.adaptivePredict(this._input,127,this._ctx);
                switch(la_) {
                case 1:
                    localctx = new ExpressionContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, JavaParser.RULE_expression);
                    this.state = 1062;
                    if (!( this.precpred(this._ctx, 13))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 13)");
                    }
                    this.state = 1063;
                    _la = this._input.LA(1);
                    if(!(((((_la - 83)) & ~0x1f) == 0 && ((1 << (_la - 83)) & ((1 << (JavaParser.MUL - 83)) | (1 << (JavaParser.DIV - 83)) | (1 << (JavaParser.MOD - 83)))) !== 0))) {
                    this._errHandler.recoverInline(this);
                    }
                    else {
                        this.consume();
                    }
                    this.state = 1064;
                    this.expression(14);
                    break;

                case 2:
                    localctx = new ExpressionContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, JavaParser.RULE_expression);
                    this.state = 1065;
                    if (!( this.precpred(this._ctx, 12))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 12)");
                    }
                    this.state = 1066;
                    _la = this._input.LA(1);
                    if(!(_la===JavaParser.ADD || _la===JavaParser.SUB)) {
                    this._errHandler.recoverInline(this);
                    }
                    else {
                        this.consume();
                    }
                    this.state = 1067;
                    this.expression(13);
                    break;

                case 3:
                    localctx = new ExpressionContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, JavaParser.RULE_expression);
                    this.state = 1068;
                    if (!( this.precpred(this._ctx, 11))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 11)");
                    }
                    this.state = 1076;
                    var la_ = this._interp.adaptivePredict(this._input,124,this._ctx);
                    switch(la_) {
                    case 1:
                        this.state = 1069;
                        this.match(JavaParser.LT);
                        this.state = 1070;
                        this.match(JavaParser.LT);
                        break;

                    case 2:
                        this.state = 1071;
                        this.match(JavaParser.GT);
                        this.state = 1072;
                        this.match(JavaParser.GT);
                        this.state = 1073;
                        this.match(JavaParser.GT);
                        break;

                    case 3:
                        this.state = 1074;
                        this.match(JavaParser.GT);
                        this.state = 1075;
                        this.match(JavaParser.GT);
                        break;

                    }
                    this.state = 1078;
                    this.expression(12);
                    break;

                case 4:
                    localctx = new ExpressionContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, JavaParser.RULE_expression);
                    this.state = 1079;
                    if (!( this.precpred(this._ctx, 10))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 10)");
                    }
                    this.state = 1080;
                    _la = this._input.LA(1);
                    if(!(((((_la - 67)) & ~0x1f) == 0 && ((1 << (_la - 67)) & ((1 << (JavaParser.GT - 67)) | (1 << (JavaParser.LT - 67)) | (1 << (JavaParser.LE - 67)) | (1 << (JavaParser.GE - 67)))) !== 0))) {
                    this._errHandler.recoverInline(this);
                    }
                    else {
                        this.consume();
                    }
                    this.state = 1081;
                    this.expression(11);
                    break;

                case 5:
                    localctx = new ExpressionContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, JavaParser.RULE_expression);
                    this.state = 1082;
                    if (!( this.precpred(this._ctx, 8))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 8)");
                    }
                    this.state = 1083;
                    _la = this._input.LA(1);
                    if(!(_la===JavaParser.EQUAL || _la===JavaParser.NOTEQUAL)) {
                    this._errHandler.recoverInline(this);
                    }
                    else {
                        this.consume();
                    }
                    this.state = 1084;
                    this.expression(9);
                    break;

                case 6:
                    localctx = new ExpressionContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, JavaParser.RULE_expression);
                    this.state = 1085;
                    if (!( this.precpred(this._ctx, 7))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 7)");
                    }
                    this.state = 1086;
                    this.match(JavaParser.BITAND);
                    this.state = 1087;
                    this.expression(8);
                    break;

                case 7:
                    localctx = new ExpressionContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, JavaParser.RULE_expression);
                    this.state = 1088;
                    if (!( this.precpred(this._ctx, 6))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 6)");
                    }
                    this.state = 1089;
                    this.match(JavaParser.CARET);
                    this.state = 1090;
                    this.expression(7);
                    break;

                case 8:
                    localctx = new ExpressionContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, JavaParser.RULE_expression);
                    this.state = 1091;
                    if (!( this.precpred(this._ctx, 5))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 5)");
                    }
                    this.state = 1092;
                    this.match(JavaParser.BITOR);
                    this.state = 1093;
                    this.expression(6);
                    break;

                case 9:
                    localctx = new ExpressionContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, JavaParser.RULE_expression);
                    this.state = 1094;
                    if (!( this.precpred(this._ctx, 4))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 4)");
                    }
                    this.state = 1095;
                    this.match(JavaParser.AND);
                    this.state = 1096;
                    this.expression(5);
                    break;

                case 10:
                    localctx = new ExpressionContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, JavaParser.RULE_expression);
                    this.state = 1097;
                    if (!( this.precpred(this._ctx, 3))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 3)");
                    }
                    this.state = 1098;
                    this.match(JavaParser.OR);
                    this.state = 1099;
                    this.expression(4);
                    break;

                case 11:
                    localctx = new ExpressionContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, JavaParser.RULE_expression);
                    this.state = 1100;
                    if (!( this.precpred(this._ctx, 2))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 2)");
                    }
                    this.state = 1101;
                    this.match(JavaParser.QUESTION);
                    this.state = 1102;
                    this.expression(0);
                    this.state = 1103;
                    this.match(JavaParser.COLON);
                    this.state = 1104;
                    this.expression(3);
                    break;

                case 12:
                    localctx = new ExpressionContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, JavaParser.RULE_expression);
                    this.state = 1106;
                    if (!( this.precpred(this._ctx, 1))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 1)");
                    }
                    this.state = 1107;
                    _la = this._input.LA(1);
                    if(!(((((_la - 66)) & ~0x1f) == 0 && ((1 << (_la - 66)) & ((1 << (JavaParser.ASSIGN - 66)) | (1 << (JavaParser.ADD_ASSIGN - 66)) | (1 << (JavaParser.SUB_ASSIGN - 66)) | (1 << (JavaParser.MUL_ASSIGN - 66)) | (1 << (JavaParser.DIV_ASSIGN - 66)) | (1 << (JavaParser.AND_ASSIGN - 66)) | (1 << (JavaParser.OR_ASSIGN - 66)) | (1 << (JavaParser.XOR_ASSIGN - 66)) | (1 << (JavaParser.MOD_ASSIGN - 66)) | (1 << (JavaParser.LSHIFT_ASSIGN - 66)))) !== 0) || _la===JavaParser.RSHIFT_ASSIGN || _la===JavaParser.URSHIFT_ASSIGN)) {
                    this._errHandler.recoverInline(this);
                    }
                    else {
                        this.consume();
                    }
                    this.state = 1108;
                    this.expression(1);
                    break;

                case 13:
                    localctx = new ExpressionContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, JavaParser.RULE_expression);
                    this.state = 1109;
                    if (!( this.precpred(this._ctx, 25))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 25)");
                    }
                    this.state = 1110;
                    this.match(JavaParser.DOT);
                    this.state = 1111;
                    this.match(JavaParser.Identifier);
                    break;

                case 14:
                    localctx = new ExpressionContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, JavaParser.RULE_expression);
                    this.state = 1112;
                    if (!( this.precpred(this._ctx, 24))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 24)");
                    }
                    this.state = 1113;
                    this.match(JavaParser.DOT);
                    this.state = 1114;
                    this.match(JavaParser.THIS);
                    break;

                case 15:
                    localctx = new ExpressionContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, JavaParser.RULE_expression);
                    this.state = 1115;
                    if (!( this.precpred(this._ctx, 23))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 23)");
                    }
                    this.state = 1116;
                    this.match(JavaParser.DOT);
                    this.state = 1117;
                    this.match(JavaParser.NEW);
                    this.state = 1119;
                    _la = this._input.LA(1);
                    if(_la===JavaParser.LT) {
                        this.state = 1118;
                        this.nonWildcardTypeArguments();
                    }

                    this.state = 1121;
                    this.innerCreator();
                    break;

                case 16:
                    localctx = new ExpressionContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, JavaParser.RULE_expression);
                    this.state = 1122;
                    if (!( this.precpred(this._ctx, 22))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 22)");
                    }
                    this.state = 1123;
                    this.match(JavaParser.DOT);
                    this.state = 1124;
                    this.match(JavaParser.SUPER);
                    this.state = 1125;
                    this.superSuffix();
                    break;

                case 17:
                    localctx = new ExpressionContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, JavaParser.RULE_expression);
                    this.state = 1126;
                    if (!( this.precpred(this._ctx, 21))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 21)");
                    }
                    this.state = 1127;
                    this.match(JavaParser.DOT);
                    this.state = 1128;
                    this.explicitGenericInvocation();
                    break;

                case 18:
                    localctx = new ExpressionContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, JavaParser.RULE_expression);
                    this.state = 1129;
                    if (!( this.precpred(this._ctx, 20))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 20)");
                    }
                    this.state = 1130;
                    this.match(JavaParser.LBRACK);
                    this.state = 1131;
                    this.expression(0);
                    this.state = 1132;
                    this.match(JavaParser.RBRACK);
                    break;

                case 19:
                    localctx = new ExpressionContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, JavaParser.RULE_expression);
                    this.state = 1134;
                    if (!( this.precpred(this._ctx, 19))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 19)");
                    }
                    this.state = 1135;
                    this.match(JavaParser.LPAREN);
                    this.state = 1137;
                    _la = this._input.LA(1);
                    if((((_la) & ~0x1f) == 0 && ((1 << _la) & ((1 << JavaParser.BOOLEAN) | (1 << JavaParser.BYTE) | (1 << JavaParser.CHAR) | (1 << JavaParser.DOUBLE) | (1 << JavaParser.FLOAT) | (1 << JavaParser.INT) | (1 << JavaParser.LONG) | (1 << JavaParser.NEW))) !== 0) || ((((_la - 37)) & ~0x1f) == 0 && ((1 << (_la - 37)) & ((1 << (JavaParser.SHORT - 37)) | (1 << (JavaParser.SUPER - 37)) | (1 << (JavaParser.THIS - 37)) | (1 << (JavaParser.VOID - 37)) | (1 << (JavaParser.IntegerLiteral - 37)) | (1 << (JavaParser.FloatingPointLiteral - 37)) | (1 << (JavaParser.BooleanLiteral - 37)) | (1 << (JavaParser.CharacterLiteral - 37)) | (1 << (JavaParser.StringLiteral - 37)) | (1 << (JavaParser.NullLiteral - 37)) | (1 << (JavaParser.LPAREN - 37)) | (1 << (JavaParser.LT - 37)))) !== 0) || ((((_la - 69)) & ~0x1f) == 0 && ((1 << (_la - 69)) & ((1 << (JavaParser.BANG - 69)) | (1 << (JavaParser.TILDE - 69)) | (1 << (JavaParser.INC - 69)) | (1 << (JavaParser.DEC - 69)) | (1 << (JavaParser.ADD - 69)) | (1 << (JavaParser.SUB - 69)) | (1 << (JavaParser.Identifier - 69)))) !== 0)) {
                        this.state = 1136;
                        this.expressionList();
                    }

                    this.state = 1139;
                    this.match(JavaParser.RPAREN);
                    break;

                case 20:
                    localctx = new ExpressionContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, JavaParser.RULE_expression);
                    this.state = 1140;
                    if (!( this.precpred(this._ctx, 16))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 16)");
                    }
                    this.state = 1141;
                    _la = this._input.LA(1);
                    if(!(_la===JavaParser.INC || _la===JavaParser.DEC)) {
                    this._errHandler.recoverInline(this);
                    }
                    else {
                        this.consume();
                    }
                    break;

                case 21:
                    localctx = new ExpressionContext(this, _parentctx, _parentState);
                    this.pushNewRecursionContext(localctx, _startState, JavaParser.RULE_expression);
                    this.state = 1142;
                    if (!( this.precpred(this._ctx, 9))) {
                        throw new antlr4.error.FailedPredicateException(this, "this.precpred(this._ctx, 9)");
                    }
                    this.state = 1143;
                    this.match(JavaParser.INSTANCEOF);
                    this.state = 1144;
                    this.type();
                    break;

                } 
            }
            this.state = 1149;
            this._errHandler.sync(this);
            _alt = this._interp.adaptivePredict(this._input,128,this._ctx);
        }

    } catch( error) {
        if(error instanceof antlr4.error.RecognitionException) {
	        localctx.exception = error;
	        this._errHandler.reportError(this, error);
	        this._errHandler.recover(this, error);
	    } else {
	    	throw error;
	    }
    } finally {
        this.unrollRecursionContexts(_parentctx)
    }
    return localctx;
};

function PrimaryContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_primary;
    return this;
}

PrimaryContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
PrimaryContext.prototype.constructor = PrimaryContext;

PrimaryContext.prototype.expression = function() {
    return this.getTypedRuleContext(ExpressionContext,0);
};

PrimaryContext.prototype.literal = function() {
    return this.getTypedRuleContext(LiteralContext,0);
};

PrimaryContext.prototype.Identifier = function() {
    return this.getToken(JavaParser.Identifier, 0);
};

PrimaryContext.prototype.type = function() {
    return this.getTypedRuleContext(TypeContext,0);
};

PrimaryContext.prototype.nonWildcardTypeArguments = function() {
    return this.getTypedRuleContext(NonWildcardTypeArgumentsContext,0);
};

PrimaryContext.prototype.explicitGenericInvocationSuffix = function() {
    return this.getTypedRuleContext(ExplicitGenericInvocationSuffixContext,0);
};

PrimaryContext.prototype.arguments = function() {
    return this.getTypedRuleContext(ArgumentsContext,0);
};

PrimaryContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterPrimary(this);
	}
};

PrimaryContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitPrimary(this);
	}
};




JavaParser.PrimaryContext = PrimaryContext;

JavaParser.prototype.primary = function() {

    var localctx = new PrimaryContext(this, this._ctx, this.state);
    this.enterRule(localctx, 176, JavaParser.RULE_primary);
    try {
        this.state = 1171;
        var la_ = this._interp.adaptivePredict(this._input,130,this._ctx);
        switch(la_) {
        case 1:
            this.enterOuterAlt(localctx, 1);
            this.state = 1150;
            this.match(JavaParser.LPAREN);
            this.state = 1151;
            this.expression(0);
            this.state = 1152;
            this.match(JavaParser.RPAREN);
            break;

        case 2:
            this.enterOuterAlt(localctx, 2);
            this.state = 1154;
            this.match(JavaParser.THIS);
            break;

        case 3:
            this.enterOuterAlt(localctx, 3);
            this.state = 1155;
            this.match(JavaParser.SUPER);
            break;

        case 4:
            this.enterOuterAlt(localctx, 4);
            this.state = 1156;
            this.literal();
            break;

        case 5:
            this.enterOuterAlt(localctx, 5);
            this.state = 1157;
            this.match(JavaParser.Identifier);
            break;

        case 6:
            this.enterOuterAlt(localctx, 6);
            this.state = 1158;
            this.type();
            this.state = 1159;
            this.match(JavaParser.DOT);
            this.state = 1160;
            this.match(JavaParser.CLASS);
            break;

        case 7:
            this.enterOuterAlt(localctx, 7);
            this.state = 1162;
            this.match(JavaParser.VOID);
            this.state = 1163;
            this.match(JavaParser.DOT);
            this.state = 1164;
            this.match(JavaParser.CLASS);
            break;

        case 8:
            this.enterOuterAlt(localctx, 8);
            this.state = 1165;
            this.nonWildcardTypeArguments();
            this.state = 1169;
            switch(this._input.LA(1)) {
            case JavaParser.SUPER:
            case JavaParser.Identifier:
                this.state = 1166;
                this.explicitGenericInvocationSuffix();
                break;
            case JavaParser.THIS:
                this.state = 1167;
                this.match(JavaParser.THIS);
                this.state = 1168;
                this.arguments();
                break;
            default:
                throw new antlr4.error.NoViableAltException(this);
            }
            break;

        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function CreatorContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_creator;
    return this;
}

CreatorContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
CreatorContext.prototype.constructor = CreatorContext;

CreatorContext.prototype.nonWildcardTypeArguments = function() {
    return this.getTypedRuleContext(NonWildcardTypeArgumentsContext,0);
};

CreatorContext.prototype.createdName = function() {
    return this.getTypedRuleContext(CreatedNameContext,0);
};

CreatorContext.prototype.classCreatorRest = function() {
    return this.getTypedRuleContext(ClassCreatorRestContext,0);
};

CreatorContext.prototype.arrayCreatorRest = function() {
    return this.getTypedRuleContext(ArrayCreatorRestContext,0);
};

CreatorContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterCreator(this);
	}
};

CreatorContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitCreator(this);
	}
};




JavaParser.CreatorContext = CreatorContext;

JavaParser.prototype.creator = function() {

    var localctx = new CreatorContext(this, this._ctx, this.state);
    this.enterRule(localctx, 178, JavaParser.RULE_creator);
    try {
        this.state = 1182;
        switch(this._input.LA(1)) {
        case JavaParser.LT:
            this.enterOuterAlt(localctx, 1);
            this.state = 1173;
            this.nonWildcardTypeArguments();
            this.state = 1174;
            this.createdName();
            this.state = 1175;
            this.classCreatorRest();
            break;
        case JavaParser.BOOLEAN:
        case JavaParser.BYTE:
        case JavaParser.CHAR:
        case JavaParser.DOUBLE:
        case JavaParser.FLOAT:
        case JavaParser.INT:
        case JavaParser.LONG:
        case JavaParser.SHORT:
        case JavaParser.Identifier:
            this.enterOuterAlt(localctx, 2);
            this.state = 1177;
            this.createdName();
            this.state = 1180;
            switch(this._input.LA(1)) {
            case JavaParser.LBRACK:
                this.state = 1178;
                this.arrayCreatorRest();
                break;
            case JavaParser.LPAREN:
                this.state = 1179;
                this.classCreatorRest();
                break;
            default:
                throw new antlr4.error.NoViableAltException(this);
            }
            break;
        default:
            throw new antlr4.error.NoViableAltException(this);
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function CreatedNameContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_createdName;
    return this;
}

CreatedNameContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
CreatedNameContext.prototype.constructor = CreatedNameContext;

CreatedNameContext.prototype.Identifier = function(i) {
	if(i===undefined) {
		i = null;
	}
    if(i===null) {
        return this.getTokens(JavaParser.Identifier);
    } else {
        return this.getToken(JavaParser.Identifier, i);
    }
};


CreatedNameContext.prototype.typeArgumentsOrDiamond = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(TypeArgumentsOrDiamondContext);
    } else {
        return this.getTypedRuleContext(TypeArgumentsOrDiamondContext,i);
    }
};

CreatedNameContext.prototype.primitiveType = function() {
    return this.getTypedRuleContext(PrimitiveTypeContext,0);
};

CreatedNameContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterCreatedName(this);
	}
};

CreatedNameContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitCreatedName(this);
	}
};




JavaParser.CreatedNameContext = CreatedNameContext;

JavaParser.prototype.createdName = function() {

    var localctx = new CreatedNameContext(this, this._ctx, this.state);
    this.enterRule(localctx, 180, JavaParser.RULE_createdName);
    var _la = 0; // Token type
    try {
        this.state = 1199;
        switch(this._input.LA(1)) {
        case JavaParser.Identifier:
            this.enterOuterAlt(localctx, 1);
            this.state = 1184;
            this.match(JavaParser.Identifier);
            this.state = 1186;
            _la = this._input.LA(1);
            if(_la===JavaParser.LT) {
                this.state = 1185;
                this.typeArgumentsOrDiamond();
            }

            this.state = 1195;
            this._errHandler.sync(this);
            _la = this._input.LA(1);
            while(_la===JavaParser.DOT) {
                this.state = 1188;
                this.match(JavaParser.DOT);
                this.state = 1189;
                this.match(JavaParser.Identifier);
                this.state = 1191;
                _la = this._input.LA(1);
                if(_la===JavaParser.LT) {
                    this.state = 1190;
                    this.typeArgumentsOrDiamond();
                }

                this.state = 1197;
                this._errHandler.sync(this);
                _la = this._input.LA(1);
            }
            break;
        case JavaParser.BOOLEAN:
        case JavaParser.BYTE:
        case JavaParser.CHAR:
        case JavaParser.DOUBLE:
        case JavaParser.FLOAT:
        case JavaParser.INT:
        case JavaParser.LONG:
        case JavaParser.SHORT:
            this.enterOuterAlt(localctx, 2);
            this.state = 1198;
            this.primitiveType();
            break;
        default:
            throw new antlr4.error.NoViableAltException(this);
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function InnerCreatorContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_innerCreator;
    return this;
}

InnerCreatorContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
InnerCreatorContext.prototype.constructor = InnerCreatorContext;

InnerCreatorContext.prototype.Identifier = function() {
    return this.getToken(JavaParser.Identifier, 0);
};

InnerCreatorContext.prototype.classCreatorRest = function() {
    return this.getTypedRuleContext(ClassCreatorRestContext,0);
};

InnerCreatorContext.prototype.nonWildcardTypeArgumentsOrDiamond = function() {
    return this.getTypedRuleContext(NonWildcardTypeArgumentsOrDiamondContext,0);
};

InnerCreatorContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterInnerCreator(this);
	}
};

InnerCreatorContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitInnerCreator(this);
	}
};




JavaParser.InnerCreatorContext = InnerCreatorContext;

JavaParser.prototype.innerCreator = function() {

    var localctx = new InnerCreatorContext(this, this._ctx, this.state);
    this.enterRule(localctx, 182, JavaParser.RULE_innerCreator);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 1201;
        this.match(JavaParser.Identifier);
        this.state = 1203;
        _la = this._input.LA(1);
        if(_la===JavaParser.LT) {
            this.state = 1202;
            this.nonWildcardTypeArgumentsOrDiamond();
        }

        this.state = 1205;
        this.classCreatorRest();
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function ArrayCreatorRestContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_arrayCreatorRest;
    return this;
}

ArrayCreatorRestContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
ArrayCreatorRestContext.prototype.constructor = ArrayCreatorRestContext;

ArrayCreatorRestContext.prototype.arrayInitializer = function() {
    return this.getTypedRuleContext(ArrayInitializerContext,0);
};

ArrayCreatorRestContext.prototype.expression = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(ExpressionContext);
    } else {
        return this.getTypedRuleContext(ExpressionContext,i);
    }
};

ArrayCreatorRestContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterArrayCreatorRest(this);
	}
};

ArrayCreatorRestContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitArrayCreatorRest(this);
	}
};




JavaParser.ArrayCreatorRestContext = ArrayCreatorRestContext;

JavaParser.prototype.arrayCreatorRest = function() {

    var localctx = new ArrayCreatorRestContext(this, this._ctx, this.state);
    this.enterRule(localctx, 184, JavaParser.RULE_arrayCreatorRest);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 1207;
        this.match(JavaParser.LBRACK);
        this.state = 1235;
        switch(this._input.LA(1)) {
        case JavaParser.RBRACK:
            this.state = 1208;
            this.match(JavaParser.RBRACK);
            this.state = 1213;
            this._errHandler.sync(this);
            _la = this._input.LA(1);
            while(_la===JavaParser.LBRACK) {
                this.state = 1209;
                this.match(JavaParser.LBRACK);
                this.state = 1210;
                this.match(JavaParser.RBRACK);
                this.state = 1215;
                this._errHandler.sync(this);
                _la = this._input.LA(1);
            }
            this.state = 1216;
            this.arrayInitializer();
            break;
        case JavaParser.BOOLEAN:
        case JavaParser.BYTE:
        case JavaParser.CHAR:
        case JavaParser.DOUBLE:
        case JavaParser.FLOAT:
        case JavaParser.INT:
        case JavaParser.LONG:
        case JavaParser.NEW:
        case JavaParser.SHORT:
        case JavaParser.SUPER:
        case JavaParser.THIS:
        case JavaParser.VOID:
        case JavaParser.IntegerLiteral:
        case JavaParser.FloatingPointLiteral:
        case JavaParser.BooleanLiteral:
        case JavaParser.CharacterLiteral:
        case JavaParser.StringLiteral:
        case JavaParser.NullLiteral:
        case JavaParser.LPAREN:
        case JavaParser.LT:
        case JavaParser.BANG:
        case JavaParser.TILDE:
        case JavaParser.INC:
        case JavaParser.DEC:
        case JavaParser.ADD:
        case JavaParser.SUB:
        case JavaParser.Identifier:
            this.state = 1217;
            this.expression(0);
            this.state = 1218;
            this.match(JavaParser.RBRACK);
            this.state = 1225;
            this._errHandler.sync(this);
            var _alt = this._interp.adaptivePredict(this._input,139,this._ctx)
            while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
                if(_alt===1) {
                    this.state = 1219;
                    this.match(JavaParser.LBRACK);
                    this.state = 1220;
                    this.expression(0);
                    this.state = 1221;
                    this.match(JavaParser.RBRACK); 
                }
                this.state = 1227;
                this._errHandler.sync(this);
                _alt = this._interp.adaptivePredict(this._input,139,this._ctx);
            }

            this.state = 1232;
            this._errHandler.sync(this);
            var _alt = this._interp.adaptivePredict(this._input,140,this._ctx)
            while(_alt!=2 && _alt!=antlr4.atn.ATN.INVALID_ALT_NUMBER) {
                if(_alt===1) {
                    this.state = 1228;
                    this.match(JavaParser.LBRACK);
                    this.state = 1229;
                    this.match(JavaParser.RBRACK); 
                }
                this.state = 1234;
                this._errHandler.sync(this);
                _alt = this._interp.adaptivePredict(this._input,140,this._ctx);
            }

            break;
        default:
            throw new antlr4.error.NoViableAltException(this);
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function ClassCreatorRestContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_classCreatorRest;
    return this;
}

ClassCreatorRestContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
ClassCreatorRestContext.prototype.constructor = ClassCreatorRestContext;

ClassCreatorRestContext.prototype.arguments = function() {
    return this.getTypedRuleContext(ArgumentsContext,0);
};

ClassCreatorRestContext.prototype.classBody = function() {
    return this.getTypedRuleContext(ClassBodyContext,0);
};

ClassCreatorRestContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterClassCreatorRest(this);
	}
};

ClassCreatorRestContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitClassCreatorRest(this);
	}
};




JavaParser.ClassCreatorRestContext = ClassCreatorRestContext;

JavaParser.prototype.classCreatorRest = function() {

    var localctx = new ClassCreatorRestContext(this, this._ctx, this.state);
    this.enterRule(localctx, 186, JavaParser.RULE_classCreatorRest);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 1237;
        this.arguments();
        this.state = 1239;
        var la_ = this._interp.adaptivePredict(this._input,142,this._ctx);
        if(la_===1) {
            this.state = 1238;
            this.classBody();

        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function ExplicitGenericInvocationContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_explicitGenericInvocation;
    return this;
}

ExplicitGenericInvocationContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
ExplicitGenericInvocationContext.prototype.constructor = ExplicitGenericInvocationContext;

ExplicitGenericInvocationContext.prototype.nonWildcardTypeArguments = function() {
    return this.getTypedRuleContext(NonWildcardTypeArgumentsContext,0);
};

ExplicitGenericInvocationContext.prototype.explicitGenericInvocationSuffix = function() {
    return this.getTypedRuleContext(ExplicitGenericInvocationSuffixContext,0);
};

ExplicitGenericInvocationContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterExplicitGenericInvocation(this);
	}
};

ExplicitGenericInvocationContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitExplicitGenericInvocation(this);
	}
};




JavaParser.ExplicitGenericInvocationContext = ExplicitGenericInvocationContext;

JavaParser.prototype.explicitGenericInvocation = function() {

    var localctx = new ExplicitGenericInvocationContext(this, this._ctx, this.state);
    this.enterRule(localctx, 188, JavaParser.RULE_explicitGenericInvocation);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 1241;
        this.nonWildcardTypeArguments();
        this.state = 1242;
        this.explicitGenericInvocationSuffix();
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function NonWildcardTypeArgumentsContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_nonWildcardTypeArguments;
    return this;
}

NonWildcardTypeArgumentsContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
NonWildcardTypeArgumentsContext.prototype.constructor = NonWildcardTypeArgumentsContext;

NonWildcardTypeArgumentsContext.prototype.typeList = function() {
    return this.getTypedRuleContext(TypeListContext,0);
};

NonWildcardTypeArgumentsContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterNonWildcardTypeArguments(this);
	}
};

NonWildcardTypeArgumentsContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitNonWildcardTypeArguments(this);
	}
};




JavaParser.NonWildcardTypeArgumentsContext = NonWildcardTypeArgumentsContext;

JavaParser.prototype.nonWildcardTypeArguments = function() {

    var localctx = new NonWildcardTypeArgumentsContext(this, this._ctx, this.state);
    this.enterRule(localctx, 190, JavaParser.RULE_nonWildcardTypeArguments);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 1244;
        this.match(JavaParser.LT);
        this.state = 1245;
        this.typeList();
        this.state = 1246;
        this.match(JavaParser.GT);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function TypeArgumentsOrDiamondContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_typeArgumentsOrDiamond;
    return this;
}

TypeArgumentsOrDiamondContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
TypeArgumentsOrDiamondContext.prototype.constructor = TypeArgumentsOrDiamondContext;

TypeArgumentsOrDiamondContext.prototype.typeArguments = function() {
    return this.getTypedRuleContext(TypeArgumentsContext,0);
};

TypeArgumentsOrDiamondContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterTypeArgumentsOrDiamond(this);
	}
};

TypeArgumentsOrDiamondContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitTypeArgumentsOrDiamond(this);
	}
};




JavaParser.TypeArgumentsOrDiamondContext = TypeArgumentsOrDiamondContext;

JavaParser.prototype.typeArgumentsOrDiamond = function() {

    var localctx = new TypeArgumentsOrDiamondContext(this, this._ctx, this.state);
    this.enterRule(localctx, 192, JavaParser.RULE_typeArgumentsOrDiamond);
    try {
        this.state = 1251;
        var la_ = this._interp.adaptivePredict(this._input,143,this._ctx);
        switch(la_) {
        case 1:
            this.enterOuterAlt(localctx, 1);
            this.state = 1248;
            this.match(JavaParser.LT);
            this.state = 1249;
            this.match(JavaParser.GT);
            break;

        case 2:
            this.enterOuterAlt(localctx, 2);
            this.state = 1250;
            this.typeArguments();
            break;

        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function NonWildcardTypeArgumentsOrDiamondContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_nonWildcardTypeArgumentsOrDiamond;
    return this;
}

NonWildcardTypeArgumentsOrDiamondContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
NonWildcardTypeArgumentsOrDiamondContext.prototype.constructor = NonWildcardTypeArgumentsOrDiamondContext;

NonWildcardTypeArgumentsOrDiamondContext.prototype.nonWildcardTypeArguments = function() {
    return this.getTypedRuleContext(NonWildcardTypeArgumentsContext,0);
};

NonWildcardTypeArgumentsOrDiamondContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterNonWildcardTypeArgumentsOrDiamond(this);
	}
};

NonWildcardTypeArgumentsOrDiamondContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitNonWildcardTypeArgumentsOrDiamond(this);
	}
};




JavaParser.NonWildcardTypeArgumentsOrDiamondContext = NonWildcardTypeArgumentsOrDiamondContext;

JavaParser.prototype.nonWildcardTypeArgumentsOrDiamond = function() {

    var localctx = new NonWildcardTypeArgumentsOrDiamondContext(this, this._ctx, this.state);
    this.enterRule(localctx, 194, JavaParser.RULE_nonWildcardTypeArgumentsOrDiamond);
    try {
        this.state = 1256;
        var la_ = this._interp.adaptivePredict(this._input,144,this._ctx);
        switch(la_) {
        case 1:
            this.enterOuterAlt(localctx, 1);
            this.state = 1253;
            this.match(JavaParser.LT);
            this.state = 1254;
            this.match(JavaParser.GT);
            break;

        case 2:
            this.enterOuterAlt(localctx, 2);
            this.state = 1255;
            this.nonWildcardTypeArguments();
            break;

        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function SuperSuffixContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_superSuffix;
    return this;
}

SuperSuffixContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
SuperSuffixContext.prototype.constructor = SuperSuffixContext;

SuperSuffixContext.prototype.arguments = function() {
    return this.getTypedRuleContext(ArgumentsContext,0);
};

SuperSuffixContext.prototype.Identifier = function() {
    return this.getToken(JavaParser.Identifier, 0);
};

SuperSuffixContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterSuperSuffix(this);
	}
};

SuperSuffixContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitSuperSuffix(this);
	}
};




JavaParser.SuperSuffixContext = SuperSuffixContext;

JavaParser.prototype.superSuffix = function() {

    var localctx = new SuperSuffixContext(this, this._ctx, this.state);
    this.enterRule(localctx, 196, JavaParser.RULE_superSuffix);
    try {
        this.state = 1264;
        switch(this._input.LA(1)) {
        case JavaParser.LPAREN:
            this.enterOuterAlt(localctx, 1);
            this.state = 1258;
            this.arguments();
            break;
        case JavaParser.DOT:
            this.enterOuterAlt(localctx, 2);
            this.state = 1259;
            this.match(JavaParser.DOT);
            this.state = 1260;
            this.match(JavaParser.Identifier);
            this.state = 1262;
            var la_ = this._interp.adaptivePredict(this._input,145,this._ctx);
            if(la_===1) {
                this.state = 1261;
                this.arguments();

            }
            break;
        default:
            throw new antlr4.error.NoViableAltException(this);
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function ExplicitGenericInvocationSuffixContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_explicitGenericInvocationSuffix;
    return this;
}

ExplicitGenericInvocationSuffixContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
ExplicitGenericInvocationSuffixContext.prototype.constructor = ExplicitGenericInvocationSuffixContext;

ExplicitGenericInvocationSuffixContext.prototype.superSuffix = function() {
    return this.getTypedRuleContext(SuperSuffixContext,0);
};

ExplicitGenericInvocationSuffixContext.prototype.Identifier = function() {
    return this.getToken(JavaParser.Identifier, 0);
};

ExplicitGenericInvocationSuffixContext.prototype.arguments = function() {
    return this.getTypedRuleContext(ArgumentsContext,0);
};

ExplicitGenericInvocationSuffixContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterExplicitGenericInvocationSuffix(this);
	}
};

ExplicitGenericInvocationSuffixContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitExplicitGenericInvocationSuffix(this);
	}
};




JavaParser.ExplicitGenericInvocationSuffixContext = ExplicitGenericInvocationSuffixContext;

JavaParser.prototype.explicitGenericInvocationSuffix = function() {

    var localctx = new ExplicitGenericInvocationSuffixContext(this, this._ctx, this.state);
    this.enterRule(localctx, 198, JavaParser.RULE_explicitGenericInvocationSuffix);
    try {
        this.state = 1270;
        switch(this._input.LA(1)) {
        case JavaParser.SUPER:
            this.enterOuterAlt(localctx, 1);
            this.state = 1266;
            this.match(JavaParser.SUPER);
            this.state = 1267;
            this.superSuffix();
            break;
        case JavaParser.Identifier:
            this.enterOuterAlt(localctx, 2);
            this.state = 1268;
            this.match(JavaParser.Identifier);
            this.state = 1269;
            this.arguments();
            break;
        default:
            throw new antlr4.error.NoViableAltException(this);
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};

function ArgumentsContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = JavaParser.RULE_arguments;
    return this;
}

ArgumentsContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
ArgumentsContext.prototype.constructor = ArgumentsContext;

ArgumentsContext.prototype.expressionList = function() {
    return this.getTypedRuleContext(ExpressionListContext,0);
};

ArgumentsContext.prototype.enterRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.enterArguments(this);
	}
};

ArgumentsContext.prototype.exitRule = function(listener) {
    if(listener instanceof JavaListener ) {
        listener.exitArguments(this);
	}
};




JavaParser.ArgumentsContext = ArgumentsContext;

JavaParser.prototype.arguments = function() {

    var localctx = new ArgumentsContext(this, this._ctx, this.state);
    this.enterRule(localctx, 200, JavaParser.RULE_arguments);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 1272;
        this.match(JavaParser.LPAREN);
        this.state = 1274;
        _la = this._input.LA(1);
        if((((_la) & ~0x1f) == 0 && ((1 << _la) & ((1 << JavaParser.BOOLEAN) | (1 << JavaParser.BYTE) | (1 << JavaParser.CHAR) | (1 << JavaParser.DOUBLE) | (1 << JavaParser.FLOAT) | (1 << JavaParser.INT) | (1 << JavaParser.LONG) | (1 << JavaParser.NEW))) !== 0) || ((((_la - 37)) & ~0x1f) == 0 && ((1 << (_la - 37)) & ((1 << (JavaParser.SHORT - 37)) | (1 << (JavaParser.SUPER - 37)) | (1 << (JavaParser.THIS - 37)) | (1 << (JavaParser.VOID - 37)) | (1 << (JavaParser.IntegerLiteral - 37)) | (1 << (JavaParser.FloatingPointLiteral - 37)) | (1 << (JavaParser.BooleanLiteral - 37)) | (1 << (JavaParser.CharacterLiteral - 37)) | (1 << (JavaParser.StringLiteral - 37)) | (1 << (JavaParser.NullLiteral - 37)) | (1 << (JavaParser.LPAREN - 37)) | (1 << (JavaParser.LT - 37)))) !== 0) || ((((_la - 69)) & ~0x1f) == 0 && ((1 << (_la - 69)) & ((1 << (JavaParser.BANG - 69)) | (1 << (JavaParser.TILDE - 69)) | (1 << (JavaParser.INC - 69)) | (1 << (JavaParser.DEC - 69)) | (1 << (JavaParser.ADD - 69)) | (1 << (JavaParser.SUB - 69)) | (1 << (JavaParser.Identifier - 69)))) !== 0)) {
            this.state = 1273;
            this.expressionList();
        }

        this.state = 1276;
        this.match(JavaParser.RPAREN);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};


JavaParser.prototype.sempred = function(localctx, ruleIndex, predIndex) {
	switch(ruleIndex) {
	case 87:
			return this.expression_sempred(localctx, predIndex);
    default:
        throw "No predicate with index:" + ruleIndex;
   }
};

JavaParser.prototype.expression_sempred = function(localctx, predIndex) {
	switch(predIndex) {
		case 0:
			return this.precpred(this._ctx, 13);
		case 1:
			return this.precpred(this._ctx, 12);
		case 2:
			return this.precpred(this._ctx, 11);
		case 3:
			return this.precpred(this._ctx, 10);
		case 4:
			return this.precpred(this._ctx, 8);
		case 5:
			return this.precpred(this._ctx, 7);
		case 6:
			return this.precpred(this._ctx, 6);
		case 7:
			return this.precpred(this._ctx, 5);
		case 8:
			return this.precpred(this._ctx, 4);
		case 9:
			return this.precpred(this._ctx, 3);
		case 10:
			return this.precpred(this._ctx, 2);
		case 11:
			return this.precpred(this._ctx, 1);
		case 12:
			return this.precpred(this._ctx, 25);
		case 13:
			return this.precpred(this._ctx, 24);
		case 14:
			return this.precpred(this._ctx, 23);
		case 15:
			return this.precpred(this._ctx, 22);
		case 16:
			return this.precpred(this._ctx, 21);
		case 17:
			return this.precpred(this._ctx, 20);
		case 18:
			return this.precpred(this._ctx, 19);
		case 19:
			return this.precpred(this._ctx, 16);
		case 20:
			return this.precpred(this._ctx, 9);
		default:
			throw "No predicate with index:" + predIndex;
	}
};


exports.JavaParser = JavaParser;
