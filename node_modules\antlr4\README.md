# JavaScript target for ANTLR 4

[![npm version](https://img.shields.io/npm/v/antlr4)](https://www.npmjs.com/package/antlr4)
[![Badge showing the supported LTS versions of Node.JS in the latest NPM release](https://img.shields.io/node/v-lts/antlr4)](https://www.npmjs.com/package/antlr4)
[![npm type definitions](https://img.shields.io/npm/types/antlr4)](https://www.npmjs.com/package/antlr4)

JavaScript runtime libraries for ANTLR 4

This runtime is available through npm. The package name is 'antlr4'.

This runtime has been tested in Node.js, Safari, Firefox, Chrome and IE.

See www.antlr.org for more information on ANTLR

See [Javascript Target](https://github.com/antlr/antlr4/blob/master/doc/javascript-target.md)
for more information on using ANTLR in JavaScript

This runtime requires node version >= 16.

ANTLR 4 runtime is available in 10 target languages, and favors consistency of versioning across targets.
As such it cannot follow recommended NPM semantic versioning.
If you install a specific version of antlr4, we strongly recommend you remove the corresponding ^ in your package.json.

 





